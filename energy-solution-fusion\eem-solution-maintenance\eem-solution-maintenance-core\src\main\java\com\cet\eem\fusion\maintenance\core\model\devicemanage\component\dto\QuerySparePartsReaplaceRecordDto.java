package com.cet.eem.fusion.maintenance.core.model.devicemanage.component.dto;

import com.cet.eem.fusion.common.model.Page;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : QuerySparePartsReaplaceRecordDto
 * @Description : 查询备件消耗信息
 * <AUTHOR> 姜子旋
 * @Date: 2021-06-02 18:25
 */
@Data
public class QuerySparePartsReaplaceRecordDto {


    @NotNull(message = "分页信息不能为空")
    private Page page;

    /**
     * 设备类型id列表
     */
    private List<Long> objectLabelIds;
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private Long startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private Long endTime;

    /**
     * 备件名称 or 设备名称
     */
    private String keyWord;
    /**
     * 系统id列表
     */
    private List<Long> systemIds;
}

