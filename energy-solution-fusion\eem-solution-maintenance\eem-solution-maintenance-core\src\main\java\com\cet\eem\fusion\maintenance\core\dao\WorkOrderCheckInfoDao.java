package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.WorkOrderCheckInfo;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCheckInfoVo;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

/**
 * <AUTHOR>
 * @date 2021/5/27
 */
public interface WorkOrderCheckInfoDao extends BaseModelDao<WorkOrderCheckInfo> {
    /**
     * 查询最新一条工单确认信息
     *
     * @param code
     * @param taskId
     * @return
     */
    WorkOrderCheckInfoVo queryLastCheckInfo(String code, String taskId);
}


