# Create more missing classes
Write-Host "Creating more missing classes..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$utf8NoBom = New-Object System.Text.UTF8Encoding $false

# Create directory structure
$fusionCommonPath = "$coreSourcePath\com\cet\eem\fusion\common"
New-Item -ItemType Directory -Path "$fusionCommonPath\model\ext\subject\powermaintenance" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\model\base" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\def\common" -Force | Out-Null
New-Item -ItemType Directory -Path "$coreSourcePath\com\cet\electric\workflow\common\model\params" -Force | Out-Null
New-Item -ItemType Directory -Path "$coreSourcePath\com\cet\electric\workflow\common\constants" -Force | Out-Null

Write-Host "Creating extension model classes..." -ForegroundColor Cyan

# SignInPointWithSubLayer
$signInPointWithSubLayerContent = @"
package com.cet.eem.fusion.common.model.ext.subject.powermaintenance;

import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInPoint;
import java.util.List;

/**
 * Sign in point with sub layer extension
 */
public class SignInPointWithSubLayer extends SignInPoint {
    private List<SignInPointWithSubLayer> subLayers;
    private Integer level;
    private String parentId;
    
    // Getters and setters
    public List<SignInPointWithSubLayer> getSubLayers() { return subLayers; }
    public void setSubLayers(List<SignInPointWithSubLayer> subLayers) { this.subLayers = subLayers; }
    
    public Integer getLevel() { return level; }
    public void setLevel(Integer level) { this.level = level; }
    
    public String getParentId() { return parentId; }
    public void setParentId(String parentId) { this.parentId = parentId; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\ext\subject\powermaintenance\SignInPointWithSubLayer.java", $signInPointWithSubLayerContent, $utf8NoBom)

# PlanSheetWithSubLayer
$planSheetWithSubLayerContent = @"
package com.cet.eem.fusion.common.model.ext.subject.powermaintenance;

import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.PlanSheet;
import java.util.List;

/**
 * Plan sheet with sub layer extension
 */
public class PlanSheetWithSubLayer extends PlanSheet {
    private List<PlanSheetWithSubLayer> subLayers;
    private Integer level;
    private String parentId;
    
    // Getters and setters
    public List<PlanSheetWithSubLayer> getSubLayers() { return subLayers; }
    public void setSubLayers(List<PlanSheetWithSubLayer> subLayers) { this.subLayers = subLayers; }
    
    public Integer getLevel() { return level; }
    public void setLevel(Integer level) { this.level = level; }
    
    public String getParentId() { return parentId; }
    public void setParentId(String parentId) { this.parentId = parentId; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\ext\subject\powermaintenance\PlanSheetWithSubLayer.java", $planSheetWithSubLayerContent, $utf8NoBom)

Write-Host "Creating base model classes..." -ForegroundColor Cyan

# ConditionBlock
$conditionBlockContent = @"
package com.cet.eem.fusion.common.model.base;

import java.util.List;
import java.util.Map;

/**
 * Condition block for query conditions
 */
public class ConditionBlock {
    private String operator;
    private List<Map<String, Object>> conditions;
    private List<ConditionBlock> subBlocks;
    
    // Getters and setters
    public String getOperator() { return operator; }
    public void setOperator(String operator) { this.operator = operator; }
    
    public List<Map<String, Object>> getConditions() { return conditions; }
    public void setConditions(List<Map<String, Object>> conditions) { this.conditions = conditions; }
    
    public List<ConditionBlock> getSubBlocks() { return subBlocks; }
    public void setSubBlocks(List<ConditionBlock> subBlocks) { this.subBlocks = subBlocks; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\base\ConditionBlock.java", $conditionBlockContent, $utf8NoBom)

# SingleModelConditionDTO
$singleModelConditionDTOContent = @"
package com.cet.eem.fusion.common.model.base;

import java.util.Map;

/**
 * Single model condition DTO
 */
public class SingleModelConditionDTO {
    private String modelName;
    private Map<String, Object> conditions;
    private String operator;
    
    // Getters and setters
    public String getModelName() { return modelName; }
    public void setModelName(String modelName) { this.modelName = modelName; }
    
    public Map<String, Object> getConditions() { return conditions; }
    public void setConditions(Map<String, Object> conditions) { this.conditions = conditions; }
    
    public String getOperator() { return operator; }
    public void setOperator(String operator) { this.operator = operator; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\base\SingleModelConditionDTO.java", $singleModelConditionDTOContent, $utf8NoBom)

Write-Host "Creating workflow classes..." -ForegroundColor Cyan

# ReviewTaskParams
$reviewTaskParamsContent = @"
package com.cet.electric.workflow.common.model.params;

import java.util.Map;

/**
 * Review task parameters
 */
public class ReviewTaskParams {
    private String taskId;
    private String userId;
    private String result;
    private String comment;
    private Map<String, Object> variables;
    
    // Getters and setters
    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }
    
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getResult() { return result; }
    public void setResult(String result) { this.result = result; }
    
    public String getComment() { return comment; }
    public void setComment(String comment) { this.comment = comment; }
    
    public Map<String, Object> getVariables() { return variables; }
    public void setVariables(Map<String, Object> variables) { this.variables = variables; }
}
"@

[System.IO.File]::WriteAllText("$coreSourcePath\com\cet\electric\workflow\common\model\params\ReviewTaskParams.java", $reviewTaskParamsContent, $utf8NoBom)

# ReviewManyTaskParams
$reviewManyTaskParamsContent = @"
package com.cet.electric.workflow.common.model.params;

import java.util.List;
import java.util.Map;

/**
 * Review many task parameters
 */
public class ReviewManyTaskParams {
    private List<String> taskIds;
    private String userId;
    private String result;
    private String comment;
    private Map<String, Object> variables;
    
    // Getters and setters
    public List<String> getTaskIds() { return taskIds; }
    public void setTaskIds(List<String> taskIds) { this.taskIds = taskIds; }
    
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getResult() { return result; }
    public void setResult(String result) { this.result = result; }
    
    public String getComment() { return comment; }
    public void setComment(String comment) { this.comment = comment; }
    
    public Map<String, Object> getVariables() { return variables; }
    public void setVariables(Map<String, Object> variables) { this.variables = variables; }
}
"@

[System.IO.File]::WriteAllText("$coreSourcePath\com\cet\electric\workflow\common\model\params\ReviewManyTaskParams.java", $reviewManyTaskParamsContent, $utf8NoBom)

# ProcessVariableDefinition
$processVariableDefinitionContent = @"
package com.cet.electric.workflow.common.constants;

/**
 * Process variable definition constants
 */
public class ProcessVariableDefinition {
    public static final String PROCESS_INSTANCE_ID = "processInstanceId";
    public static final String TASK_ID = "taskId";
    public static final String USER_ID = "userId";
    public static final String RESULT = "result";
    public static final String COMMENT = "comment";
}
"@

[System.IO.File]::WriteAllText("$coreSourcePath\com\cet\electric\workflow\common\constants\ProcessVariableDefinition.java", $processVariableDefinitionContent, $utf8NoBom)

Write-Host "Creating definition classes..." -ForegroundColor Cyan

# ErrorCode in def.common
$errorCodeDefContent = @"
package com.cet.eem.fusion.common.def.common;

/**
 * Error code definition
 */
public class ErrorCode {
    public static final String SUCCESS = "0000";
    public static final String SYSTEM_ERROR = "9999";
    public static final String PARAMETER_ERROR = "1001";
    public static final String DATA_NOT_FOUND = "1002";
    public static final String PERMISSION_DENIED = "1003";
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\def\common\ErrorCode.java", $errorCodeDefContent, $utf8NoBom)

Write-Host "Created all additional missing classes successfully!" -ForegroundColor Green
Write-Host "- Extension model classes (SignInPointWithSubLayer, PlanSheetWithSubLayer)" -ForegroundColor Cyan
Write-Host "- Base model classes (ConditionBlock, SingleModelConditionDTO)" -ForegroundColor Cyan
Write-Host "- Workflow classes (ReviewTaskParams, ReviewManyTaskParams, ProcessVariableDefinition)" -ForegroundColor Cyan
Write-Host "- Definition classes (ErrorCode)" -ForegroundColor Cyan
