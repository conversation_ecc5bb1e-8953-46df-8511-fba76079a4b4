package com.cet.eem.fusion.maintenance.core.model.devicemanage.component;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : EditDeviceComponent
 * @Description : 编辑零件的参数
 * <AUTHOR> jiangzixuan
 * @Date: 2021-05-17 09:51
 */

@Getter
@Setter
@ApiModel(value = "EditDeviceComponent", description = "编辑零件")
public class EditDeviceComponent {
    @Length(max = 50,message = "零件厂家长度不能超过50")
    @NotNull(message = "厂家不能为空")
    private String brand;
    @Length(max = 50,message = "零件型号长度不能超过50")
    @NotNull(message = "规格不能为空")
    private String model;
    @NotNull(message = "单位不能为空")
    private String unit;
    @NotNull(message = "数量不能为空")
    private Double number;
    @Length(max = 50,message = "零件名称长度不能超过50")
    @NotNull(message = "名称不能为空")
    private String name;
    @NotNull(message = "id不能为空")
    private Long id;
    @NotNull(message = "设备id不能为空")
    private Long objectId;
    @NotNull(message = "设备类型不能为空")
    private String objectLabel;
}

