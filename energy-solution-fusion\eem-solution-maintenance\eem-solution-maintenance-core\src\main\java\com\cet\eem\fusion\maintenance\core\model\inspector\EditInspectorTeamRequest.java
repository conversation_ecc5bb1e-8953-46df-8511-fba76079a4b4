package com.cet.eem.fusion.maintenance.core.model.inspector;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : EditInspectorTeamRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-09 14:35
 */
@Getter
@Setter
@ApiModel(value = "EditInspectorTeamRequest", description = "编辑巡检班组")
public class EditInspectorTeamRequest {

    /**
     * 巡检班组ID
     */
    @NotNull(message = "巡检班组ID不能为空")
    private Long id;

    /**
     * 班组名
     */
    @NotEmpty(message = "班组名不能为空")
    private String name;

    /**
     * 此班组下的用户
     */
    private List<Long> userIds;
}

