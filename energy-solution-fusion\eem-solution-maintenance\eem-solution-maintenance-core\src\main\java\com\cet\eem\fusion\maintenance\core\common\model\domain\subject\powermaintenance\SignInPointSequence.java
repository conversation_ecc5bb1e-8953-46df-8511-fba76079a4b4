package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : SignInPoint
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 11:02
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.REGISTRATION_POINT_SEQUENCE)
public class SignInPointSequence extends EntityWithName {

    /**
     * 签到点设备
     */
    @JsonProperty("registrationpointid")
    private Long registrationPointId;
    /**
     * 排序字段
     */
    private Integer sort;

    public SignInPointSequence() {
        this.modelLabel = ModelLabelDef.REGISTRATION_POINT_SEQUENCE;
    }
}
