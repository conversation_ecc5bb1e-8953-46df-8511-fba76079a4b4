package com.cet.eem.fusion.maintenance.core.controller.bff.device;

import com.cet.eem.fusion.common.def.exception.ErrorCode;
import com.cet.eem.fusion.common.entity.ParentParam;
import com.cet.electric.baseconfig.common.base.BaseEntity;
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;
import com.cet.eem.fusion.common.service.auth.NodeAuthCheckService;
import com.cet.eem.fusion.common.def.OperationAuthDef;
import com.cet.eem.fusion.maintenance.core.common.model.domain.object.physicalquantity.MeasuredbyVo;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.*;
import com.cet.eem.fusion.maintenance.core.service.device.DeviceManagerService;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;

import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.maintenance.common.toolkit.CollectionUtils;
import com.cet.electric.matterhorn.devicedataservice.api.DataServiceRestApi;
import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.DataIdLogicalId;
import com.cet.electric.matterhorn.devicedataservice.common.entity.realtime.MeasurePointData;
import com.cet.futureblue.i18n.LanguageUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @Author: fyl
 * @Description:设备详情 设备管理页面接口
 * @Data: Created in 2021-04-08
 */
public class DeviceManageBffController {

    @Autowired
    private NodeAuthCheckService nodeAuthBffService;
    @Autowired
    private DeviceManagerService deviceManagerService;
    @Autowired
    protected ConnectionService connectionService;
    @Autowired
    protected DataServiceRestApi dataServiceRestApi;

    @ApiOperation(value = "导出二维码")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_BROWSER})
    @PostMapping(value = "/exportQrCodes")
    public ApiResult<String> exportQrCodes(
            @NotNull @RequestHeader("User-ID") Long userId,
            @NotNull @ApiParam(name = "baseVo", value = "查询条件", type = "BaseVo", required = true) @RequestBody EquipmentExportInfo info,
            HttpServletResponse response) throws Exception {
        return deviceManagerService.exportQrCodees(userId, info, response);
    }

    @ApiOperation(value = "显示设备信息二维码")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_BROWSER})
    @PostMapping(value = "/downloadQrCode", produces = "application/json")
    public ApiResult<String> qrCode(
            @NotNull @RequestHeader("User-ID") Long userId,
            @NotNull @ApiParam(name = "searchDto", value = "查询条件", type = "EquipmentSearchDto", required = true) @RequestBody EquipmentSearchDto searchDto,
            HttpServletResponse response) throws Exception {
        return deviceManagerService.qrCode(userId, searchDto, response);
    }

    @ApiOperation(value = "查看设备技术参数")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_BROWSER})
    @PostMapping(value = "/TechParam", produces = "application/json")
    public ApiResult<List<TechParamValue>> queryTechParam(
            @NotNull @ApiParam(name = "searchDto", value = "查询条件", type = "EquipmentSearchDto", required = true) @RequestBody EquipmentSearchDto searchDto

    ) {
        List<TechParamValue> result = deviceManagerService.queryTechParam(searchDto);
        return new ApiResult<>(result);
    }

    @ApiOperation(value = "写入更新设备技术参数")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_UPDATE})
    @PutMapping(value = "/TechParamValue", produces = "application/json")
    public ApiResult<Object> writeTechParamValue(
            @NotNull @ApiParam(name = "list", value = "查询条件", type = "List<TechParamValue>", required = true) @RequestBody List<TechParamValue> list
    ) {
        deviceManagerService.writeTechParamValue(list);
        return Result.ok();
    }

    @ApiOperation(value = "查看实时数据")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_BROWSER})
    @PostMapping(value = "/EquipmentData", produces = "application/json")
    public ApiResult<List<MeasurePointData>> queryRealtimedataBatch(
            @RequestParam("deviceId") Integer deviceId,
            @RequestBody List<DataIdLogicalId> dataIdLogicalIdList
    ) {
        return dataServiceRestApi.getDeviceMeasureData(deviceId, dataIdLogicalIdList);
    }

    @ApiOperation(value = "获取测点信息")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_BROWSER})
    @PostMapping(value = "/dataServiceParam", produces = "application/json")
    public ApiResult<List<RunningParamValue>> dataServiceParam(
            @NotNull @ApiParam(name = "groupId", value = "模板分组id", type = "Long", required = true) @RequestParam Long groupId,
            @RequestParam(required = false) @ApiParam(value = "项目id", name = "projectId") Long projectId,
            @RequestBody @ApiParam(value = "节点", name = "nodes", required = true) List<BaseVo> nodes
    ) {
        List<MeasuredbyVo> measuredbyVos = connectionService.queryMeasureBy(nodes, projectId);
        if (CollectionUtils.isEmpty(measuredbyVos)) {
            return new ApiResult<>();
        }
        List<RunningParamValue> list = deviceManagerService.dataServiceParam(groupId, measuredbyVos);
        return Result.ok(list);
    }

    @ApiOperation(value = "获取模板信息")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_BROWSER})
    @PostMapping(value = "/TemplateInfo", produces = "application/json")
    public ApiResult<List<BaseWithTemplate>> queryTemplateName(
            @NotNull @ApiParam(name = "dtoList", value = "查询条件", type = "List", required = true) @RequestBody BaseVo baseVo) {
        ParentParam parentParam = new ParentParam();
parentParam.setRootNode(new BaseEntity(baseVo.getId(),baseVo.getModelLabel()));
parentParam.setTenantId(GlobalInfoUtils.getTenantId());
parentParam.setUserId(GlobalInfoUtils.getUserId());
if (!nodeAuthBffService.checkCompleteOrganizationNodes(parentParam)) {
    throw new ValidationException(LanguageUtil.getMessage(DataMaintainLangKeyDef.Connect.NO_COMPLETE_AUTH));
}
        List<BaseWithTemplate> baseWithTemplates = deviceManagerService.queryTemplateName(baseVo);
        return new ApiResult<>(baseWithTemplates);
    }

    @ApiOperation(value = "获取节点信息")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_BROWSER})
    @PostMapping(value = "/nodedata", produces = "application/json")
    public ApiResult<List<Map<String, Object>>> nodedata(
            @RequestBody @ApiParam(name = "DeviceInfoRequest", value = "节点信息", required = true) DeviceInfoRequest request,
            @RequestParam(required = false) @ApiParam(name = "userId", value = "用户id", required = false) Long userId
    ) {
        if (!ParamUtils.checkPrimaryKeyValid(userId)) {
            userId = GlobalInfoUtils.getUserId();
        }

        if (!ParamUtils.checkPrimaryKeyValid(userId)) {
            return new ApiResult<>(ErrorCode.INTERVAL_CODE, "用户id不合法！", null);
        }

        if (!nodeAuthBffService.checkPartAuth(request.getSelected(), userId)) {
            throw new ValidationException("无当前节点访问权限！");
        }
        return deviceManagerService.getNodeData(request, userId);
    }

    @ApiOperation(value = "由前端控制获取节点信息，当rootLabel为project或者room的时候需要传subLabel")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_BROWSER})
    @PostMapping(value = "/nodedata/controlByFront", produces = "application/json")
    public ApiResult<List<Map<String, Object>>> nodedata(
            @RequestBody @ApiParam(name = "DeviceInfoRequest", value = "节点信息", required = true) DeviceInfoRequestControlByFront deviceInfoRequestControlByFront,
            @RequestParam(required = false) @ApiParam(name = "userId", value = "用户id", required = false) Long userId
    ) {
        if (!ParamUtils.checkPrimaryKeyValid(userId)) {
            userId = GlobalInfoUtils.getUserId();
        }

        if (!ParamUtils.checkPrimaryKeyValid(userId)) {
            return new ApiResult<>(ErrorCode.INTERVAL_CODE, "用户id不合法！", null);
        }
        QueryCondition queryCondition = deviceInfoRequestControlByFront.getQueryCondition();
        if (!nodeAuthBffService.checkPartAuth(new BaseVo(queryCondition.getRootID(), queryCondition.getRootLabel()), userId)) {
            throw new ValidationException("无当前节点访问权限！");
        }
        return deviceManagerService.getNodeData(deviceInfoRequestControlByFront, userId);
    }

    @ApiOperation(value = "批量导出设备技术参数数据")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_BROWSER})
    @PostMapping(value = "/ouputTechParam", produces = "application/json")
    public ApiResult<Object> outPutTechParam(HttpServletResponse response, @RequestBody @ApiParam(name = "BaseVo", value = "设备数据", required = true) BaseVo baseVo) throws Exception {
        deviceManagerService.exportTechParamByChoose(baseVo, response);
        return null;
    }

    @ApiOperation(value = "批量导入设备技术参数数据")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_MANAGE_UPDATE})
    @PostMapping(value = "/importTechParams", produces = "application/json")
    public ApiResult<Object> importTechParams(
            @RequestParam("file") MultipartFile file) throws IOException {
        deviceManagerService.importTechParams(file);
        return Result.ok();
    }

}



