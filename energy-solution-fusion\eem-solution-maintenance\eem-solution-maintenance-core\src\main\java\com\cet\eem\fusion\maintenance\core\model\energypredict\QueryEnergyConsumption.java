package com.cet.eem.fusion.maintenance.core.model.energypredict;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @className QueryEnergyConsumption
 * @description
 * @date 2021/8/10 14:22
 */
@Getter
@Setter
@ApiModel(value = "QueryEnergyConsumption", description = "查询能耗数据")
public class QueryEnergyConsumption {
    /**
     * 周期
     */
    @JsonProperty("aggregationcycle")
    private Integer aggregationCycle;

    /**
     * 模型id
     */
    private Long id;


    /**
     * 模型标识
     */
    @JsonProperty("modellabel")
    private String modelLabel;

    /**
     * 结束时间
     */
    @JsonProperty("endtime")
    private Long endTime;

    /**
     * 开始时间
     */
    @JsonProperty("starttime")
    private Long startTime;

    /**
     * 能源类型
     */
    @JsonProperty("energytype")
    private Integer energyType;

}

