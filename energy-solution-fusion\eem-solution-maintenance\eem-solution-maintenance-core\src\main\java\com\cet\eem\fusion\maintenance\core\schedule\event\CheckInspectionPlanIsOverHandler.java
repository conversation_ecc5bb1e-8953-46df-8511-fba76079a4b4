﻿package com.cet.eem.fusion.maintenance.core.schedule.event;

import com.cet.eem.fusion.maintenance.core.dao.PlanSheetDao;
import com.cet.eem.fusion.maintenance.core.schedule.domain.PlanSheetDomain;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * @ClassName : CheckInspectionPlanIsOverHandler
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-07 10:00
 */
@Component
@Slf4j
public class CheckInspectionPlanIsOverHandler {

    @Autowired
    private PlanSheetDao planSheetDao;

    @Autowired
    private PlanSheetDomain planSheetDomain;

    @EventListener
    public void checkInspectionPlanIsOver(CheckPlanIsOverEvent checkPlanIsOverEvent) {
        CheckInspectionPlanIsOverCommand command = checkPlanIsOverEvent.getCommand();
        if (command.isMayFireAgain()) {
            return;
        }
        try {
            planSheetDomain.pausePlan(Collections.singleton(command.getPlanSheet().getId()));
            planSheetDao.updateById(command.getPlanSheet().getId(), planSheet -> planSheet.setEnabled(false));
        } catch (SchedulerException e) {
            log.error("更新计划状态失败,计划详细信息:{}", JsonUtil.toJSONString(command.getPlanSheet()), e);
        }

    }
}

