package com.cet.eem.fusion.maintenance.core.model.workorder;

import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.params.UserTaskParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/7/6
 */
@Getter
@Setter
@ApiModel(description = "工单表单提交参数")
public class WorkOrderFormSubmitParam {
    @ApiModelProperty("工单编号")
    private String code;

    @ApiModelProperty("表单通用参数")
    private UserTaskParams userTaskParams;
}

