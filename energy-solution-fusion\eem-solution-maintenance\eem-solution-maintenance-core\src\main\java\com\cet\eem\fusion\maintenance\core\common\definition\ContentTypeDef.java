package com.cet.eem.fusion.maintenance.core.common.definition;

/**
 * Content type definitions
 */
public class ContentTypeDef {
    
    // JSON content types
    public static final String APPLICATION_JSON = "application/json";
    public static final String APPLICATION_JSON_UTF8 = "application/json;charset=UTF-8";
    
    // XML content types
    public static final String APPLICATION_XML = "application/xml";
    public static final String TEXT_XML = "text/xml";
    
    // Form content types
    public static final String APPLICATION_FORM_URLENCODED = "application/x-www-form-urlencoded";
    public static final String MULTIPART_FORM_DATA = "multipart/form-data";
    
    // Text content types
    public static final String TEXT_PLAIN = "text/plain";
    public static final String TEXT_HTML = "text/html";
    public static final String TEXT_CSS = "text/css";
    public static final String TEXT_JAVASCRIPT = "text/javascript";
    
    // Binary content types
    public static final String APPLICATION_OCTET_STREAM = "application/octet-stream";
    public static final String APPLICATION_PDF = "application/pdf";
    
    // Image content types
    public static final String IMAGE_JPEG = "image/jpeg";
    public static final String IMAGE_PNG = "image/png";
    public static final String IMAGE_GIF = "image/gif";
    public static final String IMAGE_BMP = "image/bmp";
    
    // Excel content types
    public static final String APPLICATION_EXCEL = "application/vnd.ms-excel";
    public static final String APPLICATION_EXCEL_XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    
    // Word content types
    public static final String APPLICATION_WORD = "application/msword";
    public static final String APPLICATION_WORD_DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
}
