package com.cet.eem.fusion.maintenance.core.dao.devicemanager.impl;

import com.cet.eem.fusion.maintenance.core.dao.devicemanager.DeviceManagerServiceDao;
import com.cet.electric.commons.ApiResult;

import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.maintenance.service.EemModelDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-14
 */
@Service
public class DeviceManagerServiceDaoImpl implements DeviceManagerServiceDao {
    @Autowired
    EemModelDataService eemModelDataService;

    @Override
    public List<Map<String, Object>> queryDevice(QueryCondition queryCondition) {
        List<Map<String, Object>> result = eemModelDataService.query(queryCondition).getData();
        return result;
    }

    @Override
    public ApiResult<List<Map<String, Object>>> query(QueryCondition queryCondition) {
        return eemModelDataService.query(queryCondition);
    }

}


