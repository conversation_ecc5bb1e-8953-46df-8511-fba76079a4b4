这个文档描述的是针对代码的迁移前后的一些说明
- 在进行迁移适配的时候：你可以在执行的时候创建md文件存储你的所有需要完成任务，并在任务执行阶段，不断更新md文件的任务状态，可以保留最新的时间信息
- 请注意，不要做出除了文档内描述内容以外的修改，这样会导致很多不可预知的问题

# Controller层变更

## 01 返回结果变更
- 返回结果封装类变更import com.cet.eem.common.model.Result;import com.cet.eem.common.model.ResultWithTotal;
以上两个类都需要改用import com.cet.electric.commons.ApiResult;
- 针对返回结果的封装方法也要改变，从com.cet.eem.common.model.Result;和com.cet.eem.common.model.ResultWithTotal;
修改成了统一的com.cet.eem.fusion.common.entity.Result类，但是不用修改ok和error的内容
- 所以，你需要先修改引入类，然后把返回静态方法是ResultWithTotal的修改为Result就行了。不要修改静态方法为ApiResult.ok()，这个方法是不存在的
- 迁移前代码
```java
import com.cet.eem.common.model.Result;
import com.cet.eem.common.model.ResultWithTotal;

public Result<EnergyLossDataVo> getTree(@RequestBody LossAnalysisSearchVo searchVo) {
    return Result.ok(lossAnalysisService.queryLineLossMessage(queryDTO));
}

@ApiOperation(value = "获取选中项的配置信息")
@PostMapping("/node/info")
public ResultWithTotal<List<LossDataVo>> getLossConfig(@RequestBody LossConfigRequestVo configRequestVo) {
    return lossAnalysisService.getLossConfigWithLossGroupNodes(configRequestVo);
}
```
- 迁移后代码(请注意，Result.ok()返回的类就是ApiResult，并不存在ApiResult.ok的方法)
```java
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.entity.Result;

public ApiResult<EnergyLossDataVo> getTree(@RequestBody LossAnalysisSearchVo searchVo) {
    return Result.ok(lossAnalysisService.queryLineLossMessage(queryDTO));
}

@ApiOperation(value = "获取选中项的配置信息")
@PostMapping("/node/info")
public ApiResult<List<LossDataVo>> getLossConfig(@RequestBody LossConfigRequestVo configRequestVo) {
    return lossAnalysisService.getLossConfigWithLossGroupNodes(configRequestVo);
}
```

## 02 针对权限的变更
- 接口权限控制枚举变更
```
import com.cet.eem.auth.aspect.EnumAndOr;
```
- 变更后
```
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;
```

# Service层变更
## 获取产品类型方式变更
变更前: List<Product> productList = productDao.queryProducts(GlobalInfoUtils.getProjectId());
变更后：List<Product> productList = productDao.queryProducts(GlobalInfoUtils.getTenantId(), Product.class);

# Dao层变更
## 

# 实体类变更
## 模型类型定义变更，请注意，这个修改只适用于模型，即Po。

变更前
```java
import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.model.model.BaseEntity;
import com.cet.piem.common.constant.TableNameDef;

@Data
@AllArgsConstructor
@ModelLabel(TableNameDef.TEAM_GROUP_ENERGY)
public class TeamGroupEnergy extends BaseEntity {

    @ApiModelProperty("能耗值")
    private Double value;

    public TeamGroupEnergy() {
        this.modelLabel = TableNameDef.TEAM_GROUP_ENERGY;
    }
}
```
变更后
```java
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;

@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.TEAM_GROUP_ENERGY)
public class TeamGroupEnergy extends EntityWithName {

    @ApiModelProperty("能耗值")
    private Double value;

    public TeamGroupEnergy() {
        this.modelLabel = ModelLabelDef.TEAM_GROUP_ENERGY;
    }
}
```

# 常量变更
```java
请求状态码：
变更前：Result.SUCCESS_CODE
变更后：ErrorCode.SUCCESS_CODE

EemCommonUtils.BLANK_STR(com.cet.eem.bll.common.util.EemCommonUtils;)
StringFormatUtils.BLANK_STR(com.cet.eem.fusion.common.utils.datatype.StringFormatUtils;)

CommonUtils.BLANK_STR(com.cet.eem.common.CommonUtils;)
StringFormatUtils.BLANK_STR(com.cet.eem.fusion.common.utils.datatype.StringFormatUtils;)

CommonUtils.APPLICATION_MSEXCEL
ContentTypeDef.APPLICATION_MSEXCEL

CommonUtils.APPLICATION_MS_EXCEL_07
ContentTypeDef.APPLICATION_MS_EXCEL_07

CommonUtils.DOUBLE_CONVERSION_COEFFICIENT
NumberCalcUtils.DOUBLE_CONVERSION_COEFFICIENT
```

# 静态方法变更
```java
# 工程不再提供projectid，而是改用tenantId,请注意代码的所有地方的修改，
相关tenantid获取的方式变更如下
变更前：GlobalInfoUtils.getProjectId()
变更后：GlobalInfoUtils.getTenantId()

变更前：CommonUtils.calcDouble()
变更后：NumberCalcUtils.calcDouble()（同步修改所属类路径：import com.cet.eem.fusion.common.utils.datatype.NumberCalcUtils;）

变更前：CommonUtils.parseInteger()
变更后：NumberUtils.parseInteger()（同步修改所属类路径：import com.cet.eem.fusion.common.utils.datatype.NumberCalcUtils;）

CommonUtils.formatDoubleWithOutScientificNotation()
StringFormatUtils.formatDoubleWithOutScientificNotation()
```
# 其他的类或者类路径变更

```java
变更前：import com.cet.eem.common.model.BaseVo;
变更后：import com.cet.eem.fusion.common.model.BaseVo;

变更前：import com.cet.eem.fusion.common.util.GlobalInfoUtils;
变更后：import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;

权限变更
变更前：import com.cet.eem.auth.aspect.EnumAndOr;
变更后：import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;

操作权限注解变更
变更前：import com.cet.eem.auth.aspect.OperationPermission;
变更后：import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;

权限类型变更：
变更前：import com.cet.eem.bll.common.def.OperationAuthDef;
变更后：import com.cet.eem.fusion.common.def.OperationAuthDef;

模型名称变更
变更前：import com.cet.piem.common.constant.TableNameDef;
变更后：import com.cet.eem.solution.common.def.common.label.ModelLabelDef;

模型字段变更
变更前：import com.cet.piem.common.constant.TableColumnNameDef;
变更后：import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;

redis缓存操作：
变更前：import com.cet.eem.common.service.RedisService;
变更后：import com.cet.eem.fusion.common.service.RedisService;

变更前：import com.cet.eem.common.model.event.RedisEventKey;
变更后：import com.cet.eem.fusion.common.model.event.RedisEventKey;

变更前：import com.cet.eem.bll.common.util.DataValidationUtils;
变更后：待补充，37版本之后修改

变更前：import com.cet.eem.common.definition.ModelLabelDef;
变更后：import com.cet.eem.fusion.common.def.label.ModelLabelDef;

变更前：import com.cet.eem.model.tool.QueryConditionBuilder;
变更后：import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;

变更前：import com.cet.eem.model.tool.ParentQueryConditionBuilder;
变更后：import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;

变更前：import com.cet.eem.model.tool.ModelServiceUtils;
变更后：import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;

进度工具类
变更前：import com.cet.piem.common.utils.ProgressUpdaterUtils;
变更后：import com.cet.eem.solution.common.utils.ProgressUpdaterUtils;

聚合周期常量：
变更前：import com.cet.piem.common.def.AggregationCycle;
变更后：import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;

变更前：import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
变更后：import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;

异常处理
变更前：import com.cet.eem.common.exception.BusinessBaseException;
变更后：import com.cet.eem.fusion.common.exception.BusinessBaseException;

时间处理工具类：
变更前：import com.cet.eem.common.utils.TimeUtil;
变更后：import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;

基础查询dao
变更前：import com.cet.eem.dao.BaseModelDao;
变更后：import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

物理量聚合数据：
变更前：import com.cet.eem.quantity.dao.QuantityAggregationDataDao;
变更后：import com.cet.eem.fusion.energy.sdk.dao.QuantityAggregationDataDao;

小数点位数处理
变更前：import com.cet.piem.common.utils.DoubleUtils;
变更后：import com.cet.eem.solution.common.utils.DoubleUtils;

ConfigServer
变更前：import com.cet.piem.service.feign.PiemConfigServerService;
变更后：import com.cet.eem.solution.common.feign.ConfigServerService;

ProductDao
变更前：import com.cet.eem.bll.common.dao.project.ProductDao;
变更后：import com.cet.eem.fusion.config.sdk.dao.ProductDao;

CommonUtils工具类
变更前：import com.cet.eem.common.CommonUtils;
变更后：import com.cet.eem.fusion.common.utils.CommonUtils;

变更前：import com.cet.eem.common.definition.LoginDef;
变更后：import com.cet.eem.fusion.common.def.auth.LoginDef;

变更前：import com.cet.eem.common.definition.exception.WorkOrderErrorCodeEnum;
变更后：import com.cet.eem.fusion.common.def.exception.WorkOrderErrorCodeEnum;

变更前：import com.cet.eem.common.constant.EnumDataTypeId;
变更后：import com.cet.eem.fusion.common.def.base.EnumDataTypeId;

变更前：import com.cet.eem.common.definition.ColumnDef;
变更后：import com.cet.eem.fusion.common.def.common.ColumnDef;

变更前：import com.cet.eem.common.constant.EnumOperationType;
变更后：import com.cet.eem.fusion.common.def.common.EnumOperationType;

变更前：import com.cet.eem.bll.common.def.quantity.PhasorDef;
变更后：import com.cet.eem.fusion.common.def.quantity.PhasorDef;

变更前：import com.cet.eem.bll.common.def.quantity.FrequencyDef;
变更后：import com.cet.eem.fusion.common.def.quantity.FrequencyDef;

变更前：import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
变更后：import com.cet.eem.fusion.common.def.quantity.QuantityCategoryDef;

变更前：import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
变更后：import com.cet.eem.fusion.common.def.quantity.QuantityTypeDef;

分页相关：
变更前：import com.cet.eem.common.model.Page;
变更后：import com.cet.eem.fusion.common.model.Page;

分页工具：
变更前：import com.cet.eem.common.page.PageUtils;
变更后：import com.cet.eem.fusion.common.utils.page.PageUtils;

排序
变更前：import com.cet.eem.model.base.Order;
变更后：import com.cet.eem.fusion.common.modelutils.model.base.Order;

文件导出工具类
变更前：import com.cet.eem.common.file.FileUtils;
变更后：import com.cet.eem.fusion.common.utils.file.FileUtils;

变更前：import com.cet.eem.common.model.auth.user.UserVo;
变更后：import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;

项目数据类型
变更前：import com.cet.eem.bll.common.model.domain.object.physicalquantity.ProjectUnitClassify;
变更后：import com.cet.eem.fusion.common.def.base.ProjectUnitClassify;

用户自定义单位
变更前：import com.cet.eem.bll.common.model.domain.object.physicalquantity.UserDefineUnit;
变更后：import com.cet.electric.baseconfig.common.entity.UserDefineUnit;

变更前：import com.cet.eem.bll.common.model.peccore.PecCoreTreeSearchVo;
变更后：import com.cet.eem.fusion.config.sdk.entity.peccore.PecCoreTreeSearchVo;

变更前：import com.cet.eem.common.constant.EnumSystemEventType;
变更后：import com.cet.eem.fusion.common.def.energy.EnumSystemEventType;

变更前：import com.cet.eem.common.model.peccore.Meter;
变更后：import com.cet.eem.fusion.common.model.peccore.Meter;

日志注解、OperationLog
变更前：import com.cet.eem.bll.common.log.annotation.OperationLog;
变更后：import com.cet.eem.fusion.config.sdk.service.log.OperationLog;

变更前：import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
变更后：import com.cet.eem.fusion.config.sdk.def.OperationLogType;

变更前：import com.cet.eem.bll.common.log.constant.EnumOperationSubType;
变更后：import com.cet.eem.fusion.common.utils.EnumOperationSubType;

变更前：import com.cet.eem.model.tool.QueryResultContentTaker;
变更后：import com.cet.eem.fusion.common.modelutils.model.tool.QueryResultContentTaker;

变更前：import com.cet.eem.common.constant.PecsNodeType;
变更后：import com.cet.eem.fusion.common.def.pec.PecsNodeType;

变更前：import com.cet.eem.bll.common.def.MessageTypeDef;
变更后：import com.cet.eem.fusion.common.def.MessageTypeDef;

变更前：import com.cet.eem.common.utils.PoiExcelUtils;
变更后：import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;

变更前：import com.cet.eem.common.constant.ExcelType;
变更后：import com.cet.eem.fusion.common.def.base.ExcelType;

变更前：import com.cet.eem.common.ErrorUtils;
变更后：import com.cet.eem.fusion.common.utils.ErrorUtils;
import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;

import com.cet.eem.common.definition.SplitCharDef;
import com.cet.eem.fusion.common.def.common.SplitCharDef;

import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.fusion.common.def.common.EnumOperationType;

import com.cet.eem.quantity.dao.QuantityObjectDao;

import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;
import com.cet.electric.modelsdk.quantity.model.QuantityAggregationData;

import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;
import com.cet.electric.baseconfig.common.entity.QuantityObject;

import com.cet.eem.auth.service.InnerAuthService;
import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;

import com.cet.eem.node.TopologyUtils;
import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;

import com.cet.eem.bll.common.util.ExcelValidationUtils;
import com.cet.eem.fusion.common.utils.excel.ExcelValidationUtils;

import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.fusion.config.sdk.service.EemNodeService;

import com.cet.eem.common.ParamUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;

import com.cet.eem.bll.common.dao.poi.EemPoiRecordDao;
import com.cet.eem.fusion.energy.sdk.dao.EemPoiRecordDao;

import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;
import com.cet.eem.fusion.energy.sdk.model.EemPoiRecord;

import com.cet.eem.common.constant.QueryType;
import com.cet.eem.fusion.common.def.base.QueryType;

import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;

变更前：import com.cet.eem.model.tool.SubConditionBuilder;
变更后：import com.cet.eem.fusion.common.modelutils.model.tool.SubConditionBuilder;

import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;

import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;

import com.cet.eem.model.base.ModelSingeWriteVo;
import com.cet.eem.fusion.common.modelutils.model.base.ModelSingeWriteVo;

import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;

import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;

import com.cet.eem.common.constant.AggregationType;
import com.cet.eem.fusion.energy.sdk.def.AggregationType;

变更前：import com.cet.eem.common.constant.EnumRoomType;
变更后：import com.cet.eem.fusion.common.def.base.EnumRoomType;

import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.fusion.common.def.base.EnergyTypeDef;

import com.cet.eem.conditions.update.LambdaUpdateWrapper;
import com.cet.eem.fusion.common.modelutils.conditions.update.LambdaUpdateWrapper;

import com.cet.eem.bll.common.model.domain.subject.generalrules.UnnaturalSetVo;
import com.cet.eem.fusion.config.sdk.entity.unnatural.UnnaturalSetVo;

import com.cet.eem.bll.common.model.domain.subject.generalrules.FeeScheme;
import com.cet.eem.fusion.energy.sdk.model.generalrules.FeeScheme;

import com.cet.eem.bll.common.model.domain.object.architecture.RoomVo;
import com.cet.eem.fusion.config.sdk.model.node.RoomVo;

import com.cet.eem.bll.common.model.domain.object.architecture.BuildingVo;
import com.cet.eem.fusion.config.sdk.model.node.BuildingVo;

变更前：import com.cet.eem.common.utils.performance.annotation.ExecuteIndexAnnotation;
变更后：import com.cet.eem.fusion.common.utils.performance.annotation.ExecuteIndexAnnotation;
```

# 迁移检查清单
完成融合适配后，需要做检查复盘，补全遗漏和不对的地方，请检查以下项目：

- [ ] 针对迁移的代码，Controller中引用的相关service和实体类，注意扫描，识别新的service路径并修复。
- [ ] 针对sevice引用的实体，dao，也需要将新的引用路径修复。
- [ ] 针对dao层，也需要完善相关的dao层针对实体类的引用路径。