package com.cet.eem.fusion.maintenance.core.controller.bff.app;

import com.cet.eem.fusion.common.entity.Result;
import com.cet.eem.fusion.maintenance.common.log.annotation.OperationLog;
import com.cet.eem.fusion.maintenance.common.log.constant.EnumOperationSubType;
import com.cet.eem.fusion.maintenance.core.model.sign.SignInStatisticsTableWriteVo;
import com.cet.eem.fusion.maintenance.core.service.singinpoint.app.SignInStatisticsTableMobileService;
import com.cet.electric.commons.ApiResult;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 签到点接口
 *
 * <AUTHOR>
 * @date 2021/5/12
 */
public class SignInStatisticsTableBffMobileController {
    @Autowired
    SignInStatisticsTableMobileService signInStatisticsTableMobileService;

    @ApiOperation(value = "写入签到数据")
    @PostMapping("")
    @OperationLog(operationType = OperationLogType.SIGN_IN_STATISTICS, subType = EnumOperationSubType.ADD, description = "签到点数据")
    public ApiResult<Object> writeSignInStatisticsTable(@RequestBody List<SignInStatisticsTableWriteVo> writeVos) {
        signInStatisticsTableMobileService.writeSignInStatisticsTable(writeVos);
        return Result.ok();
    }
}



