﻿package com.cet.eem.fusion.maintenance.core.utils;


import com.cet.eem.fusion.common.def.base.EnumRoomType;
import com.cet.eem.fusion.common.modelutils.model.tool.QueryResultContentTaker;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.EquipmentExportInfo;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.QrInfo;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 导出excel相关工具类
 *
 * <AUTHOR>
 * @date 2020-6-17
 */
@Slf4j
public class ExcelExportFactory {


    private static final int EVEN = 2;

    private List<String> colLst = new ArrayList<>();

    private String excelType;

    public void setColLst(List<String> colLst) {
        this.colLst.addAll(colLst);
    }

    public void setExcelType(String type) {
        this.excelType = type;
    }

    public void imageIntoExcel(Integer n, List<Map<String, Object>> infos, HSSFPatriarch patriarch, HSSFWorkbook wb, HSSFSheet sheet, EquipmentExportInfo room) throws IOException {

        int m = n * 52 + 1;
        int l = n * 52 + 8;
        String roomType;
        Long objectId;
        String objectLabel;
        for (int i = 0; i < infos.size(); i++) {
            int y1 = 0;
            int y2 = 0;
            int x1 = 0;
            int x2 = 0;
            if (i % 2 == 0) {
                x1 = 1;
                x2 = 3;
            } else {
                x1 = 6;
                x2 = 8;
            }
            if (i > 1) {
                y1 = m + 20;
                y2 = l + 20;
            } else {
                y1 = m;
                y2 = l;
            }
            Map<String, Object> info = infos.get(i);
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            objectLabel = QueryResultContentTaker.getModelLabel(info);
            objectId = QueryResultContentTaker.getId(info);
            String qrCode = JsonTransferUtils.toJSONString(new QrInfo(objectId, objectLabel));
            if (!Objects.isNull(room.getRoomType())) {
                roomType = EnumRoomType.textOfId(room.getRoomType());
            } else {
                roomType = "项目";
            }
            BitMatrix bitMatrix = QrCodeUtils.createCode(qrCode, 223, 223);
            MatrixToImageWriter.writeToStream(bitMatrix, "jpg", byteArrayOut);
            HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0, 223, 223, (short) x1, y1, (short) x2, y2);
            patriarch.createPicture(anchor, wb.addPicture(byteArrayOut.toByteArray(), Workbook.PICTURE_TYPE_JPEG));
            int j = 1;
            setProjectDatas(sheet, y2, x1, "设备名称" + ":" + info.get(ColumnDef.NAME), j, i);
            j = j + 2;
            Object code = info.get(ColumnDef.CODE);
            setProjectDatas(sheet, y2, x1, "设备编号" + ":" + (Objects.isNull(code) ? "--" : code), j, i);
            j = j + 2;
            setProjectDatas(sheet, y2, x1, roomType + ":" + room.getName(), j, i);
        }


    }

    private void setProjectDatas(HSSFSheet sheet, int y2, int x1, String value, int j, int i) {
        HSSFRow row;
        if (i % EVEN == 0) {
            row = sheet.createRow(y2 + j + EVEN);
        } else {
            row = sheet.getRow(y2 + j + EVEN);
        }
        HSSFCell nowCell = row.createCell(x1);
        nowCell.setCellValue(value);

    }


    /**
     * (2007 xlsx后缀 导出)
     *
     * @param
     * @return void 返回类型
     * <AUTHOR>
     * @2016-12-7上午10:44:30
     */
    public void exportExcelByTemp(String tempPath, List<Map<String, Object>> dataLst, HttpServletResponse response) throws IOException {
        //excel模板路径
        File fi = new File(tempPath);
        try (HSSFWorkbook wb = new HSSFWorkbook(Files.newInputStream(fi.toPath()))) {
            //读取了模板内所有sheet内容
            HSSFSheet sheet = wb.getSheetAt(0);
            //在相应的单元格进行赋值
            for (int i = EVEN; i < dataLst.size() + EVEN; i++) {
                HSSFRow row = sheet.createRow(i);
                for (int j = 0; j < colLst.size(); j++) {
                    setCellValue(dataLst.get(i - EVEN).getOrDefault(colLst.get(j), ""), j, row);
                }
            }
            wb.write(response.getOutputStream());
        } catch (Exception e) {
            log.error("导出Excel异常：", e);
        }
    }


    private void setCellValue(Object data, int colNum, HSSFRow row) {
        row.createCell(colNum).setCellValue((String) data);
    }


}


