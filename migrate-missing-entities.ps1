# Migrate missing entity classes from energy-base to fusion project
Write-Host "Starting migration of missing entity classes..." -ForegroundColor Green

# Define source and target paths
$sourceBasePath = "energy-base\cet-eem-bll-base\cet-eem-bll-common\src\main\java\com\cet\eem\bll\common\model\domain\subject"
$targetBasePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java\com\cet\eem\fusion\common\model\domain\subject"

# Create target directories if they don't exist
Write-Host "Creating target directories..." -ForegroundColor Cyan
New-Item -ItemType Directory -Path "$targetBasePath\powermaintenance" -Force | Out-Null
New-Item -ItemType Directory -Path "$targetBasePath\activiti" -Force | Out-Null

# Function to migrate and update package names
function Migrate-EntityClass {
    param(
        [string]$sourceFile,
        [string]$targetFile,
        [string]$oldPackage,
        [string]$newPackage
    )
    
    if (!(Test-Path $sourceFile)) {
        Write-Host "Source file not found: $sourceFile" -ForegroundColor Red
        return $false
    }
    
    try {
        # Read source file content
        $content = Get-Content $sourceFile -Raw -Encoding UTF8
        
        # Update package declaration
        $content = $content -replace [regex]::Escape($oldPackage), $newPackage
        
        # Write to target file
        $targetDir = Split-Path $targetFile -Parent
        if (!(Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        # Write without BOM
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText($targetFile, $content, $utf8NoBom)
        
        Write-Host "  - Migrated: $(Split-Path $sourceFile -Leaf)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "  - Error migrating $(Split-Path $sourceFile -Leaf): $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Migrate powermaintenance entities
Write-Host "`nMigrating powermaintenance entities..." -ForegroundColor Cyan
$powermaintenanceFiles = @(
    "Attachment.java",
    "DeviceComponent.java", 
    "DevicePlanRelationship.java",
    "DeviceSystem.java",
    "EnabledDays.java",
    "ExecuteStrategyType.java",
    "InspectionParameter.java",
    "InspectionScheme.java",
    "InspectionSchemeDetail.java",
    "InspectionSchemeDetailExport.java",
    "InspectionSchemeDetailVo.java",
    "MaintenanceExtend.java",
    "MaintenanceGroup.java",
    "MaintenanceItem.java",
    "MaintenanceTypeDefine.java",
    "MeasureNode.java",
    "PlanSheet.java",
    "ProcessFlowUnit.java",
    "RunningParamNode.java",
    "SignInEquipment.java",
    "SignInGroup.java",
    "SignInPoint.java",
    "SignInPointSequence.java",
    "SignInStatisticsTable.java",
    "SignInStatusDef.java",
    "SignInStatusRecord.java",
    "SpareParts.java",
    "SparePartsDevice.java",
    "SparePartsReplaceRecord.java",
    "SparePartsReplaceRecordVo.java",
    "WorkOrderCheckInfo.java"
)

$migratedCount = 0
foreach ($file in $powermaintenanceFiles) {
    $sourceFile = "$sourceBasePath\powermaintenance\$file"
    $targetFile = "$targetBasePath\powermaintenance\$file"
    $oldPackage = "package com.cet.eem.bll.common.model.domain.subject.powermaintenance"
    $newPackage = "package com.cet.eem.fusion.common.model.domain.subject.powermaintenance"
    
    if (Migrate-EntityClass -sourceFile $sourceFile -targetFile $targetFile -oldPackage $oldPackage -newPackage $newPackage) {
        $migratedCount++
    }
}

# Migrate activiti entities
Write-Host "`nMigrating activiti entities..." -ForegroundColor Cyan
$activitiFiles = @(
    "WorksheetAbnormalReason.java"
)

foreach ($file in $activitiFiles) {
    $sourceFile = "$sourceBasePath\activiti\$file"
    $targetFile = "$targetBasePath\activiti\$file"
    $oldPackage = "package com.cet.eem.bll.common.model.domain.subject.activiti"
    $newPackage = "package com.cet.eem.fusion.common.model.domain.subject.activiti"
    
    if (Migrate-EntityClass -sourceFile $sourceFile -targetFile $targetFile -oldPackage $oldPackage -newPackage $newPackage) {
        $migratedCount++
    }
}

Write-Host "`nPowermaintenance entity migration completed!" -ForegroundColor Green
Write-Host "Total entities migrated: $migratedCount" -ForegroundColor Cyan

# Now migrate common entities from other locations
Write-Host "`nMigrating common entities from other locations..." -ForegroundColor Cyan

# Check for common entities in cet-eem-dal
$dalCommonPath = "energy-base\cet-eem-dal\cet-eem-common\src\main\java\com\cet\eem\common"
$targetCommonPath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java\com\cet\eem\fusion\common"

if (Test-Path $dalCommonPath) {
    Write-Host "Found common entities in DAL, migrating..." -ForegroundColor Yellow
    
    # Create target common directories
    New-Item -ItemType Directory -Path "$targetCommonPath\definition" -Force | Out-Null
    New-Item -ItemType Directory -Path "$targetCommonPath\model\auth\user" -Force | Out-Null
    New-Item -ItemType Directory -Path "$targetCommonPath\model\base" -Force | Out-Null
    New-Item -ItemType Directory -Path "$targetCommonPath\annotation" -Force | Out-Null
    
    # Look for specific files we need
    $commonFiles = Get-ChildItem -Path $dalCommonPath -Recurse -Filter "*.java" | Where-Object { 
        $_.Name -match "(Order|UserGroupVo|RoleVo|ModelDao|AuthUtils|NodeAuthCheckService|ConnectionService)\.java$" 
    }
    
    foreach ($file in $commonFiles) {
        $relativePath = $file.FullName.Substring($dalCommonPath.Length + 1)
        $targetFile = "$targetCommonPath\$relativePath"
        $targetDir = Split-Path $targetFile -Parent
        
        if (!(Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        # Copy and update package names
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        $content = $content -replace "package com\.cet\.eem\.common", "package com.cet.eem.fusion.common"
        
        # Write without BOM
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText($targetFile, $content, $utf8NoBom)
        
        Write-Host "  - Migrated common: $($file.Name)" -ForegroundColor Green
        $migratedCount++
    }
}

Write-Host "`nEntity migration script completed!" -ForegroundColor Green
Write-Host "Total files migrated: $migratedCount" -ForegroundColor Cyan
Write-Host "Next step: Run compilation to check for remaining missing dependencies" -ForegroundColor Yellow
