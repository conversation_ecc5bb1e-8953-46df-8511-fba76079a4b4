# PowerShell script to fix BOM encoding issues in newly created files
Write-Host "Fixing BOM encoding issues in newly created files..."

$baseDir = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Function to remove BOM from file
function Remove-BOM {
    param($filePath)
    
    if (Test-Path $filePath) {
        try {
            # Read file content without BOM
            $content = Get-Content -Path $filePath -Raw -Encoding UTF8
            
            # Remove BOM character if present
            if ($content.StartsWith([char]0xFEFF)) {
                $content = $content.Substring(1)
                Write-Host "  - Removing BOM from: $filePath"
                
                # Write back without BOM
                [System.IO.File]::WriteAllText($filePath, $content, [System.Text.UTF8Encoding]::new($false))
                Write-Host "Fixed BOM in: $(Split-Path $filePath -Leaf)"
            }
        }
        catch {
            Write-Host "Error processing $filePath : $($_.Exception.Message)"
        }
    }
}

# List of newly created files that need BOM fix
$newFiles = @(
    "$baseDir\com\cet\eem\fusion\common\conditions\query\QueryConditionBuilder.java",
    "$baseDir\com\cet\eem\fusion\common\model\objective\physicalquantity\PhysicalQuantity.java",
    "$baseDir\com\cet\eem\fusion\common\model\ext\subject\powermaintenance\PlanSheetWithSubLayer.java",
    "$baseDir\com\cet\eem\fusion\common\model\ext\subject\powermaintenance\DeviceWithSubLayer.java",
    "$baseDir\com\cet\eem\fusion\common\util\TimeUtil.java",
    "$baseDir\com\cet\eem\fusion\common\util\CommonUtilsService.java",
    "$baseDir\com\cet\eem\fusion\common\utils\ExcelValidationUtils.java",
    "$baseDir\com\cet\eem\fusion\common\utils\StringUtils.java",
    "$baseDir\com\cet\eem\fusion\common\model\tool\QueryConditionBuilder.java",
    "$baseDir\com\cet\eem\fusion\common\service\EemModelDataService.java",
    "$baseDir\com\cet\eem\fusion\common\model\auth\user\UserInfo.java",
    "$baseDir\com\cet\eem\fusion\common\model\auth\node\NodeInfo.java",
    "$baseDir\com\cet\eem\fusion\common\auth\utils\AuthUtils.java",
    "$baseDir\com\cet\eem\fusion\maintenance\constant\MaintenanceConstant.java",
    "$baseDir\com\cet\eem\fusion\common\definition\CommonDef.java",
    "$baseDir\com\cet\eem\fusion\common\definition\ContentTypeDef.java",
    "$baseDir\com\cet\electric\workflow\api\UserTaskRestApi.java",
    "$baseDir\com\cet\electric\workflow\api\TriggerRestApi.java",
    "$baseDir\com\cet\eem\fusion\common\exception\BusinessException.java",
    "$baseDir\com\cet\eem\fusion\common\model\event\ConvergenceEvent.java",
    "$baseDir\com\cet\eem\fusion\common\service\event\ConvergenceEventService.java",
    "$baseDir\com\cet\eem\fusion\common\service\event\expert\ExpertService.java",
    "$baseDir\com\cet\eem\fusion\common\service\event\PecCoreEventBffService.java",
    "$baseDir\com\cet\eem\fusion\common\dao\event\ConvergenceEventDao.java",
    "$baseDir\com\cet\eem\fusion\common\model\domain\perception\logicaldevice\LogicalDevice.java",
    "$baseDir\com\cet\eem\fusion\common\model\domain\object\powersystem\PowerSystemObject.java",
    "$baseDir\com\cet\eem\fusion\common\dao\device\DeviceCommonInfoDao.java",
    "$baseDir\com\cet\eem\fusion\common\dao\device\PecDeviceExtendDao.java",
    "$baseDir\com\cet\eem\fusion\common\model\device\DeviceCommonInfo.java",
    "$baseDir\com\cet\eem\fusion\common\model\device\PecDeviceExtendVo.java",
    "$baseDir\com\cet\eem\fusion\common\utils\performance\annotation\ExecuteIndexAnnotation.java",
    "$baseDir\com\cet\eem\fusion\common\constant\PecsNodeType.java",
    "$baseDir\com\cet\eem\fusion\common\constant\DemandConstant.java",
    "$baseDir\com\cet\eem\fusion\common\model\domain\subject\huaxingguangdian\EventPlan.java",
    "$baseDir\com\cet\eem\fusion\common\model\expert\ExpertRule.java",
    "$baseDir\com\cet\eem\fusion\common\model\expert\ExpertAnalysis.java",
    "$baseDir\com\cet\eem\fusion\common\log\service\LogService.java",
    "$baseDir\com\cet\eem\fusion\common\model\peccore\PecCoreNode.java"
)

Write-Host "Processing $($newFiles.Count) newly created files..."

foreach ($file in $newFiles) {
    Remove-BOM $file
}

Write-Host "BOM fix for new files completed!"
