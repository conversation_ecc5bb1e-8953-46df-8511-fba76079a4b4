package com.cet.eem.fusion.maintenance.core.model.handover;

import com.cet.eem.fusion.maintenance.core.def.HandoverDef;
import com.cet.eem.fusion.maintenance.core.model.workorder.OperationUser;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7
 */
@Getter
@Setter
public class HandoverMatterVo {
    @ApiModelProperty("值班长")
    @JsonProperty(value = HandoverDef.DUTYOFFICER)
    private Long dutyOfficer;

    @ApiModelProperty("值班长名称")
    private String dutyOfficerName;

    @ApiModelProperty("班组id")
    @JsonProperty(value = HandoverDef.TEAM_ID)
    private Long teamId;

    @ApiModelProperty("班组名称")
    private String teamName;

    @ApiModelProperty("交接班注意事项")
    @JsonProperty(value = HandoverDef.HANDOVERMATTER)
    private String handoverMatter;

    @ApiModelProperty("是否第一次接班")
    private Boolean firstHandOver;

    @ApiModelProperty("当前正在值班值班长")
    private Long onDutyOfficer;

    @ApiModelProperty("当前正在值班值班长名称")
    private String onDutyOfficerName;

    @ApiModelProperty("允许选择的值班人员")
    private List<OperationUser> allowChooseUsers;
}

