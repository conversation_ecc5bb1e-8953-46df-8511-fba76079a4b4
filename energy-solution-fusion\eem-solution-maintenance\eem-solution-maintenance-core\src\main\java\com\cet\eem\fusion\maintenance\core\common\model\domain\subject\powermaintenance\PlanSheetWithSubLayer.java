package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import java.util.List;

/**
 * Plan sheet with sub layer
 */
public class PlanSheetWithSubLayer extends EntityWithName {
    
    private String planName;
    private String planType;
    private String planStatus;
    private String description;
    private String deviceId;
    private String deviceName;
    private String nodeId;
    private String nodeName;
    private String planDate;
    private String executor;
    private String reviewer;
    private List<PlanSheetWithSubLayer> subLayers;
    
    public String getPlanName() {
        return planName;
    }
    
    public void setPlanName(String planName) {
        this.planName = planName;
    }
    
    public String getPlanType() {
        return planType;
    }
    
    public void setPlanType(String planType) {
        this.planType = planType;
    }
    
    public String getPlanStatus() {
        return planStatus;
    }
    
    public void setPlanStatus(String planStatus) {
        this.planStatus = planStatus;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getDeviceName() {
        return deviceName;
    }
    
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    
    public String getNodeId() {
        return nodeId;
    }
    
    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }
    
    public String getNodeName() {
        return nodeName;
    }
    
    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }
    
    public String getPlanDate() {
        return planDate;
    }
    
    public void setPlanDate(String planDate) {
        this.planDate = planDate;
    }
    
    public String getExecutor() {
        return executor;
    }
    
    public void setExecutor(String executor) {
        this.executor = executor;
    }
    
    public String getReviewer() {
        return reviewer;
    }
    
    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }
    
    public List<PlanSheetWithSubLayer> getSubLayers() {
        return subLayers;
    }
    
    public void setSubLayers(List<PlanSheetWithSubLayer> subLayers) {
        this.subLayers = subLayers;
    }
}
