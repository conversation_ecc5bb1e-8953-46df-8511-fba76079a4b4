package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : MaintenanceGroup
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-10 13:47
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.MAINTENANCE_GROUP)
public class MaintenanceGroup extends EntityWithName {

    /**
     * 项目
     */
    @JsonProperty("projectid")
    private Long projectId;

    public MaintenanceGroup() {
        this.modelLabel = ModelLabelDef.MAINTENANCE_GROUP;
    }
}
