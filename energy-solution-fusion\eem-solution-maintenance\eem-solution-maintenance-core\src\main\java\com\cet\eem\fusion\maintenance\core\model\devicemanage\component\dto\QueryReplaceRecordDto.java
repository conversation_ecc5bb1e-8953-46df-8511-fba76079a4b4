package com.cet.eem.fusion.maintenance.core.model.devicemanage.component.dto;

import com.cet.eem.fusion.common.model.Page;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : QueryReplaceRecord
 * @Description : 查询备件更换记录参数
 * <AUTHOR> jzx
 * @Date: 2021-05-14 16:36
 */
@Data
public class QueryReplaceRecordDto {
    @NotNull(message = "分页信息不能为空")
    private Page page;

    /**
     * 设备类型id
     */
    private Long objectLabelId;
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private Long startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private Long endTime;

    /**
     * 备件名称 or 设备名称
     */
    private String keyWord;

    private Long systemId;
}

