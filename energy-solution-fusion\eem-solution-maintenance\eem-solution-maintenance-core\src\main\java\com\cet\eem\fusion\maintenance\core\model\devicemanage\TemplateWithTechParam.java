package com.cet.eem.fusion.maintenance.core.model.devicemanage;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.AttributeTemplateDto;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.TechParam;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("模板表和技术参数关系")
@ModelLabel(ModelLabelDef.NODE_TEMPLATE)
public class TemplateWithTechParam extends AttributeTemplateDto {
    @JsonProperty(ModelLabelDef.TECH_PARAM_TEMPLATE+"_model")
    private List<TechParam> techParamList;

}


