package com.cet.eem.fusion.maintenance.core.dao.devicemanager;

import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.NodeWithMeasureNode;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.RunningParamNode;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-14
 */
public interface RunningParamNodeDao  extends BaseModelDao<RunningParamNode> {

    NodeWithMeasureNode nodeWithMeasureNode(Long id);
}


