package com.cet.eem.fusion.maintenance.core.service.bff.inspect;

import com.cet.eem.fusion.maintenance.core.common.toolkit.CollectionUtils;
import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;

import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.maintenance.core.common.model.auth.user.UserGroupVo;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/7
 */
@Service
public class InspectorBffServiceImpl implements InspectorBffService {
    @Autowired
    InspectorService inspectorService;

    @Resource
    UserRestApi userRestApi;

    @Override
    public List<UserGroupVo> queryInspectorTeam(Long tenantId) {
        List<UserGroupVo> userGroupList = inspectorService.queryInspectorTeam(tenantId);

        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        if (!inspectorService.isMaintenanceUser(user)) {
            return userGroupList;
        }

        Long group = authUtils.getRelativeGroup(user);
        return userGroupList.stream().filter(it -> group.equals(it.getId())).collect(Collectors.toList());
    }

    @Override
    public List<UserGroupVo> queryInspectorTeamWithoutUser(Long tenantId, String name) {
        List<UserGroupVo> userGroupList = inspectorService.queryInspectorTeamWithoutUser(tenantId);
        if (CollectionUtils.isEmpty(userGroupList)) {
            return Collections.emptyList();
        }

        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        if (!inspectorService.isMaintenanceUser(user)) {
            return filterGroupByName(name, userGroupList);
        }

        Long group = authUtils.getRelativeGroup(user);
        if (StringUtils.isEmpty(name)) {
            return userGroupList.stream()
                    .filter(it -> group.equals(it.getId()))
                    .sorted((v1, v2) -> CommonUtils.sort(v1.getId(), v2.getId(), true))
                    .collect(Collectors.toList());
        }

        return userGroupList.stream()
                .filter(it -> group.equals(it.getId()) && it.getName().contains(name))
                .sorted((v1, v2) -> CommonUtils.sort(v1.getId(), v2.getId(), true))
                .collect(Collectors.toList());
    }

    @Override
    public List<UserGroupVo> queryInspectorTeamWithOutAuth(Long tenantId, String name) {
        authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        List<UserGroupVo> userGroupList = inspectorService.queryInspectorTeamWithoutUser(tenantId);
        if (CollectionUtils.isEmpty(userGroupList)) {
            return Collections.emptyList();
        }

        return filterGroupByName(name, userGroupList);
    }

    private List<UserGroupVo> filterGroupByName(String name, List<UserGroupVo> userGroupList) {
        if (StringUtils.isEmpty(name)) {
            return userGroupList.stream()
                    .sorted((v1, v2) -> CommonUtils.sort(v1.getId(), v2.getId(), true))
                    .collect(Collectors.toList());
        }
        return userGroupList.stream()
                .filter(it -> it.getName().contains(name))
                .sorted((v1, v2) -> CommonUtils.sort(v1.getId(), v2.getId(), true))
                .collect(Collectors.toList());
    }
}


