package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.common.model.model.EntityWithName;

/**
 * Attachment entity for fusion framework
 */
@ModelLabel("闄勪欢")
public class Attachment extends EntityWithName {
    private String fileName;
    private String filePath;
    private String fileType;
    private Long fileSize;
    private String businessId;
    private String businessType;
    
    // Getters and setters
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    
    public String getFilePath() { return filePath; }
    public void setFilePath(String filePath) { this.filePath = filePath; }
    
    public String getFileType() { return fileType; }
    public void setFileType(String fileType) { this.fileType = fileType; }
    
    public Long getFileSize() { return fileSize; }
    public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
    
    public String getBusinessId() { return businessId; }
    public void setBusinessId(String businessId) { this.businessId = businessId; }
    
    public String getBusinessType() { return businessType; }
    public void setBusinessType(String businessType) { this.businessType = businessType; }
}