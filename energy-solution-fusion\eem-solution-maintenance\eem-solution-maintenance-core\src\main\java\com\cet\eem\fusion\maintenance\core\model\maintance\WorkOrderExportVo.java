package com.cet.eem.fusion.maintenance.core.model.maintance;

import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/6/2
 */
@Getter
@Setter
public class WorkOrderExportVo extends WorkOrderVo {
    @ApiModelProperty("开始时间字符串格式")
    private String startTimeStr;

    @ApiModelProperty("计划时间字符串格式")
    private String timeConsumePlanStr;

    public String getStartTimeStr() {
        return TimeUtil.format(this.createTime, TimeUtil.LONG_TIME_FORMAT);
    }

    public String getTimeConsumePlanStr() {
        return TimeUtil.parseTime(this.timeConsumePlan);
    }
}


