package com.cet.eem.fusion.maintenance.core.common.model.enumeration.subject.powermaintenance;

/**
 * Maintenance type enumeration
 */
public enum MaintenanceType {
    PREVENTIVE("preventive", "Preventive"),
    CORRECTIVE("corrective", "Corrective"),
    PREDICTIVE("predictive", "Predictive"),
    EMERGENCY("emergency", "Emergency");
    
    private final String code;
    private final String description;
    
    MaintenanceType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() { return code; }
    public String getDescription() { return description; }
}