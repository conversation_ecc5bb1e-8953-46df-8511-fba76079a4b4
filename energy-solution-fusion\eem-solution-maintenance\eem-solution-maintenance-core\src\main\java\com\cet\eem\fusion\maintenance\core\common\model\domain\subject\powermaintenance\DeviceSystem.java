package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

/**
 * @Author: jiangzixuan
 * @Description:
 * @Data: Created in 2021-05-12
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.DEVICE_SYSTEM)
public class DeviceSystem extends EntityWithName {
    @JsonProperty("projectid")
    private Long projectId;

    public DeviceSystem(){
        this.modelLabel=ModelLabelDef.DEVICE_SYSTEM;
    }

    public DeviceSystem(Long id, String name) {
        this.id = id;
        this.name = name;
        this.modelLabel=ModelLabelDef.DEVICE_SYSTEM;
    }
}
