package com.cet.eem.fusion.maintenance.core.controller.device;

import com.cet.eem.fusion.maintenance.core.controller.bff.device.DeviceManageBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-08
 */
@Api(value = "/eem/v1/device", tags = "设备管理接口")
@RequestMapping(value = "/eem/solution/maintenance/eem/v1/device")
@RestController
@Validated
public class DeviceManageController extends DeviceManageBffController {

}


