package com.cet.eem.fusion.maintenance.core.model.devicemanage.component;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(value = "AddSparePartsDevice", description = "新增设备")
public class AddSparePartsDevice {
    @NotNull(message = "设备规格不能为空")
    private String model;
    @Length(max = 50,message = "设备名称长度不能超过50")
    @NotNull(message = "设备名称不能为空")
    private String name;
    @NotNull(message = "设备类型对应的id不能为空")
    private Long objectLabelId;
    @NotNull(message = "系统id不能为空")
    private Long deviceSystemId;

    /**
     * 所属公司
     */
    private String company;

    /**
     * 关键技术参数
     */
    private String criticalParams;

    /**
     * 二级库备品备件id
     */
    private Long secondarySparePartsId;

}

