# 最终融合适配脚本
Write-Host "Starting final fusion adaptation based on 02能管融合适配任务.md..." -ForegroundColor Green

$coreDir = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$javaFiles = Get-ChildItem -Path $coreDir -Filter "*.java" -Recurse

$modifiedCount = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $modified = $false
    
    # 1. 方法调用替换 - getProjectId() 改为 getTenantId()
    if ($content -contains "getProjectId()") {
        $content = $content -replace "getProjectId\(\)", "getTenantId()"
        $modified = $true
    }
    
    # 2. 常量替换
    if ($content -contains "TableNameDef.") {
        $content = $content -replace "TableNameDef\.", "ModelLabelDef."
        $modified = $true
    }
    
    # 3. 基础实体类替换
    if ($content -contains "extends BaseEntity") {
        $content = $content -replace "extends BaseEntity", "extends EntityWithName"
        $modified = $true
    }
    
    # 4. 查询构建器替换
    if ($content -contains "QueryConditionBuilder") {
        $content = $content -replace "QueryConditionBuilder", "ParentQueryConditionBuilder"
        $modified = $true
    }
    
    # 5. 返回类型替换
    if ($content -contains "public ResultWithTotal<") {
        $content = $content -replace "public ResultWithTotal<", "public ApiResult<"
        $modified = $true
    }
    
    if ($content -contains "public Result<") {
        $content = $content -replace "public Result<", "public ApiResult<"
        $modified = $true
    }
    
    # 如果内容有修改，写回文件
    if ($modified) {
        try {
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
            $modifiedCount++
            Write-Host "Modified: $($file.Name)" -ForegroundColor Green
        }
        catch {
            Write-Host "Failed: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "Final fusion adaptation completed! Total files modified: $modifiedCount" -ForegroundColor Green
