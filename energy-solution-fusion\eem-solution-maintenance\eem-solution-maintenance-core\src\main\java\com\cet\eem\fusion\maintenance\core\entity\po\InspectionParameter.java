package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : InspectionParameter
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-13 20:19
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.INSPECTION_PARAMETER)
public class InspectionParameter extends EntityWithName {
    /**
     * 操作
     */
    private String operate;

    /**
     * 项目id
     */
    @JsonProperty("projectid")
    private Long projectId;

    /**
     * 参数类型
     * 1 状态量
     * 2 模拟量
     */
    private Integer type;

    /**
     * 单位类型
     */
    private Integer unitSymbol;

    public InspectionParameter() {
        this.modelLabel = ModelLabelDef.INSPECTION_PARAMETER;
    }
}
