# 鉴权相关修改脚本 - 基于03其他修改任务.md
Write-Host "Starting authentication modifications..." -ForegroundColor Green

$coreDir = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$javaFiles = Get-ChildItem -Path $coreDir -Filter "*.java" -Recurse

$modifiedCount = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $modified = $false
    
    # 1. EemCloudAuthService 修改
    if ($content -match "import com\.cet\.eem\.service\.EemCloudAuthService;") {
        $content = $content -replace "import com\.cet\.eem\.service\.EemCloudAuthService;", "import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;`nimport com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;"
        $modified = $true
        Write-Host "Updated EemCloudAuthService import in: $($file.Name)" -ForegroundColor Yellow
    }
    
    # EemCloudAuthService 注入修改
    if ($content -match "@Resource\s+private\s+EemCloudAuthService\s+cloudAuthService;") {
        $content = $content -replace "@Resource\s+private\s+EemCloudAuthService\s+cloudAuthService;", "@Resource`nUserRestApi userRestApi;"
        $modified = $true
        Write-Host "Updated EemCloudAuthService injection in: $($file.Name)" -ForegroundColor Yellow
    }
    
    # queryUserBatch 方法调用修改
    if ($content -match "cloudAuthService\.queryUserBatch\(([^)]+)\)") {
        $content = $content -replace "Result<List<UserVo>>\s+listResult\s*=\s*cloudAuthService\.queryUserBatch\(([^)]+)\);\s*List<UserVo>\s+userInfoList\s*=\s*listResult\.getData\(\);", "ApiResultI18n<List<UserVo>> userQueryResult = userRestApi.getUsers(`$1);`nList<UserVo> userInfoList = userQueryResult.getData();"
        $modified = $true
        Write-Host "Updated queryUserBatch method call in: $($file.Name)" -ForegroundColor Yellow
    }
    
    # 2. 节点权限校验修改
    if ($content -match "import com\.cet\.eem\.auth\.service\.NodeAuthCheckService;") {
        $content = $content -replace "import com\.cet\.eem\.auth\.service\.NodeAuthCheckService;", "import com.cet.eem.fusion.config.sdk.auth.service.NodeAuthCheckService;`nimport com.cet.eem.fusion.common.def.i18n.DataMaintainLangKeyDef;"
        $modified = $true
        Write-Host "Updated NodeAuthCheckService import in: $($file.Name)" -ForegroundColor Yellow
    }
    
    # checkPartAuth 方法调用修改
    if ($content -match "nodeAuthBffService\.checkPartAuth\(([^,]+),\s*GlobalInfoUtils\.getUserId\(\)\)") {
        $content = $content -replace "if\s*\(\s*!nodeAuthBffService\.checkPartAuth\(([^,]+),\s*GlobalInfoUtils\.getUserId\(\)\)\s*\)\s*\{[^}]*throw new ValidationException\([^)]*\);[^}]*\}", "ParentParam parentParam = new ParentParam();`nparentParam.setRootNode(new BaseEntity(`$1.getId(),`$1.getModelLabel()));`nparentParam.setTenantId(GlobalInfoUtils.getTenantId());`nparentParam.setUserId(GlobalInfoUtils.getUserId());`nif (!nodeAuthBffService.checkCompleteOrganizationNodes(parentParam)) {`n    throw new ValidationException(LanguageUtil.getMessage(DataMaintainLangKeyDef.Connect.NO_COMPLETE_AUTH));`n}"
        $modified = $true
        Write-Host "Updated checkPartAuth method call in: $($file.Name)" -ForegroundColor Yellow
    }
    
    # 3. AuthUtils 修改
    if ($content -match "import com\.cet\.eem\.auth\.service\.AuthUtils;") {
        $content = $content -replace "import com\.cet\.eem\.auth\.service\.AuthUtils;", "import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;`nimport com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;"
        $modified = $true
        Write-Host "Updated AuthUtils import in: $($file.Name)" -ForegroundColor Yellow
    }
    
    # AuthUtils 注入修改
    if ($content -match "@Autowired\s+AuthUtils\s+authUtils;") {
        $content = $content -replace "@Autowired\s+AuthUtils\s+authUtils;", "@Resource`nUserRestApi userRestApi;"
        $modified = $true
        Write-Host "Updated AuthUtils injection in: $($file.Name)" -ForegroundColor Yellow
    }
    
    # queryAndCheckUser 方法调用修改
    if ($content -match "authUtils\.queryAndCheckUser\(([^)]+)\)") {
        $content = $content -replace "UserVo\s+user\s*=\s*authUtils\.queryAndCheckUser\(([^)]+)\);", "ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(`$1);`nUserVo user = usrRes.getData();"
        $modified = $true
        Write-Host "Updated queryAndCheckUser method call in: $($file.Name)" -ForegroundColor Yellow
    }
    
    # 4. GlobalInfoUtils.getHttpResponse() 修改
    if ($content -match "GlobalInfoUtils\.getHttpResponse\(\)") {
        Write-Host "Found GlobalInfoUtils.getHttpResponse() usage in: $($file.Name) - Manual intervention required" -ForegroundColor Red
        # 这个需要手动修改，因为需要在方法参数中添加 HttpServletResponse response
    }
    
    # 如果内容有修改，写回文件
    if ($modified -and $content -ne $originalContent) {
        try {
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
            $modifiedCount++
            Write-Host "Modified file: $($file.Name)" -ForegroundColor Green
        }
        catch {
            Write-Host "Failed to modify file: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "Authentication modifications completed! Total files modified: $modifiedCount" -ForegroundColor Green
