package com.cet.eem.fusion.maintenance.core.service.device.impl;

import com.cet.eem.fusion.common.service.auth.NodeAuthCheckService;
import com.cet.eem.fusion.common.service.auth.NodeManageWithAuthService;
import com.cet.eem.fusion.common.service.auth.node.ManageNodeMobileService;
import com.cet.eem.bll.common.dao.powersystem.DeviceCommonInfoDao;
import com.cet.eem.bll.common.def.ProjectLabel;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.object.powersystem.BusBarConnectorVo;
import com.cet.eem.bll.common.model.domain.object.powersystem.DeviceCommonInfo;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.MeasureNode;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.RunningParamNode;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.fusion.maintenance.core.bll.common.model.node.EemNodeFieldInfo;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.devicemanager.*;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.MeasureNodeGroupWithLayer;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.*;
import com.cet.eem.fusion.maintenance.core.service.device.TemplateService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.def.common.SplitCharDef;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.common.utils.ArrayUtils;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.maintenance.core.common.model.base.SingleModelConditionDTO;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.TreeIdUtils;
import com.cet.futureblue.i18n.LanguageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-11
 */
@Service
public class TemplateServiceImpl implements TemplateService {

    @Autowired
    private TemplateNodeTreeDao templateNodeTreeDao;
    @Autowired
    private TemplateDao templateDao;
    @Autowired
    private TemplateGroupDao templateGroupDao;
    @Autowired
    private RunningParamNodeDao runningParamNodeDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    CommonUtilsService commonUtilsService;
    @Autowired
    DeviceCommonInfoDao deviceCommonInfoDao;
    @Autowired
    MeasureNodeGroupDao measureNodeGroupDao;

    @Autowired
    protected NodeManageWithAuthService nodeManageBffService;

    @Autowired
    private NodeAuthCheckService nodeAuthBffService;

    @Autowired
    ManageNodeMobileService manageNodeMobileService;

    @Override
    public List<EquipmentNodeTreeVo> queryTemplateNodeTree() {
        List<EquipmentNodeTreeVo> result = new ArrayList<>();
        List<EquipmentNodeTreeDto> nodeTrees = templateNodeTreeDao.selectByProjectId(GlobalInfoUtils.getTenantId());
        buildNodeTree(result, nodeTrees);
        return result;
    }

    private void buildNodeTree(List<EquipmentNodeTreeVo> result, List<EquipmentNodeTreeDto> nodeTrees) {
        if (CollectionUtils.isNotEmpty(nodeTrees)) {
            List<EquipmentNodeTreeVo> vos = new ArrayList<>();
            nodeTrees.forEach(n -> {
                EquipmentNodeTreeVo vo = new EquipmentNodeTreeVo();
                BeanUtils.copyProperties(n, vo);
                vos.add(vo);
            });
            packageResult(result, vos);
        }
    }

    @Override
    public EquipmentNodeTreeDto insertTemplateNodeTree(EquipmentNodeTreeDto data) {
        data.setProjectId(GlobalInfoUtils.getTenantId());
        checkNameRepeatWhileCreate(data);
        data.setModelLabel(ModelLabelDef.TEMPLATE_NODE_TREE);
        templateNodeTreeDao.insert(data);
        commonUtilsService.writeAddOperationLogs(OperationLogType.NODE_TEMPLATE, "创建设备模板类型", data);
        return data;
    }

    @Override
    public EquipmentNodeTreeDto editTemplateNodeTree(EquipmentNodeTreeDto data) {
        checkNameRepeatWhileUpdate(data);
        templateNodeTreeDao.updateById(data);
        commonUtilsService.writeUpdateOperationLogs(OperationLogType.NODE_TEMPLATE, "更新设备模板类型", data);
        return data;
    }

    @Override
    public void deleteNode(List<Long> ids) {
        List<NodeWithTemplate> nodeWithTemplates = templateNodeTreeDao.queryNodeTemplate(ids);
        if (CollectionUtils.isEmpty(nodeWithTemplates)) {
            return;
        }
        //判断该节点是否为父节点，如果是则判断该节点下是否存在分组，如果存在，则无法直接删除该节点
        List<Long> parentIds = new ArrayList<>();
        for (NodeWithTemplate node : nodeWithTemplates) {
            //判断其是否有ParentId，如果没有表示为父节点，则将父节点的id添加到集合中
            if (Objects.isNull(node.getParentId())) {
                parentIds.add(node.getId());
            }
            List<AttributeTemplate> templateList = node.getTemplateList();
            Assert.isTrue(CollectionUtils.isEmpty(templateList), "分组下存在模板,无法删除");
        }
        //根据parentId进行查询数据库，如果返回不为空，表示父节点下有分组
        if (CollectionUtils.isNotEmpty(parentIds)) {
            List<EquipmentNodeTreeDto> equipmentNodeTreeDtos = templateNodeTreeDao.selectByParentIds(parentIds);
            Assert.isTrue(CollectionUtils.isEmpty(equipmentNodeTreeDtos), "节点树下存在分组,无法删除");
        }
        templateNodeTreeDao.deleteBatchIds(ids);
        commonUtilsService.writeDeleteOperationLogs(OperationLogType.NODE_TEMPLATE, "删除模板节点信息", new Object[]{ids});
    }

    private List<EntityWithName> packageDeleteData(List<Long> ids, String label) {
        List<EntityWithName> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.EMPTY_LIST;
        }
        ids.forEach(id -> {
            EntityWithName EntityWithName = new EntityWithName();
            EntityWithName.setId(id);
            EntityWithName.setModelLabel(label);
            result.add(EntityWithName);
        });
        return result;
    }


    @Override
    public void writeTemplate(AttributeTemplate template, Long parentId) {
        if (template.getId() == null) {
            checkTemplateRepeat(template, parentId);
        }
        List<AttributeTemplate> attributeTemplates = modelServiceUtils.writeData(template, AttributeTemplate.class);
        modelServiceUtils.writeRelations(parentId, ModelLabelDef.TEMPLATE_NODE_TREE, attributeTemplates.get(0).getId(),
                ModelLabelDef.NODE_TEMPLATE);
        commonUtilsService.writeAddOperationLogs(OperationLogType.NODE_TEMPLATE, "创建模板信息", template);
    }

    private List<RunningParam> assembleRunningParam(List<RunningParam> runningParamList) {
        if (CollectionUtils.isEmpty(runningParamList)) {
            return Collections.emptyList();
        }
        Set<Long> dataIds = runningParamList.stream().map(RunningParam::getDataId).collect(Collectors.toSet());
        LambdaQueryWrapper<MeasureNode> wrapper = LambdaQueryWrapper.of(MeasureNode.class)
                .in(MeasureNode::getDataId, dataIds);
        List<MeasureNodeGroupWithLayer> measureNodeGroupWithLayers = measureNodeGroupDao.selectRelatedList(MeasureNodeGroupWithLayer.class, null, Collections.singletonList(wrapper));
        if (CollectionUtils.isEmpty(measureNodeGroupWithLayers)) {
            return Collections.emptyList();
        }
        List<RunningParam> runningParams = new ArrayList<>();
        for (MeasureNodeGroupWithLayer measureNodeGroupWithLayer : measureNodeGroupWithLayers) {
            if (CollectionUtils.isNotEmpty(measureNodeGroupWithLayer.getList())) {
                for (MeasureNodeDto measureNode : measureNodeGroupWithLayer.getList()) {
                    RunningParam runningParam = new RunningParam();
                    runningParam.setMeasureId(measureNodeGroupWithLayer.getId());
                    runningParam.setDataId(measureNode.getDataId());
                    runningParams.add(runningParam);
                }


            }
        }
        List<RunningParam> result = new ArrayList<>();
        for (RunningParam runningParam : runningParamList) {
            for (RunningParam runningParamNow : runningParams) {
                if (runningParam.getDataId().equals(runningParamNow.getDataId())) {
                    RunningParam runningParam1 = new RunningParam();
                    BeanUtils.copyProperties(runningParam, runningParam1);
                    runningParam1.setMeasureId(runningParamNow.getMeasureId());
                    result.add(runningParam1);
                }
            }
        }

        return result;
    }

    private List<AttributeTemplateVo> assembleRunningGroup(List<AttributeTemplateVo> result, List<RunningParam> runningParams) {
        for (RunningParam runningParam : runningParams) {
            result.stream().filter(attributeTemplateVo -> CollectionUtils.isNotEmpty(attributeTemplateVo.getRunningParam())).flatMap(attributeTemplateVo -> attributeTemplateVo.getRunningParam().stream())
                    .filter(runningParamGroup -> CollectionUtils.isNotEmpty(runningParamGroup.getRunningParams()))
                    .flatMap(runningParamGroup -> runningParamGroup.getRunningParams().stream())
                    .filter(runningParam1 -> runningParam1.getDataId().equals(runningParam.getDataId()))
                    .map(runningParam1 -> {
                        runningParam1.setMeasureId(runningParam.getMeasureId());
                        return true;
                    }).collect(Collectors.toList());
        }

        return result;
    }

    @Override
    public List<AttributeTemplateVo> queryTemplates(Long id) {
        List<NodeWithTemplate> nodeWithTemplates = templateNodeTreeDao.queryNodeWithTemplates(id);
        return getAttributeTemplateVos(nodeWithTemplates);
    }

    private List<AttributeTemplateVo> getAttributeTemplateVos(List<NodeWithTemplate> nodeWithTemplates) {
        NodeWithTemplate nodeWithTemplate = nodeWithTemplates.stream().findFirst().orElse(null);
        if (Objects.isNull(nodeWithTemplate)) {
            return Collections.emptyList();
        }
        List<AttributeTemplate> templateList = nodeWithTemplate.getTemplateList();
        List<AttributeTemplateVo> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(templateList)) {
            packageTemplateResult(result, templateList);
        }
        List<RunningParam> runningParamList = result.stream().filter(attributeTemplateVo -> CollectionUtils.isNotEmpty(attributeTemplateVo.getRunningParam())).flatMap(attributeTemplateVo -> attributeTemplateVo.getRunningParam().stream())
                .filter(runningParamGroup -> CollectionUtils.isNotEmpty(runningParamGroup.getRunningParams()))
                .flatMap(runningParamGroup -> runningParamGroup.getRunningParams().stream()).collect(Collectors.toList());
        List<RunningParam> runningParams = assembleRunningParam(runningParamList);
        List<AttributeTemplateVo> attributeTemplateVos = assembleRunningGroup(result, runningParams);
        List<AttributeTemplateVo> sortResult = new ArrayList<>();
        for (AttributeTemplateVo attributeTemplateVo : attributeTemplateVos) {
            AttributeTemplateVo attributeTemplateVo1 = new AttributeTemplateVo();
            BeanUtils.copyProperties(attributeTemplateVo, attributeTemplateVo1);
            if (CollectionUtils.isNotEmpty(attributeTemplateVo.getTechParams())) {
                List<TechParam> collect = attributeTemplateVo.getTechParams().stream().sorted(Comparator.comparing(TechParam::getId)).collect(Collectors.toList());
                attributeTemplateVo1.setTechParams(collect);
            }
            sortResult.add(attributeTemplateVo1);
        }
        return sortResult;
    }

    @Override
    public ApiResult<List<AttributeTemplateVo>> queryTemplates(QueryTemplatesSearchVO searchV0) {
        ApiResult<List<NodeWithTemplate>> listResultWithTotal = templateNodeTreeDao.queryNodeWithTemplates(searchV0.getId(), searchV0.getPage());
        if (!listResultWithTotal.isSuccess()) {
            return Result.error(listResultWithTotal.getMsg());
        }
        List<AttributeTemplateVo> attributeTemplateVoList = getAttributeTemplateVos(listResultWithTotal.getData());
        return Result.ok(attributeTemplateVoList, listResultWithTotal.getTotal());
    }

    @Override
    public void deleteTemplate(Long id, Long parentId) {
        AttributeTemplate template = getTemplate(id);
        if (Objects.isNull(template)) {
            return;
        }
        List<DeviceCommonInfo> infos = deviceCommonInfoDao.queryByTemplateId(id);
        Assert.isTrue(CollectionUtils.isEmpty(infos), "有设备套用了该模板,无法删除");
        List<String> subNodes = Arrays.asList(ModelLabelDef.TECH_PARAM_TEMPLATE, ModelLabelDef.RUNNING_PARAM_TEMPLATE_GROUP, ModelLabelDef.RUNNING_PARAM);
        modelServiceUtils.delete(id, ModelLabelDef.NODE_TEMPLATE, subNodes);
        commonUtilsService.writeDeleteOperationLogs(OperationLogType.NODE_TEMPLATE, "删除模板信息", id);
    }


    private void deleteTechParam(AttributeTemplate original, AttributeTemplate update) {
        List<Long> ids;
        if (CollectionUtils.isNotEmpty(update.getTechParams())) {
            List<Long> updateIds = update.getTechParams().stream().map(TechParam::getId).collect(Collectors.toList());
            ids = original.getTechParams().stream().filter(l -> updateIds.contains(l.getId()) == false).map(TechParam::getId).collect(Collectors.toList());
        } else {
            ids = original.getTechParams().stream().map(TechParam::getId).collect(Collectors.toList());
        }
        List<EntityWithName> baseEntities = packageDeleteData(ids, ModelLabelDef.TECH_PARAM_TEMPLATE);
        templateDao.deleteChild(original.getId(), baseEntities);
    }

    @Override
    public List<RunningParamNodeVo> queryRunningParamNodeTree() {
        List<RunningParamNodeVo> result = new ArrayList<>();
        List<RunningParamNode> nodeTrees = runningParamNodeDao.selectAll();
        buildParamNodeTree(result, nodeTrees);
        return result;
    }

    @Override
    public List<MeasureNode> queryRunningParam(Long id) {
        NodeWithMeasureNode nodeWithMeasureNode = runningParamNodeDao.nodeWithMeasureNode(id);
        if (Objects.isNull(nodeWithMeasureNode)) {
            return Collections.EMPTY_LIST;
        }
        List<MeasureNode> params = nodeWithMeasureNode.getParams();
        if (CollectionUtils.isNotEmpty(params)) {
            params.forEach(measureNode -> measureNode.setName(LanguageUtil.getMessage(measureNode.getModelLabel() + SplitCharDef.Point + measureNode.getDataId())));
        }
        return params;
    }

    @Override
    public void updateTemplate(AttributeTemplate update, Long parentId) {
        checkExtTemplateRepeat(update, parentId);
        //更新前判断删除技术参数，运行参数，运行参数分组
        AttributeTemplate original = getTemplate(update.getId());
        if (Objects.isNull(original)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(original.getTechParams())) {
            deleteTechParam(original, update);
        }
        if (CollectionUtils.isNotEmpty(original.getRunningParam())) {
            deleteGroup(update, original);
        }
        writeTemplate(update, parentId);
        commonUtilsService.writeUpdateOperationLogs(OperationLogType.NODE_TEMPLATE, "更新模板信息", update);
    }

    private void checkExtTemplateRepeat(AttributeTemplate update, Long parentId) {
        NodeWithTemplate nodeWithTemplate = templateNodeTreeDao.checkRepeat(update, parentId);
        Assert.isNull(nodeWithTemplate, "模板名称重复");
        checkTemplateParam(update);
    }

    private void deleteGroup(AttributeTemplate update, AttributeTemplate original) {
        //先删除组内运行参数再删除组数据
        deleteRparamInExtGroup(update, original);
        List<Long> updateGroupIds = update.getRunningParam().stream().map(RunningParamGroup::getId).collect(Collectors.toList());
        List<RunningParamGroup> deleteGroup = original.getRunningParam().stream().filter(group -> !updateGroupIds.contains(group.getId())).collect(Collectors.toList());
        deleteGroup.forEach(group -> {
            if (CollectionUtils.isNotEmpty(group.getRunningParams())) {
                List<Long> deleteRParams = group.getRunningParams().stream().map(RunningParam::getId).collect(Collectors.toList());
                List<EntityWithName> baseEntities = packageDeleteData(deleteRParams, ModelLabelDef.RUNNING_PARAM);
                templateGroupDao.deleteChild(group.getId(), baseEntities);
            }
        });
        List<Long> deleteGroupIds = deleteGroup.stream().map(TemplateGroupDto::getId).collect(Collectors.toList());
        List<EntityWithName> baseEntities = packageDeleteData(deleteGroupIds, ModelLabelDef.RUNNING_PARAM_TEMPLATE_GROUP);
        templateDao.deleteChild(update.getId(), baseEntities);

    }

    private void deleteRparamInExtGroup(AttributeTemplate update, AttributeTemplate original) {
        if (CollectionUtils.isNotEmpty(update.getRunningParam())) {
            update.getRunningParam().forEach(updateGroup -> {
                original.getRunningParam().forEach(originGroup -> {
                    if (originGroup.getId().equals(updateGroup.getId())) {
                        deleteRparam(originGroup, updateGroup);
                    }
                });
            });
        }
    }

    private void deleteRparam(RunningParamGroup originGroup, RunningParamGroup updateGroup) {
        if (CollectionUtils.isNotEmpty(originGroup.getRunningParams())) {
            List<Long> updateParamIds = updateGroup.getRunningParams().stream().map(RunningParam::getId).collect(Collectors.toList());
            List<Long> deleteParam = originGroup.getRunningParams().stream().filter(originParam -> !updateParamIds.contains(originParam.getId())).map(RunningParam::getId).collect(Collectors.toList());
            List<EntityWithName> baseEntities = packageDeleteData(deleteParam, ModelLabelDef.RUNNING_PARAM);
            templateGroupDao.deleteChild(originGroup.getId(), baseEntities);
        }
    }

    @Override
    public AttributeTemplate getTemplate(Long templateId) {
        return templateDao.getTemplates(Collections.singletonList(templateId)).get(0);
    }

    private void buildParamNodeTree(List<RunningParamNodeVo> result, List<RunningParamNode> nodeTrees) {
        if (CollectionUtils.isNotEmpty(nodeTrees)) {
            List<RunningParamNodeVo> vos = new ArrayList<>();
            nodeTrees.forEach(node -> {
                RunningParamNodeVo vo = new RunningParamNodeVo();
                BeanUtils.copyProperties(node, vo);
                vo.setName(LanguageUtil.getMessage(ModelLabelDef.MEASURE_NODE_GROUP + SplitCharDef.Point + vo.getId()));
                vos.add(vo);
            });
            packageParamNodeResult(result, vos);
        }
    }

    private void packageParamNodeResult(List<RunningParamNodeVo> result, List<RunningParamNodeVo> nodeTrees) {
        List<RunningParamNodeVo> parentNode = nodeTrees.stream().filter(n -> !ParamUtils.checkPrimaryKeyValid(n.getParentId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentNode)) {
            return;
        }
        parentNode.forEach(parent -> {
            parent = recursionParamNode(nodeTrees, parent);
            result.add(parent);
        });
    }

    private RunningParamNodeVo recursionParamNode(List<RunningParamNodeVo> nodeTrees, RunningParamNodeVo parentNode) {
        List<RunningParamNodeVo> childList = new ArrayList<>();
        for (RunningParamNodeVo node : nodeTrees) {
            if (node.getParentId() != null && node.getParentId().equals(parentNode.getId())) {
                childList.add(recursionParamNode(nodeTrees, node));
            }
            parentNode.setChildren(childList);
        }
        return parentNode;
    }


    private void packageTemplateResult(List<AttributeTemplateVo> result, List<AttributeTemplate> templateList) {
        templateList.forEach(template -> {
            AttributeTemplateVo attributeTemplateVo = new AttributeTemplateVo(template.getName(), template.getOpenprotect(),
                    template.getRunningParam(), template.getTechParams());
            attributeTemplateVo.setId(template.getId());
            attributeTemplateVo.setModelLabel(template.getModelLabel());
            attributeTemplateVo.setTechParamAmount(CollectionUtils.isEmpty(template.getTechParams()) ? 0 : template.getTechParams().size());
            if (CollectionUtils.isEmpty(template.getRunningParam())) {
                attributeTemplateVo.setRunningParamAmount(0);
            } else {
                int size = template.getRunningParam().stream().filter(group -> CollectionUtils.isNotEmpty(group.getRunningParams())).
                        flatMap(paramGroup -> paramGroup.getRunningParams().stream()).collect(Collectors.toList()).size();
                attributeTemplateVo.setRunningParamAmount(size);
            }
            result.add(attributeTemplateVo);
        });
    }


    private void checkTemplateRepeat(AttributeTemplate template, Long parentId) {
        NodeWithTemplate nodeWithTemplate = templateNodeTreeDao.NodeWithTemplateName(template, parentId);
        Assert.isNull(nodeWithTemplate, "模板名称重复");
        checkTemplateParam(template);
    }

    private void checkTemplateParam(AttributeTemplate template) {
        if (CollectionUtils.isNotEmpty(template.getRunningParam())) {
            Long count = template.getRunningParam().stream().map(RunningParamGroup::getName).distinct().count();
            Assert.isTrue(count.intValue() == template.getRunningParam().size(), "存在同名分组，无法创建");
            template.getRunningParam().forEach(group -> {
                checkRunningParamRepeat(group);
            });
        }
        checkTechParams(template);
    }

    private void checkTechParams(AttributeTemplate template) {
        if (CollectionUtils.isNotEmpty(template.getTechParams())) {
            Long count = template.getTechParams().stream().map(TechParam::getName).distinct().count();
            Assert.isTrue(count.intValue() == template.getTechParams().size(), "存在同名技术参数，无法创建");
        }
    }

    private void checkRunningParamRepeat(RunningParamGroup group) {
        if (CollectionUtils.isNotEmpty(group.getRunningParams())) {
            Long count = group.getRunningParams().stream().map(RunningParam::getName).distinct().count();
            Assert.isTrue(count.intValue() == group.getRunningParams().size(), "分组下存在同名运行参数，无法创建");
        }
    }


    private void checkNameRepeatWhileUpdate(EquipmentNodeTreeDto data) {
        EquipmentNodeTreeDto dto = templateNodeTreeDao.selectByNameAndParentIdAndId(data);
        Assert.isNull(dto, "节点参数名重复");
    }

    private void checkNameRepeatWhileCreate(EquipmentNodeTreeDto data) {
        EquipmentNodeTreeDto dto = templateNodeTreeDao.selectByNameAndParentId(data);
        Assert.isNull(dto, "节点参数名重复");
        if (data.getParentId() != null) {
            EquipmentNodeTreeDto parent = templateNodeTreeDao.selectById(data.getParentId());
            Assert.isNull(parent.getParentId(), "新增节点失败，无法创建多层节点");
        }
    }

    private void packageResult(List<EquipmentNodeTreeVo> result, List<EquipmentNodeTreeVo> nodeTrees) {
        List<EquipmentNodeTreeVo> parentNode = nodeTrees.stream().filter(n -> n.getParentId() == null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentNode)) {
            return;
        }
        parentNode.forEach(parent -> {
            parent = recursionNode(nodeTrees, parent);
            result.add(parent);
        });
    }

    private EquipmentNodeTreeVo recursionNode(List<EquipmentNodeTreeVo> nodeTrees, EquipmentNodeTreeVo parentNode) {
        List<EquipmentNodeTreeVo> childList = new ArrayList<>();
        for (EquipmentNodeTreeVo node : nodeTrees) {
            if (node.getParentId() != null && node.getParentId().equals(parentNode.getId())) {
                childList.add(recursionNode(nodeTrees, node));
            }
            parentNode.setChildren(childList);
        }
        return parentNode;
    }

    @Override
    public List<EquipmentNodeTreeVo> queryAllTemplateNodeTree() {
        List<NodeWithTemplate> nodeWithTemplates = templateNodeTreeDao.queryNodeTemplateByProject(GlobalInfoUtils.getTenantId());
        return buildNodeTree(nodeWithTemplates, null);
    }

    private List<EquipmentNodeTreeVo> buildNodeTree(List<NodeWithTemplate> allTemplateTree, Long parentId) {
        List<EquipmentNodeTreeVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(allTemplateTree)) {
            return result;
        }

        // 过滤子层级的节点
        List<NodeWithTemplate> templates = allTemplateTree.stream().filter(it -> {
            if (!ParamUtils.checkPrimaryKeyValid(parentId)) {
                return !ParamUtils.checkPrimaryKeyValid(it.getParentId());
            }

            return Objects.equals(parentId, it.getParentId());
        }).collect(Collectors.toList());
        if (templates.isEmpty()) {
            return result;
        }

        for (NodeWithTemplate template : templates) {
            EquipmentNodeTreeVo node = new EquipmentNodeTreeVo();
            BeanUtils.copyProperties(template, node);
            TreeIdUtils.setTreeId(node);

            // 处理模板节点树
            if (CollectionUtils.isEmpty(template.getTemplateList())) {
                List<EquipmentNodeTreeVo> childTemplates = buildNodeTree(allTemplateTree, template.getId());
                node.setChildren(childTemplates);
                result.add(node);
                continue;
            }

            result.add(node);
            // 处理模板记录
            List<EquipmentNodeTreeVo> childTemplates = new ArrayList<>();
            List<AttributeTemplate> attributeTemplates = template.getTemplateList();
            for (AttributeTemplate attributeTemplate : attributeTemplates) {
                EquipmentNodeTreeVo equipmentNodeTree = new EquipmentNodeTreeVo();
                equipmentNodeTree.setName(attributeTemplate.getName());
                equipmentNodeTree.setId(attributeTemplate.getId());
                equipmentNodeTree.setModelLabel(attributeTemplate.getModelLabel());
                TreeIdUtils.setTreeId(equipmentNodeTree);
                childTemplates.add(equipmentNodeTree);
            }
            node.setChildren(childTemplates);
        }

        return result;
    }

    @Override
    public Map<Long, List<BaseVo>> queryNodeTemplateWithParentNode(Long projectId, Collection<Long> nodeTemplateId) {
        List<NodeWithTemplate> nodeWithTemplates = templateNodeTreeDao.queryNodeTemplateByProject(projectId);
        List<EquipmentNodeTreeVo> nodeTree = buildNodeTree(nodeWithTemplates, null);
        List<List<BaseVo>> nodeList = getNode(nodeTree);

        Map<Long, List<BaseVo>> result = new HashMap<>(nodeTemplateId.size());
        for (List<BaseVo> node : nodeList) {
            BaseVo obj = node.get(0);
            if (!Objects.equals(obj.getModelLabel(), ModelLabelDef.NODE_TEMPLATE)) {
                continue;
            }

            if (!nodeTemplateId.contains(obj.getId())) {
                continue;
            }

            result.put(obj.getId(), node);
        }

        return result;
    }

    @Override
    public Map<Long, String> queryNodeTemplateParentPath(Long projectId, Collection<Long> nodeTemplateId) {
        Map<Long, List<BaseVo>> parentNodePathMap = queryNodeTemplateWithParentNode(projectId, nodeTemplateId);
        Map<Long, String> result = new HashMap<>();
        parentNodePathMap.forEach((key, val) -> {
            Collections.reverse(val);
            List<String> names = val.stream().map(BaseVo::getName).collect(Collectors.toList());
            result.put(key, ArrayUtils.join(names, "/"));
        });

        return result;
    }

    public List<List<BaseVo>> getNode(@NotNull List<EquipmentNodeTreeVo> nodeTree) {
        List<List<BaseVo>> result = new ArrayList<>();
        for (EquipmentNodeTreeVo obj : nodeTree) {
            if (CollectionUtils.isEmpty(obj.getChildren())) {
                List<BaseVo> tmpResult = new ArrayList<>();
                tmpResult.add(new BaseVo(obj.getId(), obj.getModelLabel(), obj.getName()));
                result.add(tmpResult);
                continue;
            }

            List<List<BaseVo>> tmpResult = getNode(obj.getChildren());
            tmpResult.forEach(it -> it.add(new BaseVo(obj.getId(), obj.getModelLabel(), obj.getName())));
            result.addAll(tmpResult);
        }
        return result;
    }

    @Override
    public List<EemNodeFieldInfo> queryNodeInfoByNodeFieldDefWithTemplate(BaseVo node) {
        ParentParam parentParam = new ParentParam();
parentParam.setRootNode(new BaseEntity(node.getId(),node.getModelLabel()));
parentParam.setTenantId(GlobalInfoUtils.getTenantId());
parentParam.setUserId(GlobalInfoUtils.getUserId());
if (!nodeAuthBffService.checkCompleteOrganizationNodes(parentParam)) {
    throw new ValidationException(LanguageUtil.getMessage(DataMaintainLangKeyDef.Connect.NO_COMPLETE_AUTH));
}
        List<EemNodeFieldInfo> eemNodeFieldInfos = manageNodeMobileService.queryNodeInfoByNodeFieldDef(node);

        //获取设备套用模板信息
        List<DeviceCommonInfo> deviceCommonInfos = deviceCommonInfoDao.queryDeviceInfoByObjects(Collections.singletonList(node));
        List<Long> collect = deviceCommonInfos.stream().map(DeviceCommonInfo::getNodetemplateid).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, String> nodeTemplateAndParentNode = queryNodeTemplateParentPath(GlobalInfoUtils.getTenantId(), collect);
        List<EemNodeFieldInfo> filterEemNodeFieldInfo = eemNodeFieldInfos.stream().filter(x -> ColumnDef.NODE_TEMPLATE_ID.equals(x.getName())).collect(Collectors.toList());
        EemNodeFieldInfo eemNodeFieldInfo = new EemNodeFieldInfo();
        if (CollectionUtils.isNotEmpty(filterEemNodeFieldInfo)) {
            EemNodeFieldInfo eemNodeFieldInfoTemplate = filterEemNodeFieldInfo.get(0);
            if (Objects.nonNull(eemNodeFieldInfoTemplate.getShowValue())) {
                Long templateId = Long.valueOf(String.valueOf(eemNodeFieldInfoTemplate.getShowValue()));
                String templatePath = nodeTemplateAndParentNode.getOrDefault(templateId, "");
                eemNodeFieldInfo.setShowValue(templatePath);
            }
        }
        eemNodeFieldInfo.setName(ColumnDef.NODE_TEMPLATE_PATH);
        eemNodeFieldInfo.setDescription("设备套用模板");
        eemNodeFieldInfos.add(eemNodeFieldInfo);

        String longitude = CommonUtils.BLANK_STR;
        String latitude = CommonUtils.BLANK_STR;
        for (EemNodeFieldInfo eemNodeFieldInfoSingle : eemNodeFieldInfos) {
            if (ProjectLabel.LabelColumn.LONGITUDE.equals(eemNodeFieldInfoSingle.getName()) && Objects.nonNull(eemNodeFieldInfoSingle.getShowValue())) {
                longitude = String.valueOf(eemNodeFieldInfoSingle.getShowValue());
            } else if (ProjectLabel.LabelColumn.LATITUDE.equals(eemNodeFieldInfoSingle.getName()) && Objects.nonNull(eemNodeFieldInfoSingle.getShowValue())) {
                latitude = String.valueOf(eemNodeFieldInfoSingle.getShowValue());
            }
        }
        if (Objects.nonNull(longitude) && Objects.nonNull(latitude)) {
            EemNodeFieldInfo lonLatEemNodeFieldInfo = new EemNodeFieldInfo();
            lonLatEemNodeFieldInfo.setName("lonlat");
            lonLatEemNodeFieldInfo.setDescription("经纬度");
            lonLatEemNodeFieldInfo.setShowValue(longitude + "," + latitude);
            eemNodeFieldInfos.add(lonLatEemNodeFieldInfo);
        }
        eemNodeFieldInfos.removeIf(s -> ProjectLabel.LabelColumn.LONGITUDE.equals(s.getName()) || ProjectLabel.LabelColumn.LATITUDE.equals(s.getName()));
        handleResult(eemNodeFieldInfos);
        return eemNodeFieldInfos;

    }

    /**
     * 补充母线名称
     *
     * @param eemNodeFieldInfos
     */
    private void handleResult(List<EemNodeFieldInfo> eemNodeFieldInfos) {
        EemQueryCondition condition = new EemQueryCondition();
        condition.setRootLabel(NodeLabelDef.PROJECT);
        condition.setRootID(GlobalInfoUtils.getTenantId());
        SingleModelConditionDTO singleModelConditionDTO = new SingleModelConditionDTO();
        singleModelConditionDTO.setModelLabel(NodeLabelDef.BUS_BAR_SECTION);
        condition.setTreeReturnEnable(true);
        condition.setSubLayerConditions(Collections.singletonList(singleModelConditionDTO));
        List<Map<String, Object>> maps = nodeManageBffService.queryNodeTree(condition, GlobalInfoUtils.getUserId());
        List list = (List) maps.get(0).get(ColumnDef.CHILDREN);
        List<BusBarConnectorVo> busBarConnectorVos = JsonTransferUtils.transferList(list, BusBarConnectorVo.class);
        for (EemNodeFieldInfo eemNodeFieldInfo : eemNodeFieldInfos) {
            if (Objects.equals(eemNodeFieldInfo.getName(), ColumnDef.BUS_BAR_SEG_I) || Objects.equals(eemNodeFieldInfo.getName(), ColumnDef.BUS_BAR_SEG_II)) {
                Map<Long, List<BusBarConnectorVo>> collect1 = busBarConnectorVos.stream().collect(Collectors.groupingBy(BusBarConnectorVo::getId));
                List<BusBarConnectorVo> busBarConnectorVos1 = collect1.get(Long.valueOf(String.valueOf(eemNodeFieldInfo.getOriginValue())));
                if (CollectionUtils.isNotEmpty(busBarConnectorVos1)) {
                    eemNodeFieldInfo.setShowValue(busBarConnectorVos1.get(0).getName());
                }
            }
        }
    }
}




