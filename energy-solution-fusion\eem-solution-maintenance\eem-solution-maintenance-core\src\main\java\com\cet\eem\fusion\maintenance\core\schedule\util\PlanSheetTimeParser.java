package com.cet.eem.fusion.maintenance.core.schedule.util;

import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.ExecuteStrategyType;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName : SheetPlanTimeParser
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 08:50
 */
@Getter
public class PlanSheetTimeParser {

    private static final Pattern TIME_PATTERN = Pattern.compile("P(\\d+)M(\\d+)DT(\\d+)H");

    private final String cycle;

    private Integer month;

    private Integer day;

    private Integer hours;

    private Integer executeStrategy;

    public PlanSheetTimeParser(String cycle, Integer executeStrategy) {
        Assert.isTrue(StringUtils.isNotEmpty(cycle), "巡检计划自定义时间周期不能为空");
        this.cycle = cycle;
        this.executeStrategy = executeStrategy;
        parseCycle();
        checkCycle();
    }

    private void parseCycle() {
        Matcher matcher = TIME_PATTERN.matcher(this.cycle);
        if (matcher.find()) {
            this.month = Integer.parseInt(matcher.group(1));
            this.day = Integer.parseInt(matcher.group(2));
            this.hours = Integer.parseInt(matcher.group(3));
        } else {
            throw new IllegalArgumentException(String.format("巡检计划ISO8601时间格式不合法:%s", this.cycle));
        }
    }

    private void checkCycle() {
        if(this.executeStrategy == null || this.executeStrategy != ExecuteStrategyType.OPERATING_PERIOD) {
            Assert.isTrue(this.hours >= 0 && this.hours <= 23, String.format("巡检计划自定义周期%s,小时设置为%s,超过23小时", this.cycle, this.hours));
        } else {
            Assert.isTrue(this.hours >= 0, String.format("巡检计划自定义周期%s,小时设置为%s, 小于0h", this.cycle, this.hours));
        }
        Assert.isTrue(this.day >= 0 && this.day <= 29, String.format("巡检计划自定义周期%s,天设置为%s,超过29天", this.cycle, this.day));
        Assert.isTrue(this.month >= 0 && this.month <= 99, String.format("巡检计划自定义周期%s,月设置为%s,超过99月", this.cycle, this.hours));
        Assert.isTrue(!(this.hours == 0 && this.day == 0 && this.month == 0), String.format("巡检计划自定义周期%s,小时天月不能全设置为0", this.cycle));
    }
}

