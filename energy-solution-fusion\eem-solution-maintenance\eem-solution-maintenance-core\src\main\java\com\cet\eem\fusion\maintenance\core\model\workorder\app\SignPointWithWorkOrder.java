package com.cet.eem.fusion.maintenance.core.model.workorder.app;

import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/20
 */
@ApiModel(description = "工单计数")
@Getter
@Setter
public class SignPointWithWorkOrder {
    @ApiModelProperty("签到点id")
    private Long registerPointId;

    @ApiModelProperty("签到点名称")
    private String registerPointName;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("签到点时间间隔")
    private Integer interval;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("nfc")
    private String nfc;

    @ApiModelProperty("签到分组id")
    private Long signGroupId;

    @ApiModelProperty("工单详情")
    private List<InspectionWorkOrderDto> workOrders;

    public SignPointWithWorkOrder() {
        //空参构造
    }
}

