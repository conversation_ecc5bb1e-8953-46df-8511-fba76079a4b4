# Check for missing entities and classes
Write-Host "Checking for missing entities and classes..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

function Check-MissingClasses {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return @()
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $missingClasses = @()
    
    # Check for common missing classes
    $commonMissingPatterns = @(
        "NodeDao",
        "DeviceDao", 
        "UnitService",
        "EemCloudAuthService",
        "ResultWithTotal",
        "GlobalInfoUtils\.getHttpResponse\(\)"
    )
    
    foreach ($pattern in $commonMissingPatterns) {
        if ($content -match $pattern) {
            $missingClasses += $pattern
        }
    }
    
    return $missingClasses
}

# Process all Java files
Write-Host "Scanning Java files for missing classes..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$issuesFound = @{}

foreach ($file in $javaFiles) {
    $missing = Check-MissingClasses -filePath $file.FullName
    if ($missing.Count -gt 0) {
        $issuesFound[$file.Name] = $missing
    }
}

Write-Host "`nMissing Classes Report:" -ForegroundColor Yellow
if ($issuesFound.Count -eq 0) {
    Write-Host "✓ No missing classes found!" -ForegroundColor Green
} else {
    foreach ($file in $issuesFound.Keys) {
        Write-Host "File: $file" -ForegroundColor Red
        foreach ($missing in $issuesFound[$file]) {
            Write-Host "  - $missing" -ForegroundColor Yellow
        }
    }
}

# Check for specific import issues
Write-Host "`nChecking for import issues..." -ForegroundColor Cyan
$importIssues = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    # Check for old imports that should be updated
    if ($content -match "import.*\.bll\.common\.dao\.node\.NodeDao;") {
        Write-Host "  - Old NodeDao import in: $($file.Name)" -ForegroundColor Red
        $importIssues++
    }
    
    if ($content -match "import.*\.bll\.common\.dao\.device\.DeviceDao;") {
        Write-Host "  - Old DeviceDao import in: $($file.Name)" -ForegroundColor Red
        $importIssues++
    }
    
    if ($content -match "import.*EemCloudAuthService;") {
        Write-Host "  - EemCloudAuthService import in: $($file.Name)" -ForegroundColor Red
        $importIssues++
    }
}

if ($importIssues -eq 0) {
    Write-Host "✓ No import issues found!" -ForegroundColor Green
} else {
    Write-Host "Found $importIssues import issues" -ForegroundColor Red
}

Write-Host "`nCheck completed!" -ForegroundColor Green
