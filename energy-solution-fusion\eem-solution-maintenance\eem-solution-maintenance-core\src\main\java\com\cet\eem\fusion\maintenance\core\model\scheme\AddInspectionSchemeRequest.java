package com.cet.eem.fusion.maintenance.core.model.scheme;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @ClassName : AddInspectionSchemeRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 17:03
 */
@Getter
@Setter
public class AddInspectionSchemeRequest {

    @NotEmpty(message = "巡检方案名称不能为空")
    private String name;

    private List<InspectionSchemeDetail> schemeDetails;

}

