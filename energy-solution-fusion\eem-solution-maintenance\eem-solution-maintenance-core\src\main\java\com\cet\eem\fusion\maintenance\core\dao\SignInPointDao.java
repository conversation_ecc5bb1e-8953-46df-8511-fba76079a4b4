package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInPoint;
import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInGroupWithAllSubLayer;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.List;

public interface SignInPointDao extends BaseModelDao<SignInPoint> {

    /**
     * 查询项目下的所有签到点
     *
     * @param projectId
     * @return
     */
    List<SignInPoint> queryAllSignInPointInProject(Long projectId);

    List<SignInGroupWithAllSubLayer> querySignInPoint(List<Long> signPointIds, List<Long> signGroupIds);
}



