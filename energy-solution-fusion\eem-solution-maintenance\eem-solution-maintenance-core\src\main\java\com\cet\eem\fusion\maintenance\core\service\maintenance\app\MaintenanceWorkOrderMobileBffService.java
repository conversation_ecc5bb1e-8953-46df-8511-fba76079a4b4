package com.cet.eem.fusion.maintenance.core.service.maintenance.app;

import com.cet.eem.fusion.maintenance.core.model.workorder.app.AddMaintenanceWorkOrder;
import com.cet.eem.fusion.maintenance.core.model.workorder.app.MaintenanceWorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.app.WorkOrderCountDto;
import com.cet.eem.fusion.common.model.Page;

import java.util.List;

public interface MaintenanceWorkOrderMobileBffService {
    /**
     * 查询维保工单
     * @return
     */
    List<MaintenanceWorkOrderCountDto> queryWorkOrder(Page page);

    /**
     * 录入维保工单
     * @param addMaintenanceWorkOrder
     */
    void submitInputWorkOrder(AddMaintenanceWorkOrder addMaintenanceWorkOrder);
}


