package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : DevicePlanRelationship
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-16 09:15
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP)
public class DevicePlanRelationship extends EntityWithName {

    @JsonProperty("device_id")
    private Long deviceId;

    @JsonProperty("device_label")
    private String deviceLabel;

    @JsonProperty("devicename")
    private String deviceName;

    private Boolean enabled;

    @JsonProperty("ordercode")
    private String orderCode;

    @JsonProperty("pmworksheet_id")
    private Long pmWorkSheetId;

    @JsonProperty("project_id")
    private Long projectId;

    @JsonProperty("projectname")
    private String projectName;

    @JsonProperty("room_id")
    private Long roomId;

    @JsonProperty("roomname")
    private String roomName;

    public DevicePlanRelationship() {
        this.modelLabel = ModelLabelDef.DEVICE_PLAN_RELATIONSHIP;
    }

    public boolean contentEquals(DevicePlanRelationship devicePlanRelationship) {
        return this.deviceId.equals(devicePlanRelationship.getDeviceId()) && this.deviceLabel.equals(devicePlanRelationship.getDeviceLabel());
    }

    public DevicePlanRelationship(Long deviceId, String deviceLabel) {
        this.deviceId = deviceId;
        this.deviceLabel = deviceLabel;
    }
}
