package com.cet.eem.fusion.maintenance.core.controller.bff.repair;

import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;
import com.cet.eem.fusion.common.def.OperationAuthDef;
import com.cet.eem.fusion.maintenance.core.bll.common.model.ConvergenceEvent;
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecEventExtendVo;
import com.cet.eem.fusion.maintenance.core.bll.common.service.event.convergence.ConvergenceEventDao;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderSourceTypeDef;
import com.cet.eem.fusion.maintenance.core.model.maintance.WorkOrderExportVo;
import com.cet.eem.fusion.maintenance.core.model.maintance.WorkOrderVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.*;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionCountSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.WorkOrderBatchReviewVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.WorkOrderReviewVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.*;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderServiceCallBackParam;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderServiceCallBackResult;
import com.cet.eem.fusion.maintenance.core.service.bff.repair.RepairWorkOrderBffService;
import com.cet.eem.fusion.maintenance.core.service.maintenance.MaintenanceWorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.repair.RepairWorkOrderService;
import com.cet.electric.commons.ApiResult;

import com.cet.eem.event.service.ConvergenceEventService;
import com.cet.eem.event.service.expert.PecCoreEventBffService;
import com.cet.electric.model.enums.ConfirmEventStatusEnum;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.ProcessInstanceResponse;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.node.config.UserTaskConfig;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.ValidationException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-03-12 16:38
 */
public class RepairWorkOrderBffController {
    @Autowired
    RepairWorkOrderService repairWorkOrderService;
    @Autowired
    RepairWorkOrderBffService repairWorkOrderBffService;
    @Autowired
    MaintenanceWorkOrderService maintenanceWorkOrderService;
    @Autowired
    WorkOrderService workOrderService;
    @Autowired
    PecCoreEventBffService eventManageService;
    @Autowired
    ConvergenceEventService convergenceEventService;
    @Autowired
    ConvergenceEventDao convergenceEventDao;

    @ApiOperation(value = "查询维修工单")
    @PostMapping("")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_BROWSER})
    public ApiResult<List<WorkOrderVo>> queryWorkOrderList(
            @Valid @RequestBody RepairSearchVo dto) {
        return repairWorkOrderBffService.queryWorkOrderList(dto);
    }

    @ApiOperation(value = "根据节点查询工单")
    @PostMapping("/node")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_BROWSER})
    public ApiResult<List<WorkOrderVo>> queryWorkOrderByNode(
            @Valid @RequestBody RepairByNodeSearchVo dto) {
        return repairWorkOrderBffService.queryWorkOrderByNode(dto);
    }

    @ApiOperation(value = "统计工单数量")
    @PostMapping("/count")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_BROWSER})
    public ApiResult<List<WorkOrderCountDto>> queryWorkOrderCount(
            @RequestBody InspectionCountSearchDto dto) {
        List<WorkOrderCountDto> result = repairWorkOrderBffService.queryWorkOrderCount(dto);
        return Result.ok(result);
    }

    @ApiOperation(value = "查询指定维修工单")
    @GetMapping("/{code}")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_BROWSER})
    public ApiResult<WorkOrderExportVo> queryWorkOrder(
            @PathVariable @ApiParam(name = "code", value = "工单编号", required = true) String code) {
        WorkOrderExportVo result = repairWorkOrderBffService.queryWorkOrder(code);
        return Result.ok(result);
    }

    @ApiOperation(value = "查询指定任务当前所处的节点")
    @GetMapping("/task/config/{code}")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_BROWSER})
    public ApiResult<UserTaskConfig> queryTaskConfig(
            @PathVariable @ApiParam(name = "code", value = "工单编号", required = true) String code) {
        UserTaskConfig result = repairWorkOrderService.queryTaskConfig(code);
        return Result.ok(result);
    }

    @ApiOperation(value = "创建维修工单")
    @PutMapping("")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_CREATE})
    public ApiResult<ProcessInstanceResponse> queryCurrentProjectSignInGroup(
            @Valid @RequestBody RepairWorkOrderAddDto dto) {
        //增加一个字段sourceindex，先判断有没有这个字段，没有再使用sourceid判断
        if (Objects.nonNull(dto.getEventTime())) {
            PecEventExtendVo pecEventExtendVo = eventManageService.queryEventByIndex(dto.getEventTime(),
                    dto.getDeviceId(), dto.getPecEventType(), dto.getCode1(), dto.getCode2(), dto.getEventByte());
            Assert.notNull(pecEventExtendVo, "事件不存在！");
            if (Objects.equals(pecEventExtendVo.getConfirmeventstatus(), ConfirmEventStatusEnum.CONFIRMED.getCode())) {
                throw new ValidationException("该事件已经被处理！");
            }
        }
        if (Objects.isNull(dto.getEventTime()) && Objects.nonNull(dto.getSourceId())) {
            if (Objects.equals(WorkOrderSourceTypeDef.PECCORE_EVENT, dto.getSourceType())) {
                PecEventExtendVo pecEventExtendVo = eventManageService.queryEventById(dto.getSourceId());
                Assert.notNull(pecEventExtendVo, "事件不存在！");
                if (Objects.equals(pecEventExtendVo.getConfirmeventstatus(), ConfirmEventStatusEnum.CONFIRMED.getCode())) {
                    throw new ValidationException("该事件已经被处理！");
                }
            } else if (Objects.equals(WorkOrderSourceTypeDef.CONVERGENCE_EVENT, dto.getSourceType())) {
                ConvergenceEvent event = convergenceEventDao.queryEvent(dto.getSourceId(), dto.getSourceTime());
                Assert.notNull(event, "事件不存在！");
                if (Objects.equals(event.getConfirmStatus(), ConfirmEventStatusEnum.CONFIRMED.getCode())) {
                    throw new ValidationException("该事件已经被处理！");
                }
            }

        }
        ProcessInstanceResponse workOrder = repairWorkOrderService.createWorkOrder(dto);
        return Result.ok(workOrder);
    }

    @ApiOperation(value = "提交表单数据公共方法，写入内容由调用端自己决定，推动流程进入下一步")
    @PutMapping("/commit/submit")
    public ApiResult<ProcessInstanceResponse> submitInspectParams(@RequestBody WorkOrderFormSubmitParam param) {
        workOrderService.submitFormData(param);
        repairWorkOrderService.updateSystemStatus(param, GlobalInfoUtils.getUserId());
        return Result.ok();
    }

    @ApiOperation(value = "批量提交表单数据公共方法，写入内容由调用端自己决定，推动流程进入下一步")
    @PutMapping("/common/submit/batch")
    public ApiResult<ProcessInstanceResponse> submitInspectParams(@RequestBody WorkOrderFormBatchSubmitParam param) {
        workOrderService.submitFormDataBatch(param);
        return Result.ok();
    }

    @ApiOperation(value = "提交维修数据，推动流程进入下一步")
    @PutMapping("/param/submit")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_INSPECT})
    public ApiResult<ProcessInstanceResponse> submitInspectParams(
            @RequestBody @ApiParam(name = "RepairParamsWriteVo", value = "维修记录", required = true) RepairParamsWriteVo dto) {
        repairWorkOrderService.submitInspectParams(dto);
        return Result.ok();
    }

    @ApiOperation(value = "保存维保内容")
    @PostMapping("/maintenanceContent")
    public ApiResult<WorkOrderServiceCallBackResult> submitInspectParams(
            @RequestBody @ApiParam(name = "param", value = "回调参数", required = true) WorkOrderServiceCallBackParam param) {
        WorkOrderServiceCallBackResult result = repairWorkOrderService.saveMaintenanceContent(param);
        return Result.ok(result);
    }

    @ApiOperation(value = "根据巡检工单创建维修工单")
    @PostMapping("/inspectWorkOrder")
    public ApiResult<WorkOrderServiceCallBackResult> createWorkOrderByInspectWO(
            @RequestBody @ApiParam(name = "param", value = "回调参数", required = true) WorkOrderServiceCallBackParam param) {
        WorkOrderServiceCallBackResult result = repairWorkOrderService.createWorkOrderByInspectWO(param);
        return Result.ok(result);
    }

    @ApiOperation(value = "保存维修数据，不会推动流程进入下一步")
    @PutMapping("/param/update")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_INSPECT})
    public ApiResult<ProcessInstanceResponse> updateWorkOrder(
            @RequestBody RepairParamsWriteVo dto) {
        repairWorkOrderService.updateWorkOrder(dto);
        return Result.ok();
    }

    @ApiOperation(value = "审核工单")
    @PostMapping("/check")
    public ApiResult<List<WorkOrderPo>> reviewForm(
            @RequestBody WorkOrderReviewVo workOrderReviewVo) {
        workOrderService.reviewForm(workOrderReviewVo);
        return Result.ok();
    }

    @ApiOperation(value = "批量审核工单")
    @PostMapping("/check/batch")
    public ApiResult<List<WorkOrderPo>> reviewForm(
            @RequestBody WorkOrderBatchReviewVo workOrderReviewVo) {
        workOrderService.reviewFormBatch(workOrderReviewVo);
        return Result.ok();
    }

    @ApiOperation(value = "暂存审核信息")
    @PostMapping("/check/stash")
    public ApiResult<List<WorkOrderPo>> saveReviewForm(
            @RequestBody WorkOrderReviewVo workOrderReviewVo) {
        workOrderService.saveReviewForm(workOrderReviewVo);
        return Result.ok();
    }

    @ApiOperation(value = "查询暂存的审核信息")
    @GetMapping("/check/stash/{code}")
    public ApiResult<WorkOrderCheckInfoVo> queryWorkOrderCheckInfo(@PathVariable String code) {
        WorkOrderCheckInfoVo workOrderCheckInfoVo = workOrderService.queryWorkOrderCheckInfo(code);
        return Result.ok(workOrderCheckInfoVo);
    }

    @ApiOperation(value = "导出巡检工单，内容与打印的一致")
    @GetMapping("/check/import/{code}")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_BROWSER})
    public ApiResult<WorkOrderCheckInfoVo> exportWorkOrder(@PathVariable String code) {
        repairWorkOrderBffService.exportWorkOrder(code);
        return null;
    }

    @ApiOperation(value = "获取流程状态图")
    @GetMapping("/workOrder/processDiagram/{code}")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_BROWSER})
    public ApiResult<Object> updateWorkOrder(@PathVariable String code, @RequestParam(required = false) Boolean isLightStyle) throws Exception {
        workOrderService.getProcessDiagram(code, isLightStyle);
        return null;
    }

    @ApiOperation(value = "获取配置信息")
    @PostMapping("/config")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_BROWSER})
    public ApiResult<RepairConfigVO> queryWorkOrderConfig() throws Exception {
        return Result.ok(repairWorkOrderService.getRepairConfig());
    }
}



