package com.cet.eem.fusion.maintenance.core.constant;

/**
 * Maintenance constants
 */
public class MaintenanceConstant {
    
    // Maintenance status
    public static final String STATUS_DRAFT = "DRAFT";
    public static final String STATUS_PENDING = "PENDING";
    public static final String STATUS_APPROVED = "APPROVED";
    public static final String STATUS_IN_PROGRESS = "IN_PROGRESS";
    public static final String STATUS_COMPLETED = "COMPLETED";
    public static final String STATUS_CANCELLED = "CANCELLED";
    
    // Maintenance types
    public static final String TYPE_PREVENTIVE = "PREVENTIVE";
    public static final String TYPE_CORRECTIVE = "CORRECTIVE";
    public static final String TYPE_EMERGENCY = "EMERGENCY";
    public static final String TYPE_ROUTINE = "ROUTINE";
    
    // Priority levels
    public static final String PRIORITY_LOW = "LOW";
    public static final String PRIORITY_MEDIUM = "MEDIUM";
    public static final String PRIORITY_HIGH = "HIGH";
    public static final String PRIORITY_CRITICAL = "CRITICAL";
    
    // Work order status
    public static final String WORK_ORDER_NEW = "NEW";
    public static final String WORK_ORDER_ASSIGNED = "ASSIGNED";
    public static final String WORK_ORDER_IN_PROGRESS = "IN_PROGRESS";
    public static final String WORK_ORDER_COMPLETED = "COMPLETED";
    public static final String WORK_ORDER_CLOSED = "CLOSED";
    
    // Equipment status
    public static final String EQUIPMENT_NORMAL = "NORMAL";
    public static final String EQUIPMENT_WARNING = "WARNING";
    public static final String EQUIPMENT_FAULT = "FAULT";
    public static final String EQUIPMENT_MAINTENANCE = "MAINTENANCE";
    public static final String EQUIPMENT_OFFLINE = "OFFLINE";
}
