package com.cet.eem.fusion.maintenance.core.controller.bff.inspect;

import com.cet.eem.fusion.maintenance.common.log.annotation.OperationLog;
import com.cet.eem.fusion.maintenance.common.log.constant.EnumOperationSubType;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.fusion.maintenance.core.model.scheme.*;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorSchemeService;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.electric.commons.ApiResult;

import io.swagger.annotations.ApiOperation;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName : InspectionParameterController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 14:19
 */

public class InspectionSchemeBffController {

    @Autowired
    private InspectorSchemeService inspectorSchemeService;

    @Autowired
    private CommonUtilsService commonUtilsService;

    @ApiOperation(value = "查询巡检方案")
    @PostMapping("/query")
    public ApiResult<List<InspectionSchemeVo>> queryInspectionScheme(@Valid @RequestBody QueryInspectionSchemeRequest queryInspectionSchemeRequest) {
        return inspectorSchemeService.queryInspectionScheme(queryInspectionSchemeRequest);
    }

    @ApiOperation(value = "新增巡检方案")
    @OperationLog(operationType = OperationLogType.INSPECTOR_SCHEME, subType = EnumOperationSubType.ADD, description = "【新增巡检方案】")
    @PostMapping()
    public ApiResult<InspectionScheme> addInspectionScheme(@Valid @RequestBody AddInspectionSchemeRequest addInspectionSchemeRequest) {
        return Result.ok(inspectorSchemeService.addInspectionScheme(addInspectionSchemeRequest));
    }

    @ApiOperation(value = "查询巡检方案参数")
    @GetMapping("/detail")
    public ApiResult<QueryInspectionDetailResult> queryInspectionSchemeAndDetail(@RequestParam Long inspectionSchemeId) {
        return Result.ok(inspectorSchemeService.queryInspectionSchemeAndDetail(inspectionSchemeId));
    }

    @ApiOperation(value = "编辑巡检方案")
    @OperationLog(operationType = OperationLogType.INSPECTOR_SCHEME, subType = EnumOperationSubType.UPDATE, description = "【更新巡检方案】")
    @PutMapping()
    public ApiResult<Void> editInspectionScheme(@Valid @RequestBody EditInspectionSchemeRequest editInspectionSchemeRequest) {
        inspectorSchemeService.editInspectionScheme(editInspectionSchemeRequest);
        return Result.ok();
    }

    @ApiOperation(value = "删除巡检方案")
    @OperationLog(operationType = OperationLogType.INSPECTOR_SCHEME, subType = EnumOperationSubType.DELETE, description = "【删除巡检方案】")
    @DeleteMapping()
    public ApiResult<Void> deleteInspectionScheme(@RequestBody List<Long> inspectionSchemeIds) {
        inspectorSchemeService.deleteInspectionScheme(inspectionSchemeIds);
        return Result.ok();
    }

    @ApiOperation(value = "下载导入模板")
    @PostMapping("/downloadTemplate")
    public ApiResult<Object> downloadTemplate(HttpServletResponse response) {
        inspectorSchemeService.downloadTemplate(response);
        return Result.ok();
    }

    @ApiOperation(value = "批量导入参数方案配置")
    @PostMapping("/importItem")
    public ApiResult<Object> importItem(@RequestParam("file") MultipartFile file,
                                     HttpServletRequest request) throws IOException, InvalidFormatException, ValidationException {
        commonUtilsService.writeOperationLogs(OperationLogType.INSPECTOR_SCHEME,EnumOperationSubType.UPDATE.getId(),"【导入巡检方案】",null);
        Long projectId = Long.valueOf(request.getHeader("projectId"));
        inspectorSchemeService.importItem(file,projectId);
        return Result.ok();
    }
}



