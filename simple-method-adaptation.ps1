# 简化的方法调用和常量适配脚本
Write-Host "Starting method and constant adaptation..." -ForegroundColor Green

$coreDir = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$javaFiles = Get-ChildItem -Path $coreDir -Filter "*.java" -Recurse

$modifiedCount = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $modified = $false
    
    # 方法调用替换
    if ($content -match "GlobalInfoUtils\.getProjectId\(\)") {
        $content = $content -replace "GlobalInfoUtils\.getProjectId\(\)", "GlobalInfoUtils.getTenantId()"
        $modified = $true
    }
    
    # 常量替换
    if ($content -match "Result\.SUCCESS_CODE") {
        $content = $content -replace "Result\.SUCCESS_CODE", "ErrorCode.SUCCESS_CODE"
        $modified = $true
    }
    
    if ($content -match "EemCommonUtils\.BLANK_STR") {
        $content = $content -replace "EemCommonUtils\.BLANK_STR", "StringFormatUtils.BLANK_STR"
        $modified = $true
    }
    
    # 类名替换
    if ($content -match "\bTableNameDef\.") {
        $content = $content -replace "\bTableNameDef\.", "ModelLabelDef."
        $modified = $true
    }
    
    if ($content -match "\bBaseEntity\b") {
        $content = $content -replace "\bBaseEntity\b", "EntityWithName"
        $modified = $true
    }
    
    if ($content -match "\bQueryConditionBuilder\b") {
        $content = $content -replace "\bQueryConditionBuilder\b", "ParentQueryConditionBuilder"
        $modified = $true
    }
    
    # 返回类型更新
    if ($content -match "public\s+ResultWithTotal<") {
        $content = $content -replace "public\s+ResultWithTotal<", "public ApiResult<"
        $modified = $true
    }
    
    if ($content -match "public\s+Result<") {
        $content = $content -replace "public\s+Result<", "public ApiResult<"
        $modified = $true
    }
    
    # 如果内容有修改，写回文件
    if ($modified -and $content -ne $originalContent) {
        try {
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
            $modifiedCount++
            Write-Host "Modified file: $($file.Name)" -ForegroundColor Green
        }
        catch {
            Write-Host "Failed to modify file: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "Method and constant adaptation completed! Total files modified: $modifiedCount" -ForegroundColor Green
