# Supplementary Fusion Adaptation Script V2 - Based on Updated Rules
Write-Host "Running supplementary fusion adaptation fixes V2..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Additional import replacements based on updated rules
$importReplacements = @{
    "import com.cet.eem.common.utils.TimeUtil;" = "import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;"
    "import com.cet.eem.dao.BaseModelDao;" = "import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;"
    "import com.cet.eem.quantity.dao.QuantityAggregationDataDao;" = "import com.cet.eem.fusion.energy.sdk.dao.QuantityAggregationDataDao;"
    "import com.cet.piem.common.utils.DoubleUtils;" = "import com.cet.eem.solution.common.utils.DoubleUtils;"
    "import com.cet.piem.service.feign.PiemConfigServerService;" = "import com.cet.eem.solution.common.feign.ConfigServerService;"
    "import com.cet.eem.bll.common.dao.project.ProductDao;" = "import com.cet.eem.fusion.config.sdk.dao.ProductDao;"
    "import com.cet.eem.common.CommonUtils;" = "import com.cet.eem.fusion.common.utils.CommonUtils;"
    "import com.cet.eem.common.definition.ColumnDef;" = "import com.cet.eem.fusion.common.def.common.ColumnDef;"
    "import com.cet.eem.common.constant.EnumOperationType;" = "import com.cet.eem.fusion.common.def.common.EnumOperationType;"
    "import com.cet.eem.common.model.Page;" = "import com.cet.eem.fusion.common.model.Page;"
    "import com.cet.eem.common.page.PageUtils;" = "import com.cet.eem.fusion.common.utils.page.PageUtils;"
    "import com.cet.eem.common.file.FileUtils;" = "import com.cet.eem.fusion.common.utils.file.FileUtils;"
    "import com.cet.eem.common.model.auth.user.UserVo;" = "import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;"
    "import com.cet.eem.bll.common.model.domain.object.physicalquantity.ProjectUnitClassify;" = "import com.cet.eem.fusion.common.def.base.ProjectUnitClassify;"
    "import com.cet.eem.bll.common.model.domain.object.physicalquantity.UserDefineUnit;" = "import com.cet.electric.baseconfig.common.entity.UserDefineUnit;"
    "import com.cet.eem.bll.common.model.peccore.PecCoreTreeSearchVo;" = "import com.cet.eem.fusion.config.sdk.entity.peccore.PecCoreTreeSearchVo;"
    "import com.cet.eem.common.model.peccore.Meter;" = "import com.cet.eem.fusion.common.model.peccore.Meter;"
    "import com.cet.eem.annotation.ModelLabel;" = "import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;"
    "import com.cet.eem.common.definition.SplitCharDef;" = "import com.cet.eem.fusion.common.def.common.SplitCharDef;"
    "import com.cet.eem.quantity.dao.QuantityObjectDao;" = "import com.cet.eem.fusion.energy.sdk.dao.QuantityObjectDao;"
    "import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;" = "import com.cet.electric.modelsdk.quantity.model.QuantityAggregationData;"
    "import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;" = "import com.cet.electric.baseconfig.common.entity.QuantityObject;"
    "import com.cet.eem.auth.service.InnerAuthService;" = "import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;"
    "import com.cet.eem.node.TopologyUtils;" = "import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;"
    "import com.cet.eem.bll.common.util.ExcelValidationUtils;" = "import com.cet.eem.fusion.common.utils.excel.ExcelValidationUtils;"
    "import com.cet.eem.bll.common.dao.node.NodeDao;" = "import com.cet.eem.fusion.config.sdk.service.EemNodeService;"
    "import com.cet.eem.common.ParamUtils;" = "import com.cet.eem.fusion.common.utils.ParamUtils;"
    "import com.cet.eem.bll.common.dao.poi.EemPoiRecordDao;" = "import com.cet.eem.fusion.energy.sdk.dao.EemPoiRecordDao;"
    "import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;" = "import com.cet.eem.fusion.energy.sdk.model.EemPoiRecord;"
    "import com.cet.eem.common.constant.QueryType;" = "import com.cet.eem.fusion.common.def.base.QueryType;"
    "import com.cet.eem.model.base.QueryCondition;" = "import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;"
    "import com.cet.eem.dao.ModelDaoImpl;" = "import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;"
    "import com.cet.eem.conditions.query.LambdaQueryWrapper;" = "import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;"
    "import com.cet.eem.model.base.ModelSingeWriteVo;" = "import com.cet.eem.fusion.common.modelutils.model.base.ModelSingeWriteVo;"
    "import com.cet.eem.common.parse.JsonTransferUtils;" = "import com.cet.eem.fusion.common.utils.JsonTransferUtils;"
    "import com.cet.eem.common.definition.NodeLabelDef;" = "import com.cet.eem.fusion.common.def.label.NodeLabelDef;"
    "import com.cet.eem.common.constant.AggregationType;" = "import com.cet.eem.fusion.energy.sdk.def.AggregationType;"
    "import com.cet.eem.common.constant.EnergyTypeDef;" = "import com.cet.eem.fusion.common.def.base.EnergyTypeDef;"
    "import com.cet.eem.conditions.update.LambdaUpdateWrapper;" = "import com.cet.eem.fusion.common.modelutils.conditions.update.LambdaUpdateWrapper;"
    "import com.cet.eem.bll.common.model.domain.subject.generalrules.UnnaturalSetVo;" = "import com.cet.eem.fusion.config.sdk.entity.unnatural.UnnaturalSetVo;"
    "import com.cet.eem.bll.common.model.domain.subject.generalrules.FeeScheme;" = "import com.cet.eem.fusion.energy.sdk.model.generalrules.FeeScheme;"
    "import com.cet.eem.bll.common.model.domain.object.architecture.RoomVo;" = "import com.cet.eem.fusion.config.sdk.model.node.RoomVo;"
    "import com.cet.eem.bll.common.model.domain.object.architecture.BuildingVo;" = "import com.cet.eem.fusion.config.sdk.model.node.BuildingVo;"
}

# Method call replacements
$methodReplacements = @{
    "CommonUtils\.formatDoubleWithOutScientificNotation" = "StringFormatUtils.formatDoubleWithOutScientificNotation"
}

function Apply-SupplementaryAdaptations {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $changed = $false
    
    # Apply import replacements
    foreach ($oldImport in $importReplacements.Keys) {
        $newImport = $importReplacements[$oldImport]
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), $newImport
            $changed = $true
            Write-Host "  - Updated import: $oldImport -> $newImport" -ForegroundColor Yellow
        }
    }
    
    # Apply method call replacements
    foreach ($oldMethod in $methodReplacements.Keys) {
        $newMethod = $methodReplacements[$oldMethod]
        if ($content -match $oldMethod) {
            $content = $content -replace $oldMethod, $newMethod
            $changed = $true
            Write-Host "  - Updated method call: $oldMethod -> $newMethod" -ForegroundColor Yellow
        }
    }
    
    if ($changed) {
        Set-Content $filePath -Value $content -Encoding UTF8
        Write-Host "✓ Applied supplementary adaptations to: $filePath" -ForegroundColor Green
        return $true
    }
    
    return $false
}

# Process all Java files
Write-Host "Processing Java files for supplementary adaptations..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$filesUpdated = 0

foreach ($file in $javaFiles) {
    if (Apply-SupplementaryAdaptations -filePath $file.FullName) {
        $filesUpdated++
    }
}

Write-Host "`nSupplementary adaptations V2 completed!" -ForegroundColor Green
Write-Host "Files updated: $filesUpdated" -ForegroundColor Cyan
