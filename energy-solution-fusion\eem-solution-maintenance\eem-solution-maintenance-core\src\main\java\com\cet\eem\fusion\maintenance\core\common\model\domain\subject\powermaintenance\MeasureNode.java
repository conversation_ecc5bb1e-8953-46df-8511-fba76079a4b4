package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-14
 */
@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.MEASURE_NODE)
public class MeasureNode extends EntityWithName {
    private String name;

    @JsonProperty("dataid")
    private Long dataId;

    @ApiModelProperty("父节点名称")
    private String parentName;

    public MeasureNode() {
        this.modelLabel = ModelLabelDef.MEASURE_NODE;
    }
}
