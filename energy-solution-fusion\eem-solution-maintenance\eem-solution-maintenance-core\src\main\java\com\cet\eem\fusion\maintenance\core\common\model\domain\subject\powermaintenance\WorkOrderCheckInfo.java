package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 工单审核暂存信息
 *
 * <AUTHOR>
 * @date 2021/5/27
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.WORK_ORDER_CHECK_INFO)
public class WorkOrderCheckInfo extends EntityWithName {
    @ApiModelProperty("备注信息")
    private String remark;

    @ApiModelProperty("附件")
    private String attachments;

    @JsonProperty(ColumnDef.TASK_ID)
    private String taskId;

    @JsonProperty(ColumnDef.PM_WORKSHEET_ID)
    private Long pmWorkSheetId;

    private String code;

    @JsonProperty(ColumnDef.USERID)
    private Long userId;

    @JsonProperty(ColumnDef.CREATE_TIME)
    private LocalDateTime createTime;

    @JsonProperty(ColumnDef.FORM_DATA)
    private Map<String, Object> formData;

    public WorkOrderCheckInfo() {
        this.modelLabel = ModelLabelDef.WORK_ORDER_CHECK_INFO;
    }
}
