package com.cet.eem.fusion.maintenance.core.dao.devicecomponent.impl;


import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.SparePartsReplaceRecordDao;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SparePartsReplaceRecord;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.maintenance.common.toolkit.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * @ClassName : SparePartsReplaceRecordDaoImpl
 * @Description : 备件更换记录
 * <AUTHOR> jiangzixuan
 * @Date: 2021-05-17 08:47
 */
@Repository
public class SparePartsReplaceRecordDaoImpl extends ModelDaoImpl<SparePartsReplaceRecord> implements SparePartsReplaceRecordDao {


    @Override
    public ApiResult<List<SparePartsReplaceRecord>> queryByTimeAndId(Long st, Long et, Set<Long> ids, Page page, List<Long> deviceSystemId, List<String> objectLabels) {
        LambdaQueryWrapper<SparePartsReplaceRecord> wrapper = LambdaQueryWrapper.of(SparePartsReplaceRecord.class);
        if (null == st || null == et || null == page || CollectionUtils.isEmpty(ids)) {
            return Result.ok();
        } else {
            wrapper.ge(SparePartsReplaceRecord::getLogtime, st)
                    .lt(SparePartsReplaceRecord::getLogtime, et)
                    .in(SparePartsReplaceRecord::getSparePartsStorageId, ids);

        }
        if (CollectionUtils.isNotEmpty(deviceSystemId)) {

            wrapper.in(SparePartsReplaceRecord::getDeviceSystemId, deviceSystemId);
        }
        if (CollectionUtils.isNotEmpty(objectLabels)) {
            wrapper.in(SparePartsReplaceRecord::getObjectLabel, objectLabels);

        }
        wrapper.orderByAsc(SparePartsReplaceRecord::getLogtime);

        return this.selectPage(wrapper, page);
    }

    @Override
    public ApiResult<List<SparePartsReplaceRecord>> querySparePartsReplace(Long st, Long et, Set<Long> ids, Page page) {
        LambdaQueryWrapper<SparePartsReplaceRecord> wrapper = LambdaQueryWrapper.of(SparePartsReplaceRecord.class);
        if (null == st || null == et || null == page || CollectionUtils.isEmpty(ids)) {
            return Result.ok();
        } else {
            wrapper.ge(SparePartsReplaceRecord::getLogtime, st)
                    .lt(SparePartsReplaceRecord::getLogtime, et)
                    .in(SparePartsReplaceRecord::getSparePartsStorageId, ids)
                    .orderByAsc(SparePartsReplaceRecord::getLogtime);
            return this.selectPage(wrapper, page);
        }


    }

    @Override
    public List<SparePartsReplaceRecord> queryByDevice(String objectLabel, Long objectId) {
        if (null == objectLabel || null == objectId) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SparePartsReplaceRecord> wrapper = LambdaQueryWrapper.of(SparePartsReplaceRecord.class)
                .eq(SparePartsReplaceRecord::getObjectId, objectId)
                .eq(SparePartsReplaceRecord::getObjectLabel, objectLabel)
                .orderByDesc(SparePartsReplaceRecord::getLogtime);
        return this.selectList(wrapper);

    }

    @Override
    public List<SparePartsReplaceRecord> queryByWorkOderId(Collection<Long> workOrderIds) {
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SparePartsReplaceRecord> wrapper = LambdaQueryWrapper.of(SparePartsReplaceRecord.class)
                .in(SparePartsReplaceRecord::getWorkOrderId, workOrderIds);
        return this.selectList(wrapper);
    }

    @Override
    public ApiResult<List<SparePartsReplaceRecord>> querySparePartsReplaceByDevice(Long st, Long et, String objectLabel, Page page) {
        LambdaQueryWrapper<SparePartsReplaceRecord> wrapper = LambdaQueryWrapper.of(SparePartsReplaceRecord.class)
                .ge(SparePartsReplaceRecord::getLogtime, st)
                .lt(SparePartsReplaceRecord::getLogtime, et);
        if (objectLabel!=null){
            wrapper.eq(SparePartsReplaceRecord::getObjectLabel,objectLabel);
        }
        return this.selectPage(wrapper,page);
    }
}


