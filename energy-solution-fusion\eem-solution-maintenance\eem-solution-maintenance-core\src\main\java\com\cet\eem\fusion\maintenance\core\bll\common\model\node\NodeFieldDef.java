package com.cet.eem.fusion.maintenance.core.bll.common.model.node;

import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/6/22
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.NODE_FIELD_DEF)
public class NodeFieldDef extends EntityWithName {
    @ApiModelProperty("是否允许修改")
    @JsonProperty(ColumnDef.ALLOW_EDIT)
    private Boolean allowEdit;

    @ApiModelProperty("是否必输")
    private Boolean required;

    @ApiModelProperty("数据类型")
    private Integer datatype;

    @ApiModelProperty("日期格式")
    private String dateformat;

    @ApiModelProperty("字段描述")
    private String description;

    @ApiModelProperty("枚举标识")
    @JsonProperty(ColumnDef.ENUM_LABEL)
    private String enumLabel;

    @ApiModelProperty("最大长度")
    private Integer maxlength;

    @ApiModelProperty("最大值")
    private Double maxvalue;

    @ApiModelProperty("最小长度")
    private Integer minlength;

    @ApiModelProperty("最小值")
    private Double minvalue;

    @ApiModelProperty("精度")
    private Integer precision;

    @ApiModelProperty("节点标识")
    @JsonProperty(ColumnDef.NODE_LABEL)
    private String nodeLabel;

    @ApiModelProperty("字段描述")
    private Integer sort;

    @ApiModelProperty("字段描述")
    private String tips;

    @ApiModelProperty("数据来源类型")
    @JsonProperty(ColumnDef.SOURCE_TYPE)
    private Integer sourceType;

    @ApiModelProperty("数据来源模型")
    @JsonProperty(ColumnDef.SOURCE_LABEL)
    private String sourceLabel;

    @ApiModelProperty("数据来源字段")
    @JsonProperty(ColumnDef.SOURCE_FIELD)
    private String sourceField;
}
