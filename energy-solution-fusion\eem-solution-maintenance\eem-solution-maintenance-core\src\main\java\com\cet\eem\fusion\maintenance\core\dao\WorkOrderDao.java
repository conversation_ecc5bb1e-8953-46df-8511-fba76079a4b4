package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.fusion.maintenance.common.model.domain.subject.activiti.WorksheetAbnormalReason;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.ProcessFlowUnit;
import com.cet.eem.fusion.maintenance.core.model.WorkOrderQueryVo;
import com.cet.eem.fusion.maintenance.core.model.sign.SignGroupWithSignIn;
import com.cet.eem.fusion.maintenance.core.model.wo.WorkOrderSimpleQueryDTO;
import com.cet.eem.fusion.maintenance.core.model.workorder.*;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionCountSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.maintenance.QueryMaintenanceWorkOrderCountRequest;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.ProcessInstanceResponse;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.node.config.UserTaskConfig;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.params.MultiTableTriggerParams;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.params.TableTriggerParams;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.params.UserTaskParams;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 4/12/2021
 */
public interface WorkOrderDao {

    /**
     * 根据通用条件查询工单
     *
     * @param condition
     * @param userId
     * @return
     */
    ApiResult<List<Map<String, Object>>> getModelEntityList(QueryCondition condition, Long userId);

    /**
     * 查询处于活动状态的工单
     *
     * @param condition
     * @param userId
     * @return
     */
    ApiResult<List<Map<String, Object>>> getRuntimeModelEntityList(QueryCondition condition, Long userId, Long tenantId);

    /**
     * 根据工单状态查询工单
     *
     * @param workOrderStatus
     * @param userId
     * @return
     */
    <T extends InspectionWorkOrderDto> List<T> queryWorkOrderByWorkStatus(List<Integer> workOrderStatus, Integer taskType, Long teamId, LocalDateTime et, Long userId, Class<T> clazz);

    <T extends InspectionWorkOrderDto> List<T> queryRuntimeWorkOrderByWorkStatus(List<Integer> workOrderStatus, Integer taskType, Long teamId, LocalDateTime et, Long userId, Long tenantId, Class<T> clazz);

    /**
     * 查询工单
     *
     * @param workOrderStatus
     * @param taskType
     * @param st
     * @param et
     * @param clazz
     */
    <T extends InspectionWorkOrderDto> List<T> queryWorkOrderByWorkStatus(List<Integer> workOrderStatus, Integer taskType, LocalDateTime st, LocalDateTime et, Class<T> clazz);

    /**
     * 分页查询工单
     *
     * @param workOrderQueryVo
     * @param clazz
     * @param <T>
     * @return
     */
    <T extends InspectionWorkOrderDto> ApiResult<List<T>> queryWorkOrderByWorkStatusPage(WorkOrderSimpleQueryDTO workOrderQueryVo, Class<T> clazz);

    /**
     * 分页查询工单
     *
     * @param workOrderQueryVo
     * @param clazz
     * @param <T>
     * @return
     */
    <T extends InspectionWorkOrderDto> ApiResult<List<T>> queryWorkOrderByWorkStatusByPage(WorkOrderQueryVo workOrderQueryVo, Class<T> clazz);

    ApiResult<List<Map<String, Object>>> queryWorkOrderByWorkStatus(LocalDateTime st, LocalDateTime et, Integer taskType, List<Integer> workOrderStatus, Long teamId, Long userId);


    /**
     * 根据工单id查询工单
     *
     * @param ids
     * @return
     */
    List<InspectionWorkOrderDto> queryWorkOrderByIds(Collection<Long> ids);

    /**
     * 根据id查询工单
     *
     * @param ids
     * @return
     */
    List<WorkOrderPo> selectBatchIds(Collection<Long> ids);

    /**
     * 根据条件查询工单
     *
     * @param dto
     * @param userId
     * @return
     */
    ApiResult<List<InspectionWorkOrderDto>> queryWorkOrder(InspectionSearchDto dto, Long userId);

    /**
     * 根据条件查询工单
     *
     * @param dto
     * @param userId
     * @return
     */
    ApiResult<List<InspectionWorkOrderDto>> queryWorkOrder(WorkOrderSearchVo dto, Long userId);

    /**
     * 点检曲线中查询已经完成的工单数据，时间传参对比是已经完成工单的时间
     *
     * @param dto
     * @param userId
     * @return
     */
    ApiResult<List<InspectionWorkOrderDto>> queryFinishWorkOrder(InspectionSearchDto dto, Long userId);

    /**
     * 查询处理活动状态的工单
     *
     * @param code
     * @param userId
     * @return
     */
    Map<String, Object> queryRuntimeWorkOrder(String code, Long userId, Long tenantId);

    /**
     * 查询活动状态工单
     *
     * @param codes
     * @param userId
     * @return
     */
    List<InspectionWorkOrderDto> queryRuntimeWorkOrders(List<String> codes, Long userId, Long tenantId);

    /**
     * 查询工单
     *
     * @param code
     * @return
     */
    <T extends InspectionWorkOrderDto> T queryWorkOrder(String code, Class<T> clazz);

    /**
     * 查询工单
     *
     * @param workOrderId
     * @return
     */
    <T extends InspectionWorkOrderDto> T queryWorkOrder(Long workOrderId, Class<T> clazz);

    /**
     * 根据计划查询工单信息
     *
     * @param planSheetId
     * @return
     */
    List<InspectionWorkOrderDto> queryWorkOrderByPlanSheetId(Long planSheetId, Integer taskType);

    /**
     * 统计工单数量
     *
     * @param dto
     * @return
     */
    List<Map<String, Object>> queryWorkOrderCountByStatus(InspectionCountSearchDto dto);

    /**
     * 统计工单数量
     *
     * @param dto
     * @return
     */
    List<Map<String, Object>> queryWorkOrderCountByTaskType(WorkOrderSearchVo dto);

    /**
     * 统计工单数量
     *
     * @param dto
     * @return
     */
    List<WoStatusCountDTO> queryWorkOrderCount(WoStatusCountQueryVO dto);

    /**
     * 查询具备操作权限的工单
     *
     * @param dto
     * @return
     */
    List<Map<String, Object>> queryRuntimeWorkOrderCountByTaskType(WorkOrderSearchVo dto, Long tenantId);


    /**
     * 统计工单数量
     *
     * @param queryMaintenanceWorkOrderCountRequest
     * @return
     */
    List<Map<String, Object>> queryWorkOrderCountByStatus(QueryMaintenanceWorkOrderCountRequest queryMaintenanceWorkOrderCountRequest);

    /**
     * 查询工单审批日志
     *
     * @param workOrderIds
     * @return
     */
    List<ProcessFlowUnit> queryProcessFlowUnit(Collection<Long> workOrderIds);

    /**
     * 查询异常原因
     *
     * @param workOrderId
     * @return
     */
    List<WorksheetAbnormalReason> queryWorksheetAbnormalReason(Long workOrderId);

    /**
     * 根据工单号查询任务
     *
     * @param codes
     * @return
     */
    Map<String, UserTaskConfig> queryTaskConfigList(Collection<Long> codes, Long tenantId);

    /**
     * 根据设备节点过滤工单
     *
     * @param dto
     * @param userId
     * @param projectId
     * @param baseVo
     * @return
     */
    ApiResult<List<InspectionWorkOrderDto>> queryFinishWorkOrderByNode(InspectionSearchDto dto, Long userId,
                                                                             Long projectId, Collection<BaseVo> baseVo, List<Long> schemeIds);

    ApiResult<List<Map<String, Object>>> saveModelEntityList(List<Map<String, Object>> dataList);

    ApiResult<ProcessInstanceResponse> startProcessByTable(Long userId, TableTriggerParams triggerParams);

    ApiResult<Map<String, Object>> submitForm(Long userId, String taskId, UserTaskParams taskParams);

    ApiResult<List<ProcessInstanceResponse>> startProcessesByManyTables(Long userId, MultiTableTriggerParams multiTableTriggerParams);

    List<SignGroupWithSignIn> querySignPoint(WorkOrderSimpleQueryDTO workOrderQueryVo);
}


