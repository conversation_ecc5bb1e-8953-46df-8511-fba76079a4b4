package com.cet.eem.fusion.maintenance.core.model.wo;

import com.cet.eem.fusion.maintenance.core.model.WorkOrderQueryVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2024/3/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WorkOrderSimpleQueryDTO extends WorkOrderQueryVo {
    /**
     * 需要查询的字段，如果不指定则默认查询所有
     */
    private List<String> selectFields;
}

