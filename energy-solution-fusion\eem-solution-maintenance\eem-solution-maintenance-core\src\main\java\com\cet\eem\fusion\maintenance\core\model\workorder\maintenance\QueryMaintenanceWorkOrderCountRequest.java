package com.cet.eem.fusion.maintenance.core.model.workorder.maintenance;

import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : QueryMaintenanceWorkOrderCountRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-27 13:52
 */
@Getter
@Setter
@ApiModel(description = "维保工单统计")
public class QueryMaintenanceWorkOrderCountRequest {

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束时间")
    private Long endTime;

    @ApiModelProperty("班组id")
    @JsonProperty(WorkOrderDef.TEAM_ID)
    private Long teamId;

    @ApiModelProperty("等级")
    @JsonProperty(WorkOrderDef.TASK_LEVEL)
    private Integer taskLevel;
}

