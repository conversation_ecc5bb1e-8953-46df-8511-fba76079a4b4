package com.cet.eem.fusion.maintenance.core.service.inspection;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.fusion.maintenance.core.model.plan.*;
import com.cet.electric.commons.ApiResult;
import org.quartz.SchedulerException;

import java.util.Collection;
import java.util.List;

public interface InspectionPlanService {

    /**
     * 新建巡检计划
     *
     * @param addInspectionPlanRequest
     * @return
     */
    PlanSheet addInspectionPlan(AddInspectionPlanRequest addInspectionPlanRequest) throws SchedulerException;

    /**
     * 编辑巡检计划
     *
     * @param editInspectionPlanRequest
     * @return
     */
    PlanSheet editInspectionPlan(EditInspectionPlanRequest editInspectionPlanRequest) throws SchedulerException;

    /**
     * 查询巡检计划
     *
     * @param queryInspectionPlanRequest
     * @return
     */
    ApiResult<List<InspectionPlanSheetVo>> queryInspectionPlan(QueryInspectionPlanRequest queryInspectionPlanRequest);

    /**
     * 删除计划
     *
     * @param planIds
     */
    void deletePlan(Collection<Long> planIds) throws SchedulerException;

    /**
     * 禁用巡检计划
     *
     * @param ids
     */
    void disablePlanSheet(Collection<Long> ids) throws SchedulerException;

    /**
     * 启用巡检计划
     *
     * @param ids
     */
    void enablePlanSheet(Collection<Long> ids) throws SchedulerException;

    void setTeamName(List<? extends PlanSheetBaseVo> planSheetVoList, Long tenantId);

    void setNextExecuteTime(List<? extends PlanSheetBaseVo> planSheetVoList);
    PlanSheet editUsed(Long id) throws SchedulerException;
}

