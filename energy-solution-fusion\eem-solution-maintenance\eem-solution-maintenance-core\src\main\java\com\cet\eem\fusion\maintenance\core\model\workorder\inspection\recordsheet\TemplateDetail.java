package com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : TemplateDetail
 * @Description : 模板详情
 * <AUTHOR> jiang<PERSON><PERSON><PERSON>
 * @Date: 2022-10-12 13:50
 */
@Getter
@Setter
public class TemplateDetail {
    private List<NodeIdLabel> node;
    private Integer cycle;
    @JsonProperty(RecordSheetModelLabel.SCHEME_ID)
    private List<Long> inspectionSchemeId;
    private List<DeviceFieldData> dashboard;
    private List<Long> techParamDataId;
}
