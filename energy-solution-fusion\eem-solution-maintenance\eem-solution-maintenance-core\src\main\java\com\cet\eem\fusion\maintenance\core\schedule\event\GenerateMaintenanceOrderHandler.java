﻿package com.cet.eem.fusion.maintenance.core.schedule.event;

import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionPlanService;
import com.cet.eem.fusion.maintenance.core.service.maintenance.MaintenanceWorkOrderService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.maintenance.common.definition.LoginDef;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName : GenerateInspectionOrderHandler
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-26 08:45
 */
@Component
@Slf4j
public class GenerateMaintenanceOrderHandler implements ChannelAwareMessageListener {

    @Autowired
    private MaintenanceWorkOrderService maintenanceWorkOrderService;

    @Autowired
    private CommonUtilsService commonUtilsService;

    @Autowired
    private InspectionPlanService inspectionPlanService;

    @Override
    public void onMessage(Message message, Channel channel) {
        CreateOrderCommand createOrderCommand = null;
        try {
            createOrderCommand = getObjectFromMessage(message);
            maintenanceWorkOrderService.createWorkOrderAutomatically(createOrderCommand);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            commonUtilsService.writeAddOperationLogs(OperationLogType.MAINTENANCE_WORK_ORRDER, "消费消息队列生成维保工单！" + CommonUtils.getIP(), createOrderCommand, LoginDef.USER_SYSTEM_ID);
            inspectionPlanService.editUsed(createOrderCommand.getId());
        } catch (Exception e) {
            log.error("创建维保工单失败：", e);
            if (createOrderCommand != null) {
                commonUtilsService.writeAddOperationLogs(OperationLogType.MAINTENANCE_WORK_ORRDER, "消费消息队列生成维保工单失败：" + CommonUtils.getIP(), createOrderCommand, LoginDef.USER_SYSTEM_ID);
            }
        }
    }

    private CreateOrderCommand getObjectFromMessage(Message message) {
        byte[] body = message.getBody();
        return JsonUtil.parseObject(new String(body), CreateOrderCommand.class);
    }
}



