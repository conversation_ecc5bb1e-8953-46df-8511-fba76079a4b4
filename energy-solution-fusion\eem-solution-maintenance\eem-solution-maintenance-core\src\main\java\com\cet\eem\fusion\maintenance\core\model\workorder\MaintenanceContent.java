package com.cet.eem.fusion.maintenance.core.model.workorder;

import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.Attachment;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SparePartsReplaceRecordVo;
import com.cet.eem.fusion.maintenance.core.model.EventPlanVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.maintenance.MaintenanceItemExtend;
import com.cet.eem.fusion.maintenance.core.model.workorder.maintenance.SparePartNumber;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.RepairWorkOrderAddDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/20
 */
@Getter
@Setter
@ApiModel(description = "工单内容")
public class MaintenanceContent {
    @ApiModelProperty("巡检参数列表，巡检工单中使用")
    private List<InspectionSchemeDetail> inspectParams;

    @ApiModelProperty("操作用户信息")
    private List<OperationUser> users;

    @ApiModelProperty("附件")
    private List<Attachment> attachments;

    @ApiModelProperty("备件更换记录")
    private List<SparePartsReplaceRecordVo> sparePartsReplaceRecords;

    @ApiModelProperty("预案信息")
    private EventPlanVo eventPlan;

    @ApiModelProperty("运值人员确认时间")
    private Long inspectConfirmTime;

    @ApiModelProperty("维修确认时间")
    private Long repairConfirmTime;

    @ApiModelProperty("创建维修工单数据")
    private RepairWorkOrderAddDto repairWorkOrderAddDto;

    @ApiModelProperty("零部件信息")
    private List<SparePartNumber> sparePartNumbers;

    @ApiModelProperty("维保项目")
    private List<MaintenanceItemExtend> itemExtends;
}

