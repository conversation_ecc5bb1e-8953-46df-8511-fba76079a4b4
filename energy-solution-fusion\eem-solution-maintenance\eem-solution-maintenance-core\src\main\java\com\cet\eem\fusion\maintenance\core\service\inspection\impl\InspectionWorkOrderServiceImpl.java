package com.cet.eem.fusion.maintenance.core.service.inspection.impl;

import com.cet.eem.fusion.common.def.MessageTypeDef;
import com.cet.eem.fusion.common.def.auth.LoginDef;
import com.cet.eem.fusion.common.def.base.ExcelType;
import com.cet.eem.fusion.common.def.energy.EnumSystemEventType;
import com.cet.eem.fusion.common.def.exception.WorkOrderErrorCodeEnum;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.entity.Result;
import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.fusion.common.modelutils.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.utils.ErrorUtils;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.utils.datatype.NumberUtils;
import com.cet.eem.fusion.common.utils.datatype.StringFormatUtils;
import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;
import com.cet.eem.fusion.common.utils.notice.WebNotification;
import com.cet.eem.fusion.config.sdk.def.OperationLogType;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.activiti.WorksheetAbnormalReason;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.*;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.ProcessFlowUnit;
import com.cet.eem.fusion.maintenance.core.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.maintenance.core.entity.po.InspectionParameter;
import com.cet.eem.fusion.maintenance.core.model.*;
import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;
import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;
import com.cet.eem.fusion.config.sdk.service.log.EemLogService;
import com.cet.eem.fusion.maintenance.core.entity.bo.PlanSheetWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.common.entity.ParentParam;
import com.cet.eem.fusion.maintenance.core.config.MaintenanceConfig;
import com.cet.eem.fusion.maintenance.core.dao.*;
import com.cet.eem.fusion.maintenance.core.def.*;
import com.cet.eem.fusion.maintenance.core.model.inspector.QueryInspectorRequest;
import com.cet.eem.fusion.maintenance.core.model.sign.SignGroupStatusGroup;
import com.cet.eem.fusion.maintenance.core.model.wo.WorkOrderSimpleQueryDTO;
import com.cet.eem.fusion.maintenance.core.model.workorder.DevicePlanRelationshipSaveVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.MaintenanceContent;
import com.cet.eem.fusion.maintenance.core.model.workorder.OperationUser;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.*;
import com.cet.eem.fusion.maintenance.core.schedule.event.CreateOrderCommand;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionWorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorService;
import com.cet.eem.fusion.maintenance.core.service.singinpoint.SignInService;
import com.cet.eem.fusion.maintenance.core.service.singinpoint.SignInStatusRecordService;
import com.cet.eem.fusion.maintenance.core.utils.InspectorUserCheckUtils;
import com.cet.eem.fusion.maintenance.core.utils.WorkSheetStatusUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.common.config.EemRedisTemplateConfig;
import com.cet.eem.fusion.common.constant.PecsNodeType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.common.utils.ArrayUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.electric.workflow.api.TriggerRestApi;
import com.cet.electric.workflow.api.UserTaskRestApi;
import com.cet.electric.workflow.common.constants.ProcessVariableDefinition;
import com.cet.electric.workflow.common.model.ProcessInstanceResponse;
import com.cet.electric.workflow.common.model.node.config.UserTaskConfig;
import com.cet.electric.workflow.common.model.params.MultiTableTriggerParams;
import com.cet.electric.workflow.common.model.params.TableTriggerParams;
import com.cet.electric.workflow.common.model.params.UserTaskParams;
import com.cet.futureblue.i18n.LanguageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.cet.eem.fusion.common.def.common.ContentTypeDef;

/**
 * <AUTHOR>
 * @date 4/12/2021
 */
@Service
@Slf4j
public class InspectionWorkOrderServiceImpl implements InspectionWorkOrderService {

    @Autowired
    WorkOrderDao workOrderDao;

    @Autowired
    InspectionWorkOrderDao inspectionWorkOrderDao;

    @Autowired
    UserTaskRestApi workflowService;

    @Autowired
    TriggerRestApi triggerRestApi;

    @Autowired
    InspectionSchemeDao inspectionSchemeDao;

    @Autowired
    PlanSheetDao planSheetDao;

    @Resource
    private WebNotification webNotification;

    @Autowired
    NodeDao nodeDao;

    @Autowired
    InspectorService inspectorService;

    @Resource
    UserRestApi userRestApi;

    @Autowired
    EemLogService eemLogService;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    InspectionParameterDao inspectionParameterDao;

    @Autowired
    WorkOrderCheckInfoDao workOrderCheckInfoDao;

    @Autowired
    WorkOrderService workOrderService;

    @Autowired
    SignInGroupDao signInGroupDao;

    @Autowired
    SignInService signInService;

    @Autowired
    SignInPointDao signInPointDao;

    @Autowired
    SignInStatusRecordService signInStatusRecordService;

    @Autowired
    SignInStatusRecordDao signInStatusRecordDao;

    @Autowired
    WorkSheetStatusUtils workSheetStatusUtils;

    @Autowired
    InspectorUserCheckUtils inspectorUserCheckUtils;

    // EemCloudAuthService已废弃，使用UserRestApi替代

    @Autowired
    @Qualifier(EemRedisTemplateConfig.EEM_REDIS_TEMPLATE)
    RedisTemplate<String, String> redisTemplate;

    @Autowired
    MaintenanceConfig maintenanceConfig;

    /**
     * 导出工单最大数量
     */
    @Value("${cet.eem.event.inspection.export-max-size: 2000}")
    private int exportMaxCount;

    /**
     * 消息即将超时推送预警提前时间
     */
    @Value("${cet.eem.work-order.inspect.check-over-time-notice.pre-time}")
    private int noticeMinute;

    private static final String EEM_INSPECT_WORK_ORDER_CHECK_OVER_TIME = "eem:inspect_work_order:check_over_time:";

    @Override
    public ApiResult<List<InspectionParameterWorkOrderDTO>> queryInspectionParameterWorkOrder(InspectionSearchDto dto) {
        // 查询用户信息和班组信息
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();

        // 判断当前用户是否为巡检用户，对于巡检用户只能看自己班组的工单
        dto.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(dto.getTeamId(), user));

        // 判断用户所属班组，如果班组用户，那么只允许查询本班组的工单
        ApiResult<List<InspectionParameterWorkOrderDTO>> result = inspectionWorkOrderDao.queryInspectionParameterWorkOrder(dto, GlobalInfoUtils.getUserId());
        List<InspectionParameterWorkOrderDTO> workOrderList = result.getData();
        return Result.ok(workOrderList, result.getTotal());
    }

    @Override
    public ApiResult<List<InspectionWorkOrderDto>> queryWorkOrderList(InspectionSearchDto dto) {
        // 查询用户信息和班组信息
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();

        // 判断当前用户是否为巡检用户，对于巡检用户只能看自己班组的工单
        dto.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(dto.getTeamId(), user));

        // 判断用户所属班组，如果班组用户，那么只允许查询本班组的工单
        ApiResult<List<InspectionWorkOrderDto>> result = inspectionWorkOrderDao.queryWorkOrder(dto, GlobalInfoUtils.getUserId());
        List<InspectionWorkOrderDto> workOrderList = result.getData();
        assemblyCommonData(workOrderList, dto.getTenantId());
        return Result.ok(workOrderList, result.getTotal());
    }

    /**
     * 点检曲线查询已经完成的工单信息，时间参数比较的也是工单的完成时间
     *
     * @param dto
     * @return
     */
    @Override
    public ApiResult<List<InspectionWorkOrderDto>> queryFinshWorkOrderList(InspectionSearchDto dto) {
        // 查询用户信息和班组信息
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();
        // 判断当前用户是否为巡检用户，对于巡检用户只能看自己班组的工单
        dto.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(dto.getTeamId(), user));
        // 判断用户所属班组，如果班组用户，那么只允许查询本班组的工单
        ApiResult<List<InspectionWorkOrderDto>> listResultWithTotal = workOrderDao.queryFinishWorkOrder(dto, GlobalInfoUtils.getUserId());
        List<InspectionWorkOrderDto> workOrderList = listResultWithTotal.getData();
        assemblyCommonData(workOrderList, dto.getTenantId());
        return Result.ok(workOrderList, listResultWithTotal.getTotal());
    }

    @Override
    public List<WorkOrderCountDto> queryWorkOrderCount(InspectionCountSearchDto dto) {
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();
        dto.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(dto.getTeamId(), user));

        List<Map<String, Object>> maps = workOrderDao.queryWorkOrderCountByStatus(dto);
        Map<Integer, String> statusMap = workSheetStatusUtils.getWorkSheetStatusMapByTaskType(WorkSheetTaskType.INSPECTION);
        Map<Integer, WorkOrderCountDto> statusCountMap = new HashMap<>();

        // 解析结果
        for (Map<String, Object> map : maps) {
            WorkOrderCountDto workOrderCountDto = new WorkOrderCountDto();
            Integer status = NumberUtils.parseInteger(map.get(WorkOrderDef.WORKSHEET_STATUS));
            Integer count = NumberUtils.parseInteger(map.get(ColumnDef.COUNT_ID));
            workOrderCountDto.setCount(count);
            workOrderCountDto.setWorkOrderStatus(status);
            workOrderCountDto.setWorkOrderStatusName(statusMap.get(status));
            statusCountMap.put(status, workOrderCountDto);
        }

        if (CollectionUtils.isEmpty(dto.getWorkSheetStatuses())) {
            return new ArrayList<>(statusCountMap.values());
        }

        List<WorkOrderCountDto> result = new ArrayList<>();
        for (Integer workSheetStatus : dto.getWorkSheetStatuses()) {
            WorkOrderCountDto workOrderCountDto = statusCountMap.get(workSheetStatus);
            if (workOrderCountDto == null) {
                workOrderCountDto = new WorkOrderCountDto(workSheetStatus, statusMap.get(workSheetStatus));
            }
            result.add(workOrderCountDto);
        }
        return result;
    }

    @Override
    public ProcessInstanceResponse createWorkOrder(InspectionAddDto dto) {
        Long userId = GlobalInfoUtils.getUserId();
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(userId);
        UserVo user = usrRes.getData();

        // 校验巡检对象与签到点是否匹配
        SignInGroupWithEquipment signInGroupWithEquipment = signInGroupDao.querySignInGroupWithEquipment(dto.getSignGroupId());
        List<SignInEquipment> signInEquipmentList = signInGroupWithEquipment.getSignInEquipmentList();
        Assert.notNull(signInEquipmentList, "巡检对象与签到点分组不匹配！");
        boolean flag = signInEquipmentList.stream().anyMatch(it -> Objects.equals(it.getObjectId(), dto.getObjectId()) && Objects.equals(it.getObjectLabel(), dto.getObjectLabel()));
        Assert.isTrue(flag, "巡检对象与签到点分组不匹配！");

        // 设置工单
        TableTriggerParams tableTriggerParams = new TableTriggerParams();
        tableTriggerParams.setProcessDefinitionKey(WorkOrderKeyDef.INSPECTION_KEY);

        Map<String, Object> processVariables = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
        processVariables.put(ProcessVariableDefinition.CANDICATE_GROUPS, String.valueOf(dto.getTeamId()));
        processVariables.put(ProcessVariableDefinition.TENANT_ID, user.getTenantId());
        tableTriggerParams.setProcessVariables(processVariables);

        Map<String, Object> formDataList = createWorkOrderByUser(dto, user);
        tableTriggerParams.setFormData(formDataList);

        ApiResult<ProcessInstanceResponse> result = workOrderDao.startProcessByTable(userId, tableTriggerParams);
        ParamUtils.checkResultGeneric(result);

        ParentParam parentParam = new ParentParam();
        parentParam.setTenantId(GlobalInfoUtils.getTenantId());
        parentParam.setUserId(GlobalInfoUtils.getUserId());
        eemLogService.writeAddOperationLogs(OperationLogType.INSPECTOR_WORK_ORDER, "手动创建工单", new Object[]{dto}, parentParam);
        return result.getData();
    }

    /**
     * 用户手动创建工单
     *
     * @param dto  工单参数
     * @param user 用户信息
     * @return 工单入库数据
     */
    private Map<String, Object> createWorkOrderByUser(InspectionAddDto dto, UserVo user) {
        Map<String, Object> result = new HashMap<>();
        result.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        result.put(ColumnDef.TASK_TYPE, WorkSheetTaskType.INSPECTION);
        result.put(WorkOrderDef.EXECUTE_TIME_PLAN, TimeUtil.localDateTime2timestamp(dto.getExecuteTimePlan()));
        result.put(WorkOrderDef.TIME_CONSUME_PLAN, dto.getTimeConsumePlan());
        result.put(WorkOrderDef.TEAM_ID, dto.getTeamId());
        result.put(WorkOrderDef.SIGN_GROUP_ID, dto.getSignGroupId());
        result.put(WorkOrderDef.INSPECTION_SCHEME_ID, dto.getInspectionSchemeId());
        result.put(WorkOrderDef.WORKSHEET_STATUS, WorkSheetStatusDef.TO_BE_SENT);
        result.put(ColumnDef.PROJECT_ID, GlobalInfoUtils.getTenantId());
        result.put(WorkOrderDef.DEVICE_PLAN_RELATIONSHIP_MODEL,
                Collections.singletonList(new DevicePlanRelationshipSaveVo(dto.getObjectId(), dto.getObjectLabel())));

        SignInPointWithSubLayer signPoint = signInService.querySignInPoint(dto.getSignGroupId(), new BaseVo(dto.getObjectId(), dto.getObjectLabel()));
        Assert.notNull(signPoint, "获取签到点信息失败！");
        result.put(WorkOrderDef.SIGN_POINT_ID, signPoint.getId());

        MaintenanceContent maintenanceContent = new MaintenanceContent();
        List<InspectionSchemeDetail> schemeDetails = getInspectionSchemeDetails(dto.getInspectionSchemeId());
        if (CollectionUtils.isEmpty(schemeDetails)) {
            throw new ValidationException(String.format("id=%s巡检方案未添加巡检参数，不允许根据其创建工单！", dto.getInspectionSchemeId()));
        }
        maintenanceContent.setInspectParams(schemeDetails);
        result.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceContent));

        // 创建者信息
        result.put(WorkOrderDef.CREATOR, user.getId());
        result.put(WorkOrderDef.CREATE_TIME, ProcessVariableDefinition.NOW_TIME_SYMBOL);
        result.put(ColumnDef.TENANT_ID, user.getTenantId());

        // 工单服务自动生成字段
        result.put(WorkOrderDef.CODE, ProcessVariableDefinition.CODE_SYMBOL);

        return result;
    }

    private List<InspectionSchemeDetail> getInspectionSchemeDetails(Long inspectionSchemeId) {
        InspectionSchemeWithSubLayer inspectionSchemeWithSubLayer = inspectionSchemeDao.selectRelatedById(InspectionSchemeWithSubLayer.class, inspectionSchemeId);
        if (inspectionSchemeWithSubLayer == null) {
            throw new ValidationException(String.format("id=%s的巡检方案不存在，无法创建工单！", inspectionSchemeId));
        }
        List<InspectionSchemeDetail> schemeDetails = inspectionSchemeWithSubLayer.getSchemeDetails();
        if (CollectionUtils.isEmpty(schemeDetails)) {
            return Collections.emptyList();
        }

        Set<Long> paraIds = schemeDetails.stream().map(InspectionSchemeDetail::getInspectionParameterId).collect(Collectors.toSet());
        List<InspectionParameter> inspectionParameters = inspectionParameterDao.selectBatchIds(paraIds);
        Map<Long, String> inspectParameterMap = inspectionParameters.stream().collect(Collectors.toMap(EntityWithName::getId, EntityWithName::getName));
        for (InspectionSchemeDetail schemeDetail : schemeDetails) {
            schemeDetail.setParaName(inspectParameterMap.get(schemeDetail.getInspectionParameterId()));
        }
        return schemeDetails;
    }

    @Override
    public boolean createWorkOrderByPlanSheet(CreateOrderCommand command) {
        PlanSheetWithSubLayer planSheetWithSubLayer = planSheetDao.selectRelatedById(PlanSheetWithSubLayer.class, command.getId());
        if (planSheetWithSubLayer == null) {
            log.error("id={}的巡检计划已被删除", command.getId());
            return false;
        }

        if (CollectionUtils.isEmpty(planSheetWithSubLayer.getDevicePlanRelationshipList())) {
            log.error("id={}的巡检计划无关联巡检对象！", command.getId());
            return false;
        }
        Assert.notNull(command.getFireTime(), "计划开始执行时间不允许为空！");
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(planSheetWithSubLayer.getCreator());
        UserVo userVo = usrRes.getData();
        MaintenanceContent maintenanceContent = new MaintenanceContent();
        List<InspectionSchemeDetail> schemeDetails = getInspectionSchemeDetails(planSheetWithSubLayer.getInspectionSchemeId());
        maintenanceContent.setInspectParams(schemeDetails);
        if (CollectionUtils.isEmpty(schemeDetails)) {
            log.error("id={}的巡检计划无巡检参数！", command.getId());
            return false;
        }

        // 根据计划生成工单数据
        List<Map<String, Object>> workOrders = geneWorkOrderByPlanSheet(command, planSheetWithSubLayer, userVo.getTenantId(), maintenanceContent);
        // 对计划进行分组（循环批量创建工单）
        List<List<Map<String, Object>>> woGroups = ListUtils.partition(workOrders, 5);
        log.info("【系统自动创建巡检工单】巡检计划id={}，单个计划涉及个{}节点，分{}组创建", planSheetWithSubLayer.getId(), workOrders.size(), woGroups.size());
        for (int i = 0; i < woGroups.size(); i++) {
            createWorkOrders(planSheetWithSubLayer, woGroups.get(i));
            log.info("【系统自动创建巡检工单】巡检计划id={}，第{}组创建", planSheetWithSubLayer.getId(), i + 1);
        }

        return true;
    }

    /**
     * 创建工单
     *
     * @param planSheet  巡检计划
     * @param workOrders 入库工单数据
     */
    private void createWorkOrders(PlanSheet planSheet, List<Map<String, Object>> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return;
        }

        MultiTableTriggerParams multiTableTriggerParams = new MultiTableTriggerParams();
        multiTableTriggerParams.setProcessDefinitionKey(WorkOrderKeyDef.INSPECTION_KEY);
        multiTableTriggerParams.setFormDataList(workOrders);

        Map<String, Object> processVariables = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
        // 班组格式为字符串，多个班组使用英文,分割
        String groups = String.valueOf(planSheet.getTeamId());
        processVariables.put(ProcessVariableDefinition.CANDICATE_GROUPS, groups);
        multiTableTriggerParams.setProcessVariables(processVariables);
        multiTableTriggerParams.setUserName(LoginDef.USER_SYSTEM_NAME);
        ApiResult<List<ProcessInstanceResponse>> listApiResult = workOrderDao.startProcessesByManyTables(LoginDef.USER_SYSTEM_ID, multiTableTriggerParams);
        ParamUtils.checkResultGeneric(listApiResult);
        // 创建工单，记录日志
        ParentParam parentParam = new ParentParam();
        parentParam.setTenantId(GlobalInfoUtils.getTenantId());
        parentParam.setUserId(LoginDef.USER_SYSTEM_ID);
        eemLogService.writeAddOperationLogs(OperationLogType.INSPECTOR_WORK_ORDER, "系统自动创建工单", new Object[]{multiTableTriggerParams}, parentParam);
    }

    /**
     * 根据计划生成需要创建工单的数据
     *
     * @param createInspectionOrderCommand 巡检计划信息
     * @return 工单入库数据
     */
    private List<Map<String, Object>> geneWorkOrderByPlanSheet(CreateOrderCommand createInspectionOrderCommand, PlanSheetWithSubLayer planSheet, Long tenantId, MaintenanceContent maintenanceContent) {
        List<DevicePlanRelationship> devicePlanRelationShips = planSheet.getDevicePlanRelationshipList();

        List<BaseVo> nodeList = devicePlanRelationShips.stream()
                .filter(it -> it.getEnabled() == null || it.getEnabled())
                .map(it -> new BaseVo(it.getDeviceId(), it.getDeviceLabel()))
                .distinct()
                .collect(Collectors.toList());

        List<Map<String, Object>> workOrders = new ArrayList<>();
        for (BaseVo node : nodeList) {
            Map<String, Object> workOrder = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
            workOrder.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
            workOrder.put(ColumnDef.TASK_TYPE, WorkSheetTaskType.INSPECTION);
            workOrder.put(WorkOrderDef.EXECUTE_TIME_PLAN, createInspectionOrderCommand.getScheduledFireTime());
            workOrder.put(WorkOrderDef.TIME_CONSUME_PLAN, planSheet.getTimeConsumePlan());
            workOrder.put(WorkOrderDef.TEAM_ID, planSheet.getTeamId());
            workOrder.put(WorkOrderDef.SIGN_GROUP_ID, planSheet.getSignGroupId());
            workOrder.put(ColumnDef.PROJECT_ID, planSheet.getTenantId());
            workOrder.put(WorkOrderDef.PLANSHEET_ID, planSheet.getId());
            workOrder.put(WorkOrderDef.INSPECTION_SCHEME_ID, planSheet.getInspectionSchemeId());

            SignInPointWithSubLayer signPoint = signInService.querySignInPoint(planSheet.getSignGroupId(), new BaseVo(node.getId(), node.getModelLabel()));
            if (Objects.isNull(signPoint)) {
                log.warn("Cannot fount signin point record, planSheetId=[{}], signGroupId=[{}], node=[id={}, modelLabel={}]", planSheet.getId(), planSheet.getSignGroupId(), node.getId(), node.getModelLabel());
                Map<String, Object> map = new HashMap<>();
                map.put("planSheetId", planSheet.getId());
                map.put("signGroupId", planSheet.getSignGroupId());
                map.put("node", node);
                ParentParam parentParam = new ParentParam();
                parentParam.setTenantId(GlobalInfoUtils.getTenantId());
                parentParam.setUserId(LoginDef.USER_SYSTEM_ID);
                eemLogService.writeAddOperationLogs(OperationLogType.INSPECTOR_WORK_ORDER, "签到点分组与节点不匹配", new Object[]{map}, parentParam);
                continue;
            }

            workOrder.put(WorkOrderDef.SIGN_POINT_ID, signPoint.getId());
            workOrder.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceContent));

            workOrder.put(WorkOrderDef.CREATE_TIME, ProcessVariableDefinition.NOW_TIME_SYMBOL);
            workOrder.put(WorkOrderDef.CODE, ProcessVariableDefinition.CODE_SYMBOL);
            workOrder.put(WorkOrderDef.WORKSHEET_STATUS, WorkSheetStatusDef.TO_BE_SENT);
            workOrder.put(ColumnDef.TENANT_ID, tenantId);
            workOrder.put(WorkOrderDef.DEVICE_PLAN_RELATIONSHIP_MODEL,
                    Collections.singletonList(new DevicePlanRelationshipSaveVo(node.getId(), node.getModelLabel())));
            workOrders.add(workOrder);
        }
        return workOrders;
    }

    @Override
    public InspectionWorkOrderDto queryWorkOrder(Long workOrderId) {
        if (!ParamUtils.checkPrimaryKeyValid(workOrderId)) {
            throw new ValidationException("工单id不合法!");
        }

        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();
        InspectionWorkOrderDto workOrder = workOrderDao.queryWorkOrder(workOrderId, InspectionWorkOrderDto.class);

        assemblyCommonData(Collections.singletonList(workOrder), user.getTenantId());
        return workOrder;
    }

    @Override
    public InspectionWorkOrderDto queryWorkOrder(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ValidationException("工单号不允许为空!");
        }

        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();
        InspectionWorkOrderDto workOrder = workOrderDao.queryWorkOrder(code, InspectionWorkOrderDto.class);
        if (workOrder == null) {
            throw new ValidationException(LanguageUtil.getMessage(WorkOrderErrorCodeEnum.RUNTIME_WORK_ORDER_NOT_FOUNT.getMsg()));
        }

        assemblyCommonData(Collections.singletonList(workOrder), user.getTenantId());
        return workOrder;
    }

    @Override
    public UserTaskConfig queryTaskConfig(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ValidationException("工单号不允许为空!");
        }

        InspectionWorkOrderDto workOrder = workOrderService.queryRuntimeWorkOrder(code);
        ApiResult<UserTaskConfig> userTaskConfig = workflowService.getTaskConfig(GlobalInfoUtils.getUserId(), workOrder.getTaskId());
        ParamUtils.checkResultGeneric(userTaskConfig);
        return userTaskConfig.getData();
    }

    /**
     * 组装
     *
     * @param workOrderList
     * @param tenantId
     */
    @Override
    public void assemblyCommonData(List<InspectionWorkOrderDto> workOrderList, Long tenantId) {
        if (CollectionUtils.isEmpty(workOrderList)) {
            return;
        }
        Set<Long> inspectSchemeIds = new HashSet<>();
        Set<Long> signPointIds = new HashSet<>();
        Set<Long> planSheetIds = new HashSet<>();
        Set<BaseVo> tmpNodes = new HashSet<>();
        Set<Long> executeUserIds = new HashSet<>();
        resolveData(workOrderList, inspectSchemeIds, planSheetIds, tmpNodes, executeUserIds, signPointIds);
        // 查询巡检方案
        List<InspectionSchemeWithSubLayer> inspectionSchemes = queryInspectSchemes(inspectSchemeIds);
        Map<Long, String> schemeNameMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
        if (CollectionUtils.isNotEmpty(inspectionSchemes)) {
            schemeNameMap = inspectionSchemes.stream().collect(Collectors.toMap(InspectionSchemeWithSubLayer::getId, EntityWithName::getName, (v1, v2) -> v1));
        }
        // 查询巡检计划
        Map<Long, String> planSheetMap = queryPlanSheet(planSheetIds);
        // 查询节点
        Map<BaseVo, String> nodeMap = nodeDao.queryNodeNameMap(tmpNodes);
        // 查询用户信息
        Map<Long, String> userMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        Map<Long, String> groupMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        queryUserInfo(tenantId, executeUserIds, userMap, groupMap);
        Set<Long> workOrderIds = workOrderList.stream().map(EntityWithName::getId).collect(Collectors.toSet());
        List<ProcessFlowUnit> processFlowUnits = workOrderDao.queryProcessFlowUnit(workOrderIds);
        Map<String, UserTaskConfig> taskConfigMap = workOrderDao.queryTaskConfigList(workOrderIds, tenantId);
        Map<Long, List<ProcessFlowUnit>> processFlowUnitMap = processFlowUnits.stream().collect(Collectors.groupingBy(ProcessFlowUnit::getEventid));
        Map<Integer, String> workStatusMap = workSheetStatusUtils.getWorkSheetStatusMapByTaskType(WorkSheetTaskType.INSPECTION);
        Map<Integer, String> abnormalReasonMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.ABNORMAL_REASON);
        // 查询签到点信息
        List<SignInPoint> signInPoints = signInPointDao.selectBatchIds(signPointIds);
        Map<Long, String> signinPointMap = signInPoints.stream().collect(Collectors.toMap(EntityWithName::getId, EntityWithName::getName, (v1, v2) -> v1));

        for (InspectionWorkOrderDto dto : workOrderList) {
            dto.setInspectionSchemeName(schemeNameMap.get(dto.getInspectionSchemeId()));
            dto.setPlanName(planSheetMap.get(dto.getPlanSheetId()));
            dto.setTeamName(groupMap.get(dto.getTeamId()));
            dto.setStaffName(userMap.get(dto.getStaff()));
            dto.setCreatorName(userMap.get(dto.getCreator()));
            dto.setWorkSheetStatusName(workStatusMap.get(dto.getWorkSheetStatus()));
            dto.setProcessFlowUnits(processFlowUnitMap.get(dto.getId()));
            dto.setUserTaskConfig(taskConfigMap.get(dto.getCode()));
            dto.setSignPointName(signinPointMap.get(dto.getSignPointId()));

            if (dto.getAbnormalReasonList() != null) {
                for (WorksheetAbnormalReasonDto reason : dto.getAbnormalReasonList()) {
                    reason.setTypeName(abnormalReasonMap.get(reason.getType()));
                }
            }

            // 匹配巡检对象名称
            List<DevicePlanRelationship> devicePlanRelationshipList = dto.getDevicePlanRelationshipList();
            if (CollectionUtils.isEmpty(devicePlanRelationshipList)) {
                continue;
            }
            for (DevicePlanRelationship devicePlanRelationship : devicePlanRelationshipList) {
                devicePlanRelationship.setDeviceName(nodeMap.get(new BaseVo(devicePlanRelationship.getDeviceId(), devicePlanRelationship.getDeviceLabel())));
            }
        }
    }

    /**
     * 提取数据
     *
     * @param workOrderList
     * @param inspectSchemeIds
     * @param planSheetIds
     * @param tmpNodes
     * @param executeUserIds
     */
    private void resolveData(List<InspectionWorkOrderDto> workOrderList, Set<Long> inspectSchemeIds, Set<Long> planSheetIds, Set<BaseVo> tmpNodes, Set<Long> executeUserIds,
                             Set<Long> signPointIds) {
        for (InspectionWorkOrderDto inspectionWorkOrderDto : workOrderList) {
            inspectSchemeIds.add(inspectionWorkOrderDto.getInspectionSchemeId());
            // 手动创建的工单该值为null
            if (ParamUtils.checkPrimaryKeyValid(inspectionWorkOrderDto.getPlanSheetId())) {
                planSheetIds.add(inspectionWorkOrderDto.getPlanSheetId());
            }

            // 巡检对象
            List<DevicePlanRelationship> ships = inspectionWorkOrderDto.getDevicePlanRelationshipList();
            if (CollectionUtils.isNotEmpty(ships)) {
                for (DevicePlanRelationship ship : ships) {
                    tmpNodes.add(new BaseVo(ship.getDeviceId(), ship.getDeviceLabel()));
                }
            }

            // 巡单人员
            if (ParamUtils.checkPrimaryKeyValid(inspectionWorkOrderDto.getStaff())) {
                executeUserIds.add(inspectionWorkOrderDto.getStaff());
            }

            // 创建工单
            if (ParamUtils.checkPrimaryKeyValid(inspectionWorkOrderDto.getCreator())) {
                executeUserIds.add(inspectionWorkOrderDto.getCreator());
            }

            if (inspectionWorkOrderDto.getSignPointId() != null) {
                signPointIds.add(inspectionWorkOrderDto.getSignPointId());
            }
        }
    }

    /**
     * 查询用户信息
     *
     * @param tenantId
     * @param executeUserIds
     * @param userMap
     * @param groupMap
     */
    private void queryUserInfo(Long tenantId, Set<Long> executeUserIds, Map<Long, String> userMap, Map<Long, String> groupMap) {
        for (Long executeUserId : executeUserIds) {
            if (userMap.containsKey(executeUserId)) {
                continue;
            }

            // 使用UserRestApi查询用户信息
            try {
                ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(executeUserId);
                if (usrRes.isSuccess() && usrRes.getData() != null) {
                    UserVo userVo = usrRes.getData();
                    userMap.put(userVo.getId(), userVo.getName());
                }
            } catch (Exception e) {
                log.warn("查询用户信息失败，用户ID: {}", executeUserId, e);
            }
        }
    }

    private Map<Long, String> queryPlanSheet(Set<Long> planSheetIds) {
        List<PlanSheet> planSheets = planSheetDao.selectBatchIds(planSheetIds);
        Map<Long, String> planSheetMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
        if (CollectionUtils.isNotEmpty(planSheets)) {
            planSheetMap = planSheets.stream().collect(Collectors.toMap(EntityWithName::getId, EntityWithName::getName, (v1, v2) -> v1));
        }
        return planSheetMap;
    }

    private List<InspectionSchemeWithSubLayer> queryInspectSchemes(Set<Long> inspectSchemeIds) {
        LambdaQueryWrapper<InspectionScheme> wrapper = LambdaQueryWrapper.of(InspectionScheme.class)
                .in(EntityWithName::getId, inspectSchemeIds);
        return inspectionSchemeDao.selectRelatedList(InspectionSchemeWithSubLayer.class, wrapper);
    }

    @Override
    public void updateWorkOrder(InspectParamsWriteVo inspectParamsUpdateVo) {
        Map<String, Object> params = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
        params.put(ColumnDef.ID, inspectParamsUpdateVo.getId());
        params.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(inspectParamsUpdateVo.getParams()));
        params.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);

        ApiResult<List<Map<String, Object>>> result = workOrderDao.saveModelEntityList(Collections.singletonList(params));
    }

    @Override
    public void submitInspectParams(InspectParamsWriteVo inspectParamsPointVo) {
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();
        workOrderService.checkTaskNodes(inspectParamsPointVo.getCode(), TaskNodeDef.SUBMIT_CHECK_PARAMS);

        // 查询工单
        InspectionWorkOrderDto workOrder = workOrderService.queryRuntimeWorkOrder(inspectParamsPointVo.getCode());
        Integer taskResult = NumberUtils.parseInteger(inspectParamsPointVo.getTaskObject());

        MaintenanceContent maintenanceContent = JsonTransferUtils.parseObject(workOrder.getMaintenanceContent(), MaintenanceContent.class);
        if (maintenanceContent == null) {
            maintenanceContent = new MaintenanceContent();
        }
        maintenanceContent.setRepairWorkOrderAddDto(inspectParamsPointVo.getRepairWorkOrderAddDto());
        checkInspectParams(inspectParamsPointVo, workOrder.getExecuteTimePlan(), maintenanceContent.getInspectParams(), taskResult);
        maintenanceContent.setUsers(inspectParamsPointVo.getUsers());

        UserTaskParams userTaskParams = new UserTaskParams();
        userTaskParams.setTaskResult(inspectParamsPointVo.getTaskObject());

        Map<String, Object> formData = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        formData.put(ColumnDef.ID, workOrder.getId());
        formData.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        formData.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceContent));
        formData.put(WorkOrderDef.STAFF, user.getId());
        formData.put(WorkOrderDef.RELATED_CODE, inspectParamsPointVo.getRelatedCode());
        formData.put(WorkOrderDef.RELATED_CODE_ID, inspectParamsPointVo.getRelatedCodeId());
        formData.put(WorkOrderDef.EXECUTE_TIME, inspectParamsPointVo.getExecuteTime());
        if (CollectionUtils.isNotEmpty(inspectParamsPointVo.getAttachment())) {
            formData.put(WorkOrderDef.ATTACHMENT, JsonTransferUtils.toJSONString(inspectParamsPointVo.getAttachment()));
        }
        formData.put(WorkOrderDef.HANDLE_DESCRIPTION, inspectParamsPointVo.getHandleDescription());
        formData.put(WorkOrderDef.FINISH_TIME, inspectParamsPointVo.getFinishTime() != null ? TimeUtil.localDateTime2timestamp(inspectParamsPointVo.getFinishTime()) : null);

        setAbnormalReasons(inspectParamsPointVo, workOrder, taskResult, formData);
        userTaskParams.setFormData(formData);

        workOrderDao.saveModelEntityList(Collections.singletonList(copyFormDataWithOutReason(formData)));

        workOrderDao.submitForm(user.getId(), workOrder.getTaskId(), userTaskParams);

        // 首次提交的时候更新签到点状态
        if (workOrder.getStaff() == null) {
            signInStatusRecordService.updateSignInStatus(workOrder.getSignPointId(), workOrder.getSignGroupId(), taskResult == InspectResultDef.NORMAL ? SignInStatusDef.NORMAL : SignInStatusDef.ABNORMAL);
        }
    }

    /**
     * 拷贝需要入库的表单数据，提出异常原因
     *
     * @param formData
     * @return
     */
    private Map<String, Object> copyFormDataWithOutReason(Map<String, Object> formData) {
        Map<String, Object> result = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        formData.forEach((key, val) -> {
            if (Objects.equals(key, WorkOrderDef.WORKSHEET_ABNORMAL_REASON_MODEL)) {
                return;
            }
            result.put(key, val);
        });

        return result;
    }

    /**
     * 校验数据
     *
     * @param inspectParamsPointVo
     * @param executeTimePlan
     * @param taskResult
     */
    private void checkInspectParams(InspectParamsWriteVo inspectParamsPointVo, LocalDateTime executeTimePlan, List<InspectionSchemeDetail> inspectionSchemeDetails, Integer taskResult) {
        // 校验数据
        if (inspectParamsPointVo.getExecuteTime() == null) {
            throw new ValidationException("开始巡检时间不允许为空！");
        } else {
            if (inspectParamsPointVo.getExecuteTime().isBefore(executeTimePlan)) {
                throw new ValidationException("实际巡检开始时间不允许早于计划巡检时间！");
            }
        }
        // 校验巡检参数
        boolean inspectResult = checkInspectParams(inspectParamsPointVo.getParams(), inspectionSchemeDetails);
        if ((!inspectResult || inspectParamsPointVo.isAbnormal()) && taskResult == InspectResultDef.NORMAL) {
            throw new ValidationException("巡检数据异常，但巡检结果为正常，不允许保存！");
        }
    }

    /**
     * 设置工单异常原因
     *
     * @param inspectParamsPointVo
     * @param workOrder
     * @param taskResult
     * @param formData
     */
    private void setAbnormalReasons(InspectParamsWriteVo inspectParamsPointVo, InspectionWorkOrderDto workOrder, Integer taskResult, Map<String, Object> formData) {
        List<WorksheetAbnormalReason> abnormalReasons = workOrderDao.queryWorksheetAbnormalReason(workOrder.getId());

        if (inspectParamsPointVo.isAbnormal()) {
            boolean flag = abnormalReasons.stream().anyMatch(it -> it.getType() == AbnormalReasonDef.SKIP_SIGN_POINT);
            if (!flag) {
                abnormalReasons.add(new WorksheetAbnormalReason(AbnormalReasonDef.SKIP_SIGN_POINT, workOrder.getId()));
            }
        }

        if (taskResult == InspectResultDef.REPAIR_BY_SELF) {
            boolean flag = abnormalReasons.stream().anyMatch(it -> it.getType() == AbnormalReasonDef.REPAIR_BY_SELF);
            if (!flag) {
                abnormalReasons.add(new WorksheetAbnormalReason(AbnormalReasonDef.REPAIR_BY_SELF, workOrder.getId()));
            }
        } else if (taskResult == InspectResultDef.CREATE_REPAIR_WORK_ORDER || taskResult == InspectResultDef.SELECT_REPAIR_WORK_ORDER) {
            boolean flag = abnormalReasons.stream().anyMatch(it -> it.getType() == AbnormalReasonDef.REPAIR);
            if (!flag) {
                abnormalReasons.add(new WorksheetAbnormalReason(AbnormalReasonDef.REPAIR, workOrder.getId()));
            }
        }

        if (CollectionUtils.isNotEmpty(abnormalReasons)) {
            formData.put(WorkOrderDef.WORKSHEET_ABNORMAL_REASON_MODEL, abnormalReasons);
            formData.put(WorkOrderDef.BUSSINESS_STATUS, WorkSheetStatusDef.ABNORMAL);
        }
    }

    /**
     * 校验巡检参数是否完成合法，以及是否存在异常
     *
     * @param schemeDetails
     * @param params
     * @return
     */
    private boolean checkInspectParams(List<InspectParams> params, List<InspectionSchemeDetail> schemeDetails) {
        boolean inspectResult = true;
        if (CollectionUtils.isEmpty(schemeDetails)) {
            return inspectResult;
        }
        Map<Long, InspectParams> paramMap = params.stream().collect(Collectors.toMap(InspectParams::getParamId, Function.identity(), (v1, v2) -> v1));
        for (InspectionSchemeDetail schemeDetail : schemeDetails) {
            InspectParams inspectParams = paramMap.get(schemeDetail.getId());
            if (inspectParams == null) {
                throw new ValidationException("巡检内容有缺失，不允许入库！");
            }

            // 状态量
            if (schemeDetail.getType() == ParameterTypeDef.STATUS_QUANTITY) {
                schemeDetail.setStatus(inspectParams.getStatus());
                if (inspectParams.getStatus() == null) {
                    throw new ValidationException("巡检内容不允许为空，不允许入库！");
                }

                inspectResult = inspectResult && inspectParams.getStatus();
                continue;
            }

            // 模拟量
            if (schemeDetail.getType() == ParameterTypeDef.ANALOG_QUANTITY) {
                schemeDetail.setValue(inspectParams.getValue());
                if (inspectParams.getValue() == null) {
                    throw new ValidationException("巡检内容不允许为空，不允许入库！");
                }

                inspectResult = inspectResult && (inspectParams.getValue() <= schemeDetail.getMax() && inspectParams.getValue() >= schemeDetail.getMin());
                continue;
            }
            //文本量--不判断异常
            schemeDetail.setTextValue(inspectParams.getTextValue());
        }

        return inspectResult;
    }

    @Override
    public void updateOverTimeStatus() {
        log.info("开始执行更新超时巡检工单状态任务！");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 循环查询数据，直到所有的工单都处理完毕
        LocalDateTime now = LocalDateTime.now();
        int batch = 0;
        List<Long> overTimeWoIds = new ArrayList<>();
        do {
            WorkOrderQueryVo workOrderQueryVo = getOverTimeWorkOrderQueryVo(now, batch++);
            List<InspectionWorkOrderDto> workOrderDtoList = workOrderDao
                    .queryWorkOrderByWorkStatusByPage(workOrderQueryVo, InspectionWorkOrderDto.class)
                    .getData();
            if (CollectionUtils.isEmpty(workOrderDtoList)) {
                break;
            }

            List<InspectionWorkOrderDto> updateWorkOrders = overTimeWorkOrders(now, workOrderDtoList);
            updateWorkOrders.forEach(it -> overTimeWoIds.add(it.getId()));
            log.info("执行第{}批更新超时巡检工单状态任务，本次更新工单{}个", batch, overTimeWoIds.size());
            log.debug("执行第{}批更新超时巡检工单状态任务，本次更新工单的id列表为：{}", batch, JsonTransferUtils.toJSONString(overTimeWoIds));
        } while (true);

        stopWatch.stop();
        log.info("完成执行更新超时巡检工单状态任务，更新超时工单状态{}个，耗时{}ms", overTimeWoIds.size(), stopWatch.getTotalTimeMillis());
    }

    /**
     * 保存过期工单
     *
     * @param cutOffTime       当前时间
     * @param workOrderDtoList 工单列表
     */
    private List<InspectionWorkOrderDto> overTimeWorkOrders(LocalDateTime cutOffTime, List<InspectionWorkOrderDto> workOrderDtoList) {
        if (CollectionUtils.isEmpty(workOrderDtoList)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> result = new ArrayList<>();
        List<InspectionWorkOrderDto> updateWorkOrders = new ArrayList<>();
        for (InspectionWorkOrderDto workOrder : workOrderDtoList) {
            if (workOrder.getExecuteTimePlan() == null) {
                continue;
            }
            long duration = Duration.between(workOrder.getExecuteTimePlan(), cutOffTime).toMillis();
            if (duration > workOrder.getTimeConsumePlan()) {
                Map<String, Object> map = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
                map.put(ColumnDef.ID, workOrder.getId());
                map.put(WorkOrderDef.WORKSHEET_STATUS, WorkSheetStatusDef.OVERTIME);
                map.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
                result.add(map);
                updateWorkOrders.add(workOrder);
            }
        }

        if (CollectionUtils.isNotEmpty(result)) {
            workOrderDao.saveModelEntityList(result);
        }

        return updateWorkOrders;
    }

    @NotNull
    private WorkOrderSimpleQueryDTO getOverTimeWorkOrderQueryVo(LocalDateTime cutOffTime, int batch) {
        Integer batchHandleCount = maintenanceConfig.getBatchHandleCount();
        WorkOrderSimpleQueryDTO workOrderQueryVo = new WorkOrderSimpleQueryDTO();
        workOrderQueryVo.setWorkOrderStatus(Collections.singletonList(WorkSheetStatusDef.TO_BE_SENT));
        workOrderQueryVo.setTaskType(WorkSheetTaskType.INSPECTION);
        workOrderQueryVo.setStartTime(TimeUtil.addDateTimeByCycle(cutOffTime, AggregationCycle.ONE_MONTH,
                -1 * maintenanceConfig.getInspect().getCheckOverTime().getHandleOverTimeWoPreMonth()));
        workOrderQueryVo.setEndTime(cutOffTime);
        workOrderQueryVo.setPage(new Page(batch * batchHandleCount, batchHandleCount));
        Order order = new Order(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.ASC, 1);
        workOrderQueryVo.setOrders(Collections.singletonList(order));
        workOrderQueryVo.setUserId(PecsNodeType.ROOT_USER_ID);
        return workOrderQueryVo;
    }

    @Override
    public void noticeOverTimeStatus() {
        log.info("开始执行即将超时巡检工单提醒任务！");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (noticeMinute < 0) {
            return;
        }

        // 循环查询数据，直到所有的工单都处理完毕
        LocalDateTime now = LocalDateTime.now();
        int batch = 0;
        List<Long> noticeWoIds = new ArrayList<>();
        do {
            WorkOrderSimpleQueryDTO workOrderQueryVo = getOverTimeWorkOrderQueryVo(now, batch++);
            List<String> selectFields = Arrays.asList(ColumnDef.ID, WorkOrderDef.EXECUTE_TIME_PLAN, WorkOrderDef.TIME_CONSUME_PLAN, WorkOrderDef.WORKSHEET_STATUS, WorkOrderDef.TEAM_ID);
            workOrderQueryVo.setSelectFields(selectFields);
            List<InspectionWorkOrderDto> workOrderDtoList = workOrderDao
                    .queryWorkOrderByWorkStatusPage(workOrderQueryVo, InspectionWorkOrderDto.class)
                    .getData();
            if (CollectionUtils.isEmpty(workOrderDtoList)) {
                break;
            }

            List<InspectionWorkOrderDto> noticeWorkOrderList = overTimeWoNotice(workOrderDtoList, now);
            noticeWorkOrderList.forEach(it -> noticeWoIds.add(it.getId()));
            log.debug("执行即将超时巡检工单提醒任务，本次推送工单信息为：{}", JsonTransferUtils.toJSONString(noticeWorkOrderList));
        } while (true);

        stopWatch.stop();
        log.info("完成执行即将超时巡检工单提醒任务，推送工单{}个，耗时{}ms", noticeWoIds.size(), stopWatch.getTotalTimeMillis());
    }

    @NotNull
    private List<InspectionWorkOrderDto> overTimeWoNotice(List<InspectionWorkOrderDto> workOrderDtoList, LocalDateTime now) {
        List<InspectionWorkOrderDto> noticeWorkOrderList = new ArrayList<>();
        for (InspectionWorkOrderDto workOrder : workOrderDtoList) {
            if (workOrder.getExecuteTimePlan() == null) {
                continue;
            }
            long duration = Duration.between(now, workOrder.getExecuteTimePlan()).toMillis();
            if (duration + workOrder.getTimeConsumePlan() < noticeMinute * TimeUtil.MINUTE) {
                noticeWorkOrderList.add(workOrder);
            }
        }

        List<Long> checkUserIds = new ArrayList<>();
        // 使用UserRestApi替代已废弃的EemCloudAuthService
        // 根据具体业务逻辑实现用户权限查询
        // ApiResultI18n<List<UserVo>> checkUserResult = userRestApi.queryUserByFuncName(OperationAuthDef.INSPECTOR_WORK_ORDER_TECHNICIAN_CHECK, maintenanceConfig.getRootTenantId());
        // if (checkUserResult.isSuccess() && CollectionUtils.isNotEmpty(checkUserResult.getData())) {
        //     checkUserIds.addAll(checkUserResult.getData().stream().map(UserVo::getId).collect(Collectors.toList()));
        // }

        Map<Long, String> groupMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        Map<Long, String> userMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        // 使用UserRestApi相关接口查询用户和组信息

        Map<Long, List<Long>> teamAndUserMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        if (CollectionUtils.isNotEmpty(noticeWorkOrderList)) {
            List<BaseVo> nodes = noticeWorkOrderList.stream().filter(it -> CollectionUtils.isNotEmpty(it.getDevicePlanRelationshipList()))
                    .flatMap(it -> it.getDevicePlanRelationshipList().stream())
                    .map(it -> new BaseVo(it.getDeviceId(), it.getDeviceLabel()))
                    .collect(Collectors.toList());
            Map<BaseVo, String> nodeNameMap = nodeDao.queryNodeNameMap(nodes);

            for (InspectionWorkOrderDto workOrderDto : noticeWorkOrderList) {
                noticePreOverTimeWorkOrder(checkUserIds, groupMap, teamAndUserMap, nodeNameMap, workOrderDto);
            }
        }
        return noticeWorkOrderList;
    }

    /**
     * 工单超时预警
     *
     * @param checkUserIds
     * @param groupMap
     * @param teamAndUserMap
     * @param nodeNameMap
     * @param workOrderDto
     */
    private void noticePreOverTimeWorkOrder(List<Long> checkUserIds,
                                            Map<Long, String> groupMap, Map<Long, List<Long>> teamAndUserMap,
                                            Map<BaseVo, String> nodeNameMap,
                                            InspectionWorkOrderDto workOrderDto) {
        Boolean hasKey = redisTemplate.hasKey(EEM_INSPECT_WORK_ORDER_CHECK_OVER_TIME + workOrderDto.getId());
        if (BooleanUtils.isTrue(hasKey)) {
            return;
        }

        List<Long> userIds = teamAndUserMap.get(workOrderDto.getTeamId());
        if (userIds == null) {
            userIds = new ArrayList<>();
            ApiResultI18n<List<UserVo>> teamUserResult = inspectorService.queryMaintenanceUser(new QueryInspectorRequest(workOrderDto.getTeamId()));
            ParamUtils.checkResultGeneric(teamUserResult);
            if (CollectionUtils.isNotEmpty(teamUserResult.getData())) {
                userIds.addAll(teamUserResult.getData().stream().map(UserVo::getId).collect(Collectors.toList()));
            }
            userIds.addAll(checkUserIds);
            userIds.add(LoginDef.USER_ROOT);
            teamAndUserMap.put(workOrderDto.getTeamId(), userIds);
        }

        List<DevicePlanRelationship> devicePlanRelationshipList = workOrderDto.getDevicePlanRelationshipList();
        String nodeName = StringFormatUtils.BLANK_STR;
        if (CollectionUtils.isNotEmpty(devicePlanRelationshipList)) {
            DevicePlanRelationship obj = devicePlanRelationshipList.get(0);
            nodeName = nodeNameMap.get(new BaseVo(obj.getDeviceId(), obj.getDeviceLabel()));
        }

        String desc = String.format("[%s]的巡检对象[%s]，计划于%s巡检的工单即将超过巡检时间，请及时巡检！",
                groupMap.get(workOrderDto.getTeamId()),
                nodeName,
                TimeUtil.format(workOrderDto.getExecuteTimePlan(), TimeUtil.LONG_TIME_FORMAT));
        boolean flag = webNotification.pushToWeb(null, EnumSystemEventType.INSPECT_WORK_ORDER_OVER_TIME.getId(), desc, userIds, null, MessageTypeDef.SYSTEM_EVENT, GlobalInfoUtils.getTenantId(), null);
        if (flag) {
            redisTemplate.opsForValue().set(EEM_INSPECT_WORK_ORDER_CHECK_OVER_TIME + workOrderDto.getId(), String.valueOf(workOrderDto.getId()), 1, TimeUnit.DAYS);
        }
    }

    @Override
    public void exportWorkOrder(InspectionSearchDto dto, HttpServletResponse response) {
        String fileName = "巡检工单";
        dto.setPage(new Page(0, exportMaxCount));

        List<String> fields = Arrays.asList(
                WorkOrderDef.CODE,
                ColumnDef.ID,
                WorkOrderDef.EXECUTE_TIME_PLAN,
                WorkOrderDef.TIME_CONSUME_PLAN,
                WorkOrderDef.INSPECTION_SCHEME_ID,
                WorkOrderDef.TEAM_ID,
                WorkOrderDef.BUSSINESS_STATUS,
                WorkOrderDef.WORKSHEET_STATUS,
                WorkOrderDef.MAINTENANCE_CONTENT,
                WorkOrderDef.SIGN_POINT_ID);
        dto.setSelectFields(fields);

        ApiResult<List<InspectionWorkOrderDto>> listResultWithTotal = queryWorkOrderList(dto);
        List<InspectionWorkOrderDto> inspectionWorkOrderDtos = listResultWithTotal.getData();
        Integer workSheetStatus = dto.getWorkSheetStatus();

        try (Workbook workBook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA)) {
            List<Integer> colWidth = Arrays.asList(25, 18, 18, 18, 30, 25, 25);

            PoiExcelUtils.createSheet(workBook, fileName, (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;
                if (workSheetStatus.equals(WorkSheetStatusDef.TO_BE_SENT)) {
                    writeHeaderWait(sheet, baseCellStyle, rowNum++);
                    writeDataWait(sheet, baseCellStyle, rowNum, inspectionWorkOrderDtos);
                } else if (workSheetStatus.equals(WorkSheetStatusDef.ACCOMPLISHED)) {
                    writeHeaderFinish(sheet, baseCellStyle, rowNum++);
                    writeDataFinish(sheet, baseCellStyle, rowNum, inspectionWorkOrderDtos);
                } else if (workSheetStatus.equals(WorkSheetStatusDef.OVERTIME)) {
                    writeHeaderOverTime(sheet, baseCellStyle, rowNum++);
                    writeDataOverTime(sheet, baseCellStyle, rowNum, inspectionWorkOrderDtos);
                } else if (workSheetStatus.equals(WorkSheetStatusDef.ALL)) {
                    writeHeaderAll(sheet, baseCellStyle, rowNum++);
                    writeDataAll(sheet, baseCellStyle, rowNum, inspectionWorkOrderDtos);
                } else {
                    writeHeader(sheet, baseCellStyle, rowNum++);
                    writeData(sheet, baseCellStyle, rowNum, inspectionWorkOrderDtos);
                }
            }, colWidth);
            FileUtils.downloadExcel(response, workBook, "巡检工单" + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT), ContentTypeDef.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            ErrorUtils.exportError("巡检工单", e);
        }
    }

    /**
     * 待巡检的工单
     *
     * @param sheet
     * @param baseCellStyle
     * @param startRow
     */
    private void writeHeaderWait(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("工单号", baseCellStyle);
        headerMap.put("开始时间", baseCellStyle);
        headerMap.put("耗时", baseCellStyle);
        headerMap.put("巡检方案", baseCellStyle);
        headerMap.put("巡检对象", baseCellStyle);
        headerMap.put("责任班组", baseCellStyle);
        headerMap.put("签到点", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    /**
     * 待巡检的工单
     *
     * @param sheet
     * @param baseCellStyle
     * @param rowNum
     * @param inspectionWorkOrderDtos
     */
    private void writeDataWait(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<InspectionWorkOrderDto> inspectionWorkOrderDtos) {
        int col;
        if (CollectionUtils.isEmpty(inspectionWorkOrderDtos)) {
            return;
        }
        for (InspectionWorkOrderDto item : inspectionWorkOrderDtos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getCode());

            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getExecuteTimePlan(), TimeUtil.LONG_TIME_FORMAT));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (item.getTimeConsumePlan() / TimeUtil.HOUR) + "h");
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getInspectionSchemeName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getDevicePlanRelationshipList().get(0).getDeviceName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getTeamName());
            PoiExcelUtils.createCell(row, col, baseCellStyle, item.getSignPointName());
            rowNum++;
        }
    }

    /**
     * 已完成的工单
     *
     * @param sheet
     * @param baseCellStyle
     * @param startRow
     */
    private void writeHeaderFinish(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("工单号", baseCellStyle);
        headerMap.put("开始时间", baseCellStyle);
        headerMap.put("耗时", baseCellStyle);
        headerMap.put("巡检方案", baseCellStyle);
        headerMap.put("巡检对象", baseCellStyle);
        headerMap.put("责任班组", baseCellStyle);
        headerMap.put("签到点", baseCellStyle);
        headerMap.put("异常原因", baseCellStyle);
        headerMap.put("执行人", baseCellStyle);
        headerMap.put("巡检结果", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    /**
     * 全部工单
     *
     * @param sheet
     * @param baseCellStyle
     * @param startRow
     */
    private void writeHeaderAll(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("工单号", baseCellStyle);
        headerMap.put("开始时间", baseCellStyle);
        headerMap.put("耗时", baseCellStyle);
        headerMap.put("巡检方案", baseCellStyle);
        headerMap.put("巡检对象", baseCellStyle);
        headerMap.put("责任班组", baseCellStyle);
        headerMap.put("签到点", baseCellStyle);
        headerMap.put("工单状态", baseCellStyle);
        headerMap.put("异常原因", baseCellStyle);
        headerMap.put("执行人", baseCellStyle);
        headerMap.put("巡检结果", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    /**
     * 已完成的工单
     *
     * @param sheet
     * @param baseCellStyle
     * @param rowNum
     * @param inspectionWorkOrderDtos
     */
    private void writeDataFinish(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<InspectionWorkOrderDto> inspectionWorkOrderDtos) {
        int col;
        if (CollectionUtils.isEmpty(inspectionWorkOrderDtos)) {
            return;
        }
        for (InspectionWorkOrderDto item : inspectionWorkOrderDtos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getCode());

            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getExecuteTimePlan(), TimeUtil.LONG_TIME_FORMAT));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (item.getTimeConsumePlan() / TimeUtil.HOUR) + "h");
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getInspectionSchemeName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getDevicePlanRelationshipList().get(0).getDeviceName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getTeamName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getSignPointName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, getAbnormalReason(item.getAbnormalReasonList()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, Objects.isNull(handleStaffName(item)) ? StringFormatUtils.BLANK_STR : handleStaffName(item));
            PoiExcelUtils.createCell(row, col, baseCellStyle, Objects.isNull(item.getInspectResult()) ? StringFormatUtils.BLANK_STR : item.getInspectResult());
            rowNum++;
        }
    }

    private String handleStaffName(InspectionWorkOrderDto dto) {
        MaintenanceContent maintenanceContent = JsonTransferUtils.parseObject(dto.getMaintenanceContent(), MaintenanceContent.class);
        assert maintenanceContent != null;
        List<OperationUser> users = maintenanceContent.getUsers();
        if (CollectionUtils.isEmpty(users)) {
            return null;
        }
        List<String> collect = users.stream().map(OperationUser::getUserName).collect(Collectors.toList());
        return StringUtils.join(collect, ",");
    }

    /**
     * 获取异常原因字符串
     *
     * @param reasonList
     * @return
     */
    private String getAbnormalReason(List<WorksheetAbnormalReasonDto> reasonList) {
        if (CollectionUtils.isEmpty(reasonList)) {
            return StringFormatUtils.BLANK_STR;
        }

        List<String> nameList = reasonList.stream().map(WorksheetAbnormalReasonDto::getTypeName).collect(Collectors.toList());
        return ArrayUtils.join(nameList, ",");
    }

    /**
     * 全部
     *
     * @param sheet
     * @param baseCellStyle
     * @param rowNum
     * @param inspectionWorkOrderDtos
     */
    private void writeDataAll(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<InspectionWorkOrderDto> inspectionWorkOrderDtos) {
        int col;
        if (CollectionUtils.isEmpty(inspectionWorkOrderDtos)) {
            return;
        }
        for (InspectionWorkOrderDto item : inspectionWorkOrderDtos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getCode());

            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getExecuteTimePlan(), TimeUtil.LONG_TIME_FORMAT));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (item.getTimeConsumePlan() / TimeUtil.HOUR) + "h");
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getInspectionSchemeName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getDevicePlanRelationshipList().get(0).getDeviceName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getTeamName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getSignPointName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getWorkSheetStatusName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, getAbnormalReason(item.getAbnormalReasonList()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, handleStaffName(item) == null ? StringFormatUtils.BLANK_STR : handleStaffName(item));
            PoiExcelUtils.createCell(row, col, baseCellStyle, item.getInspectResult() == null ? StringFormatUtils.BLANK_STR : item.getInspectResult());
            rowNum++;
        }
    }

    /**
     * 待审核、被退回
     *
     * @param sheet
     * @param baseCellStyle
     * @param startRow
     */
    private void writeHeader(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("工单号", baseCellStyle);
        headerMap.put("开始时间", baseCellStyle);
        headerMap.put("耗时", baseCellStyle);
        headerMap.put("巡检方案", baseCellStyle);
        headerMap.put("巡检对象", baseCellStyle);
        headerMap.put("责任班组", baseCellStyle);
        headerMap.put("签到点", baseCellStyle);
        headerMap.put("异常原因", baseCellStyle);
        headerMap.put("执行人", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    /**
     * 待审核、被退回
     *
     * @param sheet
     * @param baseCellStyle
     * @param rowNum
     * @param inspectionWorkOrderDtos
     */
    private void writeData(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<InspectionWorkOrderDto> inspectionWorkOrderDtos) {
        int col;
        if (CollectionUtils.isEmpty(inspectionWorkOrderDtos)) {
            return;
        }
        for (InspectionWorkOrderDto item : inspectionWorkOrderDtos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getCode());

            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getExecuteTimePlan(), TimeUtil.LONG_TIME_FORMAT));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (item.getTimeConsumePlan() / TimeUtil.HOUR) + "h");
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getInspectionSchemeName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getDevicePlanRelationshipList().get(0).getDeviceName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getTeamName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getSignPointName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, getAbnormalReason(item.getAbnormalReasonList()));
            PoiExcelUtils.createCell(row, col, baseCellStyle, handleStaffName(item) == null ? StringFormatUtils.BLANK_STR : handleStaffName(item));
            rowNum++;
        }
    }

    /**
     * 超时
     *
     * @param sheet
     * @param baseCellStyle
     * @param startRow
     */
    private void writeHeaderOverTime(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("工单号", baseCellStyle);
        headerMap.put("开始时间", baseCellStyle);
        headerMap.put("耗时", baseCellStyle);
        headerMap.put("巡检方案", baseCellStyle);
        headerMap.put("巡检对象", baseCellStyle);
        headerMap.put("责任班组", baseCellStyle);
        headerMap.put("签到点", baseCellStyle);
        headerMap.put("执行人", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    /**
     * 超时
     *
     * @param sheet
     * @param baseCellStyle
     * @param rowNum
     * @param inspectionWorkOrderDtos
     */
    private void writeDataOverTime(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<InspectionWorkOrderDto> inspectionWorkOrderDtos) {
        int col;
        if (CollectionUtils.isEmpty(inspectionWorkOrderDtos)) {
            return;
        }
        for (InspectionWorkOrderDto item : inspectionWorkOrderDtos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getCode());

            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getExecuteTimePlan(), TimeUtil.LONG_TIME_FORMAT));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (item.getTimeConsumePlan() / TimeUtil.HOUR) + "h");
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getInspectionSchemeName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getDevicePlanRelationshipList().get(0).getDeviceName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getTeamName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getSignPointName());
            PoiExcelUtils.createCell(row, col, baseCellStyle, handleStaffName(item) == null ? StringFormatUtils.BLANK_STR : handleStaffName(item));
            rowNum++;
        }
    }

    @Override
    public void updateSignPointStatus() {
        log.info("开始执行更新签到点状态任务...");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        LocalDateTime now = LocalDateTime.now();
        List<InspectionWorkOrderDto> workOrderDtoList = workOrderDao.queryWorkOrderByWorkStatus(Collections.singletonList(WorkSheetStatusDef.TO_BE_SENT),
                WorkSheetTaskType.INSPECTION, now, TimeUtil.getFirstTimeOfNextDay(now), InspectionWorkOrderDto.class);
        if (CollectionUtils.isEmpty(workOrderDtoList)) {
            return;
        }

        workOrderDtoList = workOrderDtoList.stream().filter(it -> it.getSignPointId() != null && it.getSignGroupId() != null).collect(Collectors.toList());
        Map<Long, List<Long>> recordGroupMap = new HashMap<>(workOrderDtoList.size());
        for (InspectionWorkOrderDto workOrderDto : workOrderDtoList) {
            List<Long> longs = recordGroupMap.computeIfAbsent(workOrderDto.getSignGroupId(), k -> new ArrayList<>());
            longs.add(workOrderDto.getSignPointId());
        }
        List<SignGroupStatusGroup> groups = new ArrayList<>();
        recordGroupMap.forEach((key, val) -> {
            SignGroupStatusGroup group = new SignGroupStatusGroup();
            group.setSignGroupId(key);
            group.setSignPointIds(val);
        });
        List<SignInStatusRecord> signInStatusRecords = signInStatusRecordDao.queryRecord(groups);
        Map<String, List<InspectionWorkOrderDto>> workOrderGroup = workOrderDtoList.stream().collect(Collectors.groupingBy(it -> String.format("%s_%s", it.getSignGroupId(), it.getSignPointId())));
        List<SignInStatusRecord> result = new ArrayList<>();
        workOrderGroup.forEach((key, val) -> {
            Optional<InspectionWorkOrderDto> min = val.stream().min((v1, v2) -> {
                if (v1.getExecuteTimePlan().isBefore(v2.getExecuteTimePlan())) {
                    return 1;
                } else if (v1.getExecuteTimePlan().isAfter(v2.getExecuteTimePlan())) {
                    return -1;
                } else {
                    return 0;
                }
            });
            if (!min.isPresent()) {
                return;
            }

            InspectionWorkOrderDto workOrder = min.get();
            SignInStatusRecord signInStatusRecord = signInStatusRecords.stream()
                    .filter(it -> Objects.equals(workOrder.getSignGroupId(), it.getSignInGroupId()) && Objects.equals(workOrder.getSignPointId(), it.getSignInPointId())).findAny().orElse(null);
            if (signInStatusRecord != null && signInStatusRecord.getUpdateTime().isBefore(workOrder.getExecuteTimePlan())) {
                signInStatusRecord.setUpdateTime(LocalDateTime.now());
                signInStatusRecord.setStatus(SignInStatusDef.UNSIGNED);
                result.add(signInStatusRecord);
            }
        });

        modelServiceUtils.writeData(result);
        stopWatch.stop();
        log.info("完成执行更新超时工单状态任务，本次更新信息为：{}，耗时{}ms", JsonTransferUtils.toJSONString(result), stopWatch.getTotalTimeMillis());
    }
}







