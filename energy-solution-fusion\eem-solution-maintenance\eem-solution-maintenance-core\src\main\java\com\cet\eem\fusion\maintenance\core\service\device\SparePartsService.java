package com.cet.eem.fusion.maintenance.core.service.device;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DeviceSystem;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SpareParts;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SparePartsReplaceRecordVo;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.*;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.dto.QueryReplaceRecordDto;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.dto.QuerySparePartsDto;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.vo.DeviceSystemVoWithSubLayer;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.vo.SparePartsCountVo;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.vo.SparePartsDeviceVo;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface SparePartsService {
    /**
     * 查询设备下的备件信息
     *
     * @param querySparePartsDto
     * @return
     */
    List<SpareParts> querySparePartsByDevice(QuerySparePartsDto querySparePartsDto);

    /**
     * 根据项目信息(projectId)查询其下的系统和设备备件信息
     *
     * @return
     */
    List<DeviceSystemVoWithSubLayer> querySpareDevice();

    /**
     * 编辑备件
     *
     * @param editSpareParts
     * @return
     */
    SpareParts editSpareParts(EditSpareParts editSpareParts);

    void deleteSpareParts(Long id, Long deviceId);

    /**
     * 新增设备
     *
     * @param addSparePartsDevice
     * @return
     */
    SparePartsDeviceVo createDevice(AddSparePartsDevice addSparePartsDevice);

    /**
     * 新增系统
     *
     * @param addDeviceSystem
     * @return
     */
    DeviceSystem addDeviceSystem(AddDeviceSystem addDeviceSystem);

    /**
     * 新增备件
     *
     * @param addSpareParts
     * @return
     */
    Map<String, Object> createSpareparts(AddSpareParts addSpareParts);

    /**
     * 编辑系统
     *
     * @param editDeviceSystem
     * @return
     */
    DeviceSystem editDeviceSystem(EditDeviceSystem editDeviceSystem);

    void deleteDeviceSystem(Long id);

    /**
     * 编辑设备
     *
     * @param editDevice
     * @return
     */
    SparePartsDeviceVo editDevice(EditDevice editDevice);

    void deleteDevice(Long id, Long systemId);

    /**
     * 根据备件信息统计备件消耗数据
     *
     * @param queryReplaceRecordDto
     * @return
     */
    ApiResult<List<SparePartsReplaceRecordVo>> queryReplaceRecordBySparePart(QueryReplaceRecordDto queryReplaceRecordDto);

    void importDeviceComponentToSpareParts(List<ModelList> modelLists);

    /**
     * 批量删除系统
     *
     * @param ids
     */
    void deleteSystemList(List<Long> ids);

    /**
     * 批量删除设备
     *
     * @param ids
     * @param systemId
     */
    void deleteDeviceList(List<Long> ids, Long systemId);

    /**
     * 批量删除备件
     *
     * @param ids
     * @param deviceId
     */
    String deleteSparePartsList(List<Long> ids, Long deviceId);

    /**
     * 根据设备信息统计备件消耗信息
     *
     * @param queryReplaceRecordDto
     * @return
     */
    ApiResult<List<SparePartsCountVo>> queryReplaceByDevice(QueryReplaceRecordDto queryReplaceRecordDto);


    /**
     * 根据具体设备查询备件更换记录
     *
     * @param baseVo
     * @return
     */
    List<SparePartsReplaceRecordVo> querySparePartsByDevice(BaseVo baseVo);

    /**
     * 根据工单id查询备件更换记录
     *
     * @param wordOrderIds 工单id
     * @return 备件更换记录
     */
    List<SparePartsReplaceRecordVo> querySparePartsReplaceRecord(@NotNull Collection<Long> wordOrderIds);

    /**
     * 备件统计-按备件统计导出
     *
     * @param queryReplaceRecordDto
     */
    void exportSparePartsReplaceRecord(HttpServletResponse response, QueryReplaceRecordDto queryReplaceRecordDto);

    /**
     * 备件统计-按设备统计导出
     *
     * @param queryReplaceRecordDto
     */
    void exportSparePartsReplaceRecordByDevice(HttpServletResponse response, QueryReplaceRecordDto queryReplaceRecordDto);
    List<SpareParts> querySparePartsStorageByDevice(BaseVo baseVo);
}


