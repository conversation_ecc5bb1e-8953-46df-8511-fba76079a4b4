package com.cet.eem.fusion.maintenance.core.schedule.job;

import com.cet.eem.fusion.maintenance.core.bll.common.task.TaskSchedule;
import com.cet.eem.fusion.maintenance.core.service.maintenance.MaintenanceWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @ClassName : GenerateMaintenanceWorkOrderByDeviceSchedule
 * @Description : 根据设备运行时长生成维保工单
 * <AUTHOR> jiangzixuan
 * @Date: 2021-09-01 10:11
 */
@Component
@Slf4j
public class GenerateMaintenanceWorkOrderByDeviceSchedule implements TaskSchedule {


    @Autowired
    MaintenanceWorkOrderService maintenanceWorkOrderService;

    /**
     * 判断时间分析是否在运行
     */
    private boolean isPecCoreRunning = false;


    @Scheduled(cron = "${cet.eem.work-order.maintenance.generate-work-order.interval}")
    @Override
    public void execute() {
        if (isPecCoreRunning) {
            return;
        }

        isPecCoreRunning = true;
        try {
            maintenanceWorkOrderService.createWorkOrderByDeviceWorkTime();
        } catch (Exception ex) {
            log.error("根据设备运行时长生成维保工单任务发生异常:", ex);
        }

        isPecCoreRunning = false;
    }
}
