package com.cet.eem.fusion.maintenance.core.model.workorder;

import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/31
 */
@Getter
@Setter
public class WorkOrderSearchVo {
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private BaseVo node;
    private Integer cycle;

    private List<Integer> taskTypes;

    /**
     * 项目id
     */
    private Long projectId;

    @ApiModelProperty("分页")
    private Page page;

    @ApiModelProperty("班组")
    private Long teamId;

    @ApiModelProperty("是否为巡检用户")
    private boolean isInspectUser;
}


