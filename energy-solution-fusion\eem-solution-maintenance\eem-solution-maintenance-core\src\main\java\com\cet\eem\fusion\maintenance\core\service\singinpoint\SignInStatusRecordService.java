package com.cet.eem.fusion.maintenance.core.service.singinpoint;

import com.cet.eem.fusion.maintenance.core.model.sign.SignGroupStatusGroup;
import com.cet.eem.fusion.maintenance.core.model.workorder.SignInPointStatusCount;
import org.springframework.util.LinkedMultiValueMap;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/17
 */
public interface SignInStatusRecordService {
    /**
     * 重置签到点状态
     *
     * @param status
     */
    void resetSignInStatus(int status);

    /**
     * 更新签到点状态
     *
     * @param signPointId
     * @param signGroupId
     * @param status
     */
    void updateSignInStatus(Long signPointId, Long signGroupId, int status);

    /**
     * 更新签到点状态
     *
     * @param groups
     */
    void updateSignInStatus(List<SignGroupStatusGroup> groups);

    void createSignInStatus(Long signPointId, Collection<Long> singGroupIds);

    void createSignInStatusBatch(Map<Long , List<Long>> signGroupIdMap);

    void deleteSignInStatusRecord(Long signPointId, Collection<Long> singGroupIds);

    /**
     * 签到点
     *
     * @param signInGroupId 签到点分组id
     */
    SignInPointStatusCount querySignInPointStatusCount(Long signInGroupId);
}

