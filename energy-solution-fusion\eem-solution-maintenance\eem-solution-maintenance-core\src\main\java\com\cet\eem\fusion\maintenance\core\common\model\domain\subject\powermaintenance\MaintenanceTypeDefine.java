package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/7/13
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.MAINTENANCE_TYPE_DEFINE)
public class MaintenanceTypeDefine extends EntityWithName {
    @ApiModelProperty("项目id")
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;

    public MaintenanceTypeDefine() {
        this.modelLabel = ModelLabelDef.MAINTENANCE_TYPE_DEFINE;
    }

    public MaintenanceTypeDefine(Long projectId, String name) {
        this();
        this.projectId = projectId;
        this.name = name;
    }
}
