package com.cet.eem.fusion.maintenance.core.service.inspection.impl;

import com.cet.eem.bll.common.dao.project.ProjectDao;
import com.cet.eem.bll.common.model.domain.object.organization.Project;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInGroup;
import com.cet.eem.fusion.maintenance.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.maintenance.core.entity.bo.PlanSheetWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.InspectionSchemeDao;
import com.cet.eem.fusion.maintenance.core.dao.PlanSheetDao;
import com.cet.eem.fusion.maintenance.core.dao.SignInGroupDao;
import com.cet.eem.fusion.maintenance.core.model.plan.*;
import com.cet.eem.fusion.maintenance.core.schedule.domain.PlanSheetDomain;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionPlanService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorService;
import com.cet.eem.fusion.maintenance.core.utils.InspectorUserCheckUtils;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;
import com.cet.electric.commons.ApiResult;

import com.cet.eem.fusion.maintenance.core.common.model.auth.user.UserGroupVo;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : InspectionPlanServiceImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-20 16:31
 */
@Service
@Slf4j
public class InspectionPlanServiceImpl implements InspectionPlanService {

    @Autowired
    private PlanSheetDao planSheetDao;

    @Autowired
    private ProjectDao projectDao;

    @Autowired
    private UserRestApi userService;

    @Autowired
    private SignInGroupDao signInGroupDao;

    @Autowired
    private InspectionSchemeDao inspectionSchemeDao;

    @Autowired
    private InspectorService inspectorService;

    @Autowired
    private PlanSheetDomain planSheetDomain;
    @Resource
UserRestApi userRestApi;
    @Autowired
    InspectorUserCheckUtils inspectorUserCheckUtils;
    @Override
    public PlanSheet addInspectionPlan(AddInspectionPlanRequest addInspectionPlanRequest) throws SchedulerException {
        checkPlanNameIsRepeatWhileCreate(addInspectionPlanRequest.getName());
        PlanSheetWithSubLayer planSheetWithSubLayer = new PlanSheetWithSubLayer();
        BeanUtils.copyProperties(addInspectionPlanRequest, planSheetWithSubLayer);
        //设置当前用户信息
        setUserInfo(planSheetWithSubLayer);
        //设置当前项目信息
        setProjectInfo(planSheetWithSubLayer);
        //设置类型信息
        planSheetWithSubLayer.setWorkSheetType(WorkSheetTaskType.INSPECTION);
        planSheetWithSubLayer.setCreateTime(System.currentTimeMillis());
        planSheetWithSubLayer.setEnabled(true);
        planSheetWithSubLayer.setDeleted(false);
        planSheetDao.insert(planSheetWithSubLayer);
        try {
            planSheetDomain.startInspectionPlan(planSheetWithSubLayer);
        } catch (Exception e) {
            //模型服务不支持事务功能，只能异常时删除，当作回滚操作，不能完全避免脏数据
            deletePlan(Collections.singletonList(planSheetWithSubLayer.getId()));
            throw e;
        }
        return planSheetWithSubLayer;
    }

    private void checkPlanNameIsRepeatWhileCreate(String planName) {
        LambdaQueryWrapper<PlanSheet> queryWrapper = LambdaQueryWrapper.of(PlanSheet.class);
        queryWrapper.eq(PlanSheet::getName, planName)
                .eq(PlanSheet::getWorkSheetType, WorkSheetTaskType.INSPECTION)
                .eq(PlanSheet::getProjectId, GlobalInfoUtils.getTenantId());
        List<PlanSheet> planSheetList = planSheetDao.selectList(queryWrapper);
        checkNameRepeat(planSheetList);
    }

    private void checkNameRepeat(List<PlanSheet> planSheetList) {
        if (CollectionUtils.isEmpty(planSheetList)) {
            return;
        }
        planSheetList = planSheetList.stream().filter(it -> !BooleanUtils.isTrue(it.getDeleted())).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(planSheetList), "巡检计划名称重复");
    }

    private void checkPlanNameIsRepeatWhileEdit(Long id, String planName) {
        LambdaQueryWrapper<PlanSheet> queryWrapper = LambdaQueryWrapper.of(PlanSheet.class);
        queryWrapper.eq(PlanSheet::getName, planName);
        queryWrapper.eq(PlanSheet::getProjectId, GlobalInfoUtils.getTenantId());
        queryWrapper.eq(PlanSheet::getWorkSheetType, WorkSheetTaskType.INSPECTION);
        queryWrapper.ne(PlanSheet::getId, id);
        List<PlanSheet> planSheetList = planSheetDao.selectList(queryWrapper);
        checkNameRepeat(planSheetList);
    }

    @Override
    public PlanSheet editInspectionPlan(EditInspectionPlanRequest editInspectionPlanRequest) throws SchedulerException {
        checkPlanNameIsRepeatWhileEdit(editInspectionPlanRequest.getId(), editInspectionPlanRequest.getName());
        PlanSheet planSheet = planSheetDao.selectById(editInspectionPlanRequest.getId());
        Assert.notNull(planSheet, "巡检工单不存在！");
        BeanUtils.copyProperties(editInspectionPlanRequest, planSheet);
        planSheetDomain.reschedulePlan(planSheet);
        planSheetDao.updateById(planSheet);
        updateInspectionTarget(editInspectionPlanRequest);
        return planSheetDao.selectRelatedById(PlanSheetWithSubLayer.class, editInspectionPlanRequest.getId());
    }

    @Override
    public ApiResult<List<InspectionPlanSheetVo>> queryInspectionPlan(QueryInspectionPlanRequest queryInspectionPlanRequest) {
        // 查询用户信息和班组信息
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());

        // 判断当前用户是否为巡检用户，对于巡检用户只能看自己班组的工单
        queryInspectionPlanRequest.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(queryInspectionPlanRequest.getTeamId(), user));
        ApiResult<List<PlanSheetWithSubLayer>> listResultWithTotal = planSheetDao.queryInspectionPlanSheetSubLayerWithPage(queryInspectionPlanRequest);
        List<InspectionPlanSheetVo> planSheetVoList = transferToVo(listResultWithTotal.getData());
        if (CollectionUtils.isNotEmpty(planSheetVoList)) {
            setSignInGroupName(planSheetVoList);
            setNextExecuteTime(planSheetVoList);
            setTeamName(planSheetVoList, queryInspectionPlanRequest.getTenantId());
            setSchemeName(planSheetVoList);
        }
        return Result.ok(planSheetVoList, listResultWithTotal.getTotal());
    }

    @Override
    public void deletePlan(Collection<Long> planIds) throws SchedulerException {
        LambdaQueryWrapper<PlanSheet> wrapper = LambdaQueryWrapper.of(PlanSheet.class).ne(PlanSheet::getUsed, true).in(PlanSheet::getId, planIds);
        List<PlanSheet> planSheets = planSheetDao.selectList(wrapper);
        List<Long> collect = planSheets.stream().map(PlanSheet::getId).collect(Collectors.toList());
        String join;
        if (CollectionUtils.isEmpty(collect)) {
            List<String> nameList = planSheetDao.selectBatchIds(planIds).stream().map(PlanSheet::getName).collect(Collectors.toList());
            join = StringUtils.join(nameList, ",");
            throw new ValidationException("名称为[" + join + "]的计划已经创建工单，不允许删除！");
        }
        planSheetDomain.deletePlan(collect);
        planSheetDao.deletePlanSheets(collect);
        planIds.removeAll(collect);
        if (CollectionUtils.isNotEmpty(planIds)) {
            List<String> nameList = planSheetDao.selectBatchIds(planIds).stream().map(PlanSheet::getName).collect(Collectors.toList());
            join = StringUtils.join(nameList, ",");
            throw new ValidationException("名称为[" + join + "]的计划已经创建工单，不允许删除！");
        }

    }

    @Override
    public void disablePlanSheet(Collection<Long> ids) throws SchedulerException {
        LambdaUpdateWrapper<PlanSheet> updateWrapper = LambdaUpdateWrapper.of(PlanSheet.class);
        updateWrapper.set(PlanSheet::getEnabled, false);
        LambdaQueryWrapper<PlanSheet> queryWrapper = LambdaQueryWrapper.of(PlanSheet.class);
        queryWrapper.in(PlanSheet::getId, ids);
        queryWrapper.eq(PlanSheet::getEnabled, true);
        planSheetDomain.pausePlan(ids);
        planSheetDao.update(updateWrapper, queryWrapper);
    }

    @Override
    public void enablePlanSheet(Collection<Long> ids) throws SchedulerException {
        LambdaQueryWrapper<PlanSheet> queryWrapper = LambdaQueryWrapper.of(PlanSheet.class);
        queryWrapper.in(PlanSheet::getId, ids);
        queryWrapper.eq(PlanSheet::getEnabled, false);
        List<PlanSheet> planSheetList = planSheetDao.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(planSheetList)) {
            return;
        }
        checkPlanSheetEndTimeIsValid(planSheetList);
        planSheetList.forEach(s -> s.setEnabled(true));
        planSheetDao.saveOrUpdateBatch(planSheetList);

        List<Long> idsAfterFilter = planSheetList.stream().map(PlanSheet::getId).collect(Collectors.toList());
        planSheetDomain.resumePlan(idsAfterFilter);
    }

    private void updateInspectionTarget(EditInspectionPlanRequest editInspectionPlanRequest) {
        PlanSheetWithSubLayer planSheetWithSubLayer = planSheetDao.selectRelatedById(PlanSheetWithSubLayer.class, editInspectionPlanRequest.getId());
        doSaveInspectionTarget(editInspectionPlanRequest, planSheetWithSubLayer);
        doDeleteInspectionTarget(editInspectionPlanRequest, planSheetWithSubLayer);
    }

    private void doSaveInspectionTarget(EditInspectionPlanRequest editInspectionPlanRequest, PlanSheetWithSubLayer planSheetWithSubLayer) {
        List<DevicePlanRelationship> newDeviceList = editInspectionPlanRequest.getDeviceList();
        List<DevicePlanRelationship> oldDeviceList = planSheetWithSubLayer.getDevicePlanRelationshipList();
        if (CollectionUtils.isEmpty(newDeviceList)) {
            return;
        }

        List<DevicePlanRelationship> devicePlanRelationshipList = newDeviceList.stream().filter(s -> {
            if (CollectionUtils.isEmpty(oldDeviceList)) {
                return true;
            }
            for (DevicePlanRelationship devicePlanRelationship : oldDeviceList) {
                if (s.contentEquals(devicePlanRelationship)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(devicePlanRelationshipList)) {
            planSheetDao.insertChild(editInspectionPlanRequest.getId(), devicePlanRelationshipList);
        }
    }

    private void doDeleteInspectionTarget(EditInspectionPlanRequest editInspectionPlanRequest, PlanSheetWithSubLayer planSheetWithSubLayer) {
        List<DevicePlanRelationship> newDeviceList = editInspectionPlanRequest.getDeviceList();
        List<DevicePlanRelationship> oldDeviceList = planSheetWithSubLayer.getDevicePlanRelationshipList();
        if (CollectionUtils.isEmpty(oldDeviceList)) {
            return;
        }
        List<DevicePlanRelationship> devicePlanRelationshipList = oldDeviceList.stream().filter(s -> {
            if (CollectionUtils.isEmpty(newDeviceList)) {
                return true;
            }
            for (DevicePlanRelationship devicePlanRelationship : newDeviceList) {
                if (s.contentEquals(devicePlanRelationship)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(devicePlanRelationshipList)) {
            planSheetDao.deleteChild(editInspectionPlanRequest.getId(), devicePlanRelationshipList);
        }
    }

    private void setSchemeName(List<InspectionPlanSheetVo> planSheetVoList) {
        List<Long> ids = planSheetVoList.stream().map(InspectionPlanSheetVo::getInspectionSchemeId).filter(Objects::nonNull).collect(Collectors.toList());
        List<InspectionScheme> inspectionSchemes = inspectionSchemeDao.selectBatchIds(ids);
        Map<Long, String> idNameMap = inspectionSchemes.stream().collect(Collectors.toMap(InspectionScheme::getId, InspectionScheme::getName));
        planSheetVoList.forEach(s -> {
            if (Objects.nonNull(s.getInspectionSchemeId())) {
                s.setInspectionSchemeName(idNameMap.get(s.getInspectionSchemeId()));
            }
        });
    }

    @Override
    public void setTeamName(List<? extends PlanSheetBaseVo> planSheetVoList, Long tenantId) {
        List<UserGroupVo> userGroupVos = inspectorService.queryInspectorTeam(tenantId);
        Map<Long, String> idNameMap = userGroupVos.stream().collect(Collectors.toMap(UserGroupVo::getId, UserGroupVo::getName));
        planSheetVoList.forEach(s -> {
            if (Objects.nonNull(s.getTeamId())) {
                s.setTeamName(idNameMap.get(s.getTeamId()));
            }
        });
    }

    @Override
    public void setNextExecuteTime(List<? extends PlanSheetBaseVo> planSheetVoList) {
        List<Long> ids = planSheetVoList.stream().map(PlanSheetBaseVo::getId).collect(Collectors.toList());
        Map<Long, Trigger> longTriggerMap = planSheetDomain.queryTriggers(ids);
        planSheetVoList.forEach(s -> {
            Trigger trigger = longTriggerMap.get(s.getId());
            Trigger.TriggerState triggerState = null;

            if (Objects.nonNull(trigger) && Objects.nonNull(trigger.getNextFireTime())) {
                try {
                    triggerState = planSheetDomain.queryTriggerState(trigger.getKey());
                } catch (SchedulerException e) {
                    log.error("获取定时触发器失败", e);
                }
                if (triggerState != null && triggerState.equals(Trigger.TriggerState.PAUSED)) {
                    return;
                }

                //巡检时间=下次触发时间+提前生成工单的时间
                long nextFireTime = trigger.getNextFireTime().getTime();
                if (Objects.nonNull(s.getAheadDuration())) {
                    nextFireTime += s.getAheadDuration() * TimeUtil.MINUTE;
                }
                s.setNextFireTime(nextFireTime);
            }
        });
    }

    @Override
    public PlanSheet editUsed(Long id) throws SchedulerException {
        PlanSheet planSheet = planSheetDao.selectById(id);
        Assert.notNull(planSheet, "巡检工单不存在！");
        planSheet.setUsed(true);
        planSheetDao.updateById(planSheet);
        return planSheetDao.selectRelatedById(PlanSheetWithSubLayer.class, id);
    }

    private void setSignInGroupName(List<InspectionPlanSheetVo> planSheetVoList) {
        Set<Long> ids = planSheetVoList.stream().map(PlanSheet::getSignGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SignInGroup> signInGroups = signInGroupDao.selectBatchIds(ids);
        Map<Long, String> idNameMap = signInGroups.stream().collect(Collectors.toMap(SignInGroup::getId, SignInGroup::getName));
        planSheetVoList.forEach(s -> {
            if (Objects.nonNull(s.getSignGroupId())) {
                s.setSignInGroupName(idNameMap.get(s.getSignGroupId()));
            }
        });
    }

    private void checkPlanSheetEndTimeIsValid(List<PlanSheet> planSheetList) {
        LocalDateTime now = LocalDateTime.now();
        for (PlanSheet planSheet : planSheetList) {
            if (Objects.isNull(planSheet.getFinishTime())) {
                continue;
            }
            LocalDateTime finishTime = TimeUtil.timestamp2LocalDateTime(planSheet.getFinishTime());
            if (finishTime.isBefore(now)) {
                throw new ValidationException(String.format("%s由于已达到结束时间无法启用,请修改结束时间后重试", planSheet.getName()));
            }
        }
    }

    private List<InspectionPlanSheetVo> transferToVo(List<? extends PlanSheet> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        return data.stream().map(InspectionPlanSheetVo::new).collect(Collectors.toList());
    }

    private void setProjectInfo(PlanSheetWithSubLayer planSheetWithSubLayer) {
        Long projectId = GlobalInfoUtils.getTenantId();
        if (Objects.isNull(projectId)) {
            return;
        }
        Project project = projectDao.selectById(projectId);
        planSheetWithSubLayer.setProjectId(project.getId());
        planSheetWithSubLayer.setProjectName(project.getName());
    }

    private void setUserInfo(PlanSheetWithSubLayer planSheetWithSubLayer) {
        Long userId = GlobalInfoUtils.getUserId();
        if (Objects.isNull(userId)) {
            return;
        }
        ApiResult<UserVo> userVoResult = eemCloudAuthService.queryByUserId(userId);
        userVoResult.throwExceptionIfFailed();
        UserVo userVo = userVoResult.getData();
        planSheetWithSubLayer.setCreator(userVo.getId());
        planSheetWithSubLayer.setCreatorName(userVo.getName());
    }
}






