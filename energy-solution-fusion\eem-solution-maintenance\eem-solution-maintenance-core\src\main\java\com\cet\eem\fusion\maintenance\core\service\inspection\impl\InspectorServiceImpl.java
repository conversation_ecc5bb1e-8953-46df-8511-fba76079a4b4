package com.cet.eem.fusion.maintenance.core.service.inspection.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.cet.eem.auth.model.user.InspectorUserGroupCustomConfig;
import com.cet.eem.auth.utils.SecurityUserInfoUtils;
import com.cet.eem.fusion.common.utils.excel.ExcelValidationUtils;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.common.definition.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.LoginDef;
import com.cet.eem.fusion.maintenance.core.common.model.auth.user.RoleVo;
import com.cet.eem.fusion.maintenance.core.common.model.auth.user.UserGroupVo;
import com.cet.eem.fusion.maintenance.core.common.model.auth.user.UserVo;
import com.cet.eem.fusion.maintenance.core.constant.InspectionTeamType;
import com.cet.eem.fusion.maintenance.core.listener.InspectorImportListener;
import com.cet.eem.fusion.maintenance.core.model.InspectorImportDto;
import com.cet.eem.fusion.maintenance.core.model.inspector.*;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.common.ErrorUtils;
import com.cet.eem.fusion.common.constant.ExcelType;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.electric.commons.ApiResult;

import com.cet.eem.common.model.auth.node.CloudModelAuthorizedNode;
import com.cet.eem.fusion.common.utils.page.PageUtils;
import com.cet.eem.common.utils.AesUtils;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;
import com.cet.eem.utils.SortUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.cet.eem.bll.maintenance.constant.TextConstant.DEFAULT_PASSWORD;
import static com.cet.eem.bll.maintenance.constant.TextConstant.*;
import static com.cet.eem.common.definition.LoginDef.*;
import com.cet.eem.fusion.common.def.common.ContentTypeDef;

/**
 * @ClassName : InspectorServiceImpl
 * @Description : 巡检人员业务
 * <AUTHOR> zhangh
 * @Date: 2021-04-01 16:47
 */
@Service
@Slf4j
public class InspectorServiceImpl implements InspectorService {

    /**
     * 认证服务
     */
    @Autowired
    private UserRestApi userService;

    @Autowired
    private ApplicationContext applicationContext;

    @Resource
UserRestApi userRestApi;

    private static Map<String, RoleVo> roleVoCache;

    private static final String INSPECTOR_NAME_RULE = "[\\u4E00-\\u9FA5a-zA-Z0-9\\s、\\p{Punct}]+";
    private static final String REMOVE_SPACES_RULE = "(^\\s+)|(\\s+$)";

    @Override
    public ApiApiResultI18n<List<UserVo>> queryMaintenanceUser(QueryInspectorRequest queryInspectorRequest) {
        ApiApiResultI18n<List<UserVo>> ApiResult = new ApiResult<>();
        ApiResult<UserGroupVo> userGroupVoResult = eemCloudAuthService.queryUserGroupById(queryInspectorRequest.getId());
        userGroupVoResult.throwExceptionIfFailed();
        UserGroupVo userGroupVo = userGroupVoResult.getData();
        if (Objects.isNull(userGroupVo)) {
            return Result.ok();
        }
        List<UserVo> users = null;
        if (CollectionUtils.isNotEmpty(userGroupVoResult.getData().getUsers())) {
            users = userGroupVoResult.getData().getUsers().stream().filter(this::isMaintenanceUserWithOutGroupCheck)
                    .sorted((v1, v2) -> CommonUtils.sort(v1.getId(), v2.getId(), true))
                    .collect(Collectors.toList());
        }
        ApiResult.setTotal(CollectionUtils.isEmpty(users) ? 0 : users.size());

        if (Objects.nonNull(queryInspectorRequest.getPage())) {
            users = PageUtils.pageByList(users, queryInspectorRequest.getPage());
        }

        //encrypt(users);
        ApiResult.setData(users);
        return ApiResult;
    }

    /**
     * 巡检人员注册
     *
     * @param inspectorRegistryRequest 巡检人员注册请求
     * @return
     */
    @Override
    public UserVo inspectorRegistry(InspectorRegistryRequest inspectorRegistryRequest) {
        Assert.notNull(GlobalInfoUtils.getUserId(), "项目信息不允许为空！");

        UserVo userVo = convertToCloudUser(inspectorRegistryRequest);
        ApiResult<Object> addResult = eemCloudAuthService.addSimpleUser(userVo);
        addResult.throwExceptionIfFailed();
        ApiResult<UserVo> userVoResult = eemCloudAuthService.queryByUserName(userVo.getName());
        return userVoResult.getData();
    }

    @Override
    public UserVo inspectorRegistrySecurity(InspectorRegistryRequest inspectorRegistryRequest) {
        Assert.notNull(GlobalInfoUtils.getUserId(), "项目信息不允许为空！");

        UserVo userVo = convertToCloudUserSecurity(inspectorRegistryRequest);
        ApiResult<Object> addResult = eemCloudAuthService.addSimpleUser(userVo);
        if (ErrorCode.SUCCESS_CODE != addResult.getCode()) {
            //由于维保人员注册不涉及手机号，注册会自动传入一个不重复的虚拟手机号，500报错的可能性只有用户名重复
            if (Objects.equals(ErrorCode.SERVER_INTERNAL_ERROR, addResult.getCode())) {
                throw new ValidationException("用户名重复");
            }
            throw new ValidationException(addResult.getMsg());
        }
        ApiResult<UserVo> userVoResult = eemCloudAuthService.queryByUserName(userVo.getName());
        SecurityUserInfoUtils.aesEncryptUserVo(userVoResult.getData());
        return userVoResult.getData();
    }

    /**
     * 给用户添加项目节点
     *
     * @param userVo
     */
    private void addProjectNode(UserVo userVo) {
        CloudModelAuthorizedNode cloudModelAuthorizedNode = new CloudModelAuthorizedNode();
        cloudModelAuthorizedNode.setId(GlobalInfoUtils.getTenantId());
        cloudModelAuthorizedNode.setModelLabel(NodeLabelDef.PROJECT);
        cloudModelAuthorizedNode.setChildSelectState(LoginDef.ALL_SELECT);
        cloudModelAuthorizedNode.setDisabled(false);

        List<CloudModelAuthorizedNode> modelNodes = userVo.getModelNodes();
        if (CollectionUtils.isEmpty(modelNodes)) {
            modelNodes = new ArrayList<>();
            modelNodes.add(cloudModelAuthorizedNode);
            userVo.setModelNodes(modelNodes);
        } else {
            boolean present = modelNodes.stream().anyMatch(it -> Objects.equals(it.getModelLabel(), cloudModelAuthorizedNode.getModelLabel()) &&
                    Objects.equals(it.getId(), cloudModelAuthorizedNode.getId()));
            if (!present) {
                modelNodes.add(cloudModelAuthorizedNode);
            }
        }
    }

    /**
     * @param inspectorPasswordChangeRequest
     * @deprecated
     */
    @Override
    @Deprecated
    public void inspectorPasswordChange(InspectorPasswordChangeRequest inspectorPasswordChangeRequest) {
        ApiResult<UserVo> userVoResult = eemCloudAuthService.queryByUserId(inspectorPasswordChangeRequest.getId());
        userVoResult.throwExceptionIfFailed();
        UserVo userVo = userVoResult.getData();
        Assert.notNull(userVo, "找不到用户");
        RootModifyUserPasswordQuery rootModifyUserPasswordQuery = applicationContext.getBean(RootModifyUserPasswordQuery.class);
        rootModifyUserPasswordQuery.setUserId(userVo.getId());
        rootModifyUserPasswordQuery.setUserName(userVo.getName());
        rootModifyUserPasswordQuery.setNewPassword(inspectorPasswordChangeRequest.getPassword());
        ApiResult<Long> modifyResult = eemCloudAuthService.modifyUserPasswordByRoot(rootModifyUserPasswordQuery);
        modifyResult.throwExceptionIfFailed();
    }

    @Override
    public void inspectorPasswordChangeSecurity(InspectorPasswordChangeRequest inspectorPasswordChangeRequest) {
        ApiResult<UserVo> userVoResult = eemCloudAuthService.queryByUserId(inspectorPasswordChangeRequest.getId());
        userVoResult.throwExceptionIfFailed();
        UserVo userVo = userVoResult.getData();
        Assert.notNull(userVo, "找不到用户");
        String pwd = AesUtils.encryptDBKey(AesUtils.decryptLoginKey(inspectorPasswordChangeRequest.getOldPassword()));
        if (!Objects.equals(pwd, userVo.getPassword())) {
            throw new ValidationException("原密码不正确！");
        }

        RootModifyUserPasswordQuery rootModifyUserPasswordQuery = applicationContext.getBean(RootModifyUserPasswordQuery.class);
        rootModifyUserPasswordQuery.setUserId(userVo.getId());
        rootModifyUserPasswordQuery.setUserName(userVo.getName());
        rootModifyUserPasswordQuery.setNewPassword(AesUtils.decryptLoginKey(inspectorPasswordChangeRequest.getPassword()));
        ApiResult<Long> modifyResult = eemCloudAuthService.modifyUserPasswordByRoot(rootModifyUserPasswordQuery);
        modifyResult.throwExceptionIfFailed();
    }

    @Override
    public void checkPasswordChange(InspectorPasswordChangeRequest inspectorPasswordChangeRequest) {
        UserVo user = authUtils.queryAndCheckUser(inspectorPasswordChangeRequest.getId());
        Map<String, Object> configMap = JsonTransferUtils.parseMap(user.getCustomConfig());
        configMap.put(ColumnDef.CHECK_PASSWORD, inspectorPasswordChangeRequest.getPassword());
        user.setCustomConfig(JsonTransferUtils.toJSONString(configMap));
        eemCloudAuthService.updateUser(user);
    }

    @Override
    public void checkPasswordChangeSecurity(InspectorPasswordChangeRequest inspectorPasswordChangeRequest) {
        UserVo user = authUtils.queryAndCheckUser(inspectorPasswordChangeRequest.getId());
        CustomConfigVo customConfig = JsonTransferUtils.parseObject(user.getCustomConfig(), CustomConfigVo.class);
        String pwd = AesUtils.encryptLoginKey(AesUtils.decryptDBKey(customConfig.getCheckPassword()));
        if (!Objects.equals(inspectorPasswordChangeRequest.getOldPassword(), pwd)) {
            throw new ValidationException("原密码错误！");
        }
        String newPassword = AesUtils.decryptLoginKey(inspectorPasswordChangeRequest.getNewPassword());
        customConfig.setCheckPassword(AesUtils.encryptDBKey(newPassword));
        user.setCustomConfig(JsonTransferUtils.toJSONString(customConfig));
        eemCloudAuthService.updateUser(user);
    }

    @Override
    public QueryInspectorRolesResult queryInspectorRoleInfo(QueryInspectorRolesRequest queryInspectorRolesRequest) {
        QueryInspectorRolesResult queryInspectorRolesResult = new QueryInspectorRolesResult();
        List<UserVo> allInspectorUser = getAllFreeInspectorUser(queryInspectorRolesRequest.getTenantId());
        if (Objects.nonNull(queryInspectorRolesRequest.getId())) {
            QueryInspectorRequest queryInspectorRequest = new QueryInspectorRequest();
            queryInspectorRequest.setId(queryInspectorRolesRequest.getId());
            ApiApiResultI18n<List<UserVo>> ApiResult = queryMaintenanceUser(queryInspectorRequest);
            List<UserVo> data = ApiResult.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                allInspectorUser.addAll(data);
                queryInspectorRolesResult.setSelectUserIds(data.stream().map(UserVo::getId).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isEmpty(allInspectorUser)) {
            return queryInspectorRolesResult;
        }
        Map<EntityWithName, List<UserVo>> roleIdUserMap = new HashMap<>();
        for (UserVo userVo : allInspectorUser) {
            List<RoleVo> roles = userVo.getRoles();
            if (CollectionUtils.isEmpty(roles)) {
                continue;
            }
            for (RoleVo role : roles) {
                if (!isInspectorRole(role)) {
                    continue;
                }
                EntityWithName EntityWithName = new EntityWithName();
                EntityWithName.setId(role.getId());
                EntityWithName.setName(role.getName());
                List<UserVo> userVos = roleIdUserMap.computeIfAbsent(EntityWithName, s -> new ArrayList<>());
                userVos.add(userVo);
            }
        }
        List<JSONObject> jsonObjectList = new ArrayList<>();
        for (Map.Entry<EntityWithName, List<UserVo>> entry : roleIdUserMap.entrySet()) {
            EntityWithName key = entry.getKey();
            List<UserVo> value = entry.getValue();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ColumnDef.ID, key.getId());
            jsonObject.put(ColumnDef.NAME, key.getName());
            jsonObject.put(ColumnDef.USERS, value);
            jsonObjectList.add(jsonObject);
        }

        jsonObjectList.sort((v1, v2) -> SortUtils.compareChar(v1.get(ColumnDef.NAME, String.class), v2.get(ColumnDef.NAME, String.class)));
        queryInspectorRolesResult.setUserRoles(jsonObjectList);
        return queryInspectorRolesResult;
    }

    @Override
    public Long addInspectorTeam(AddInspectorTeamRequest addInspectorTeamRequest) {
        AddUserGroupQuery addUserGroupQuery = new AddUserGroupQuery();
        addUserGroupQuery.setName(addInspectorTeamRequest.getName());
        addUserGroupQuery.setTenantId(addInspectorTeamRequest.getTenantId());
        InspectorUserGroupCustomConfig inspectorUserGroupCustomConfig = new InspectorUserGroupCustomConfig();
        inspectorUserGroupCustomConfig.setUserGroupType(LoginDef.INSPECTOR_USER_GROUP_TYPE);
        addUserGroupQuery.setCustomConfig(JsonUtil.toJSONString(inspectorUserGroupCustomConfig));
        addUserGroupQuery.setParentId(0L);
        ApiResult<Long> addUserGroupResult = eemCloudAuthService.addUserGroup(addUserGroupQuery);
        if (!Objects.equals(ErrorCode.SUCCESS_CODE, addUserGroupResult.getCode())) {
            throw new ValidationException(addUserGroupResult.getMsg());
        }
        if (CollectionUtils.isNotEmpty(addInspectorTeamRequest.getUserIds())) {
            List<UserVo> allUsers = authUtils.queryBatchUser(addInspectorTeamRequest.getUserIds());
            if (CollectionUtils.isNotEmpty(allUsers)) {
                for (UserVo userVo : allUsers) {
                    userVo.setRelativeUserGroup(Collections.singletonList(addUserGroupResult.getData()));
                    ApiResult<Object> updateUserResult = eemCloudAuthService.updateUser(userVo);
                    if (!Objects.equals(ErrorCode.SUCCESS_CODE, updateUserResult.getCode())) {
                        throw new ValidationException(updateUserResult.getMsg());
                    }
                }
            }
        }

        return addUserGroupResult.getData();
    }

    @Override
    public void editInspectorTeam(EditInspectorTeamRequest editInspectorTeamRequest) {
        ApiResult<UserGroupVo> userGroupVoResult = eemCloudAuthService.queryUserGroupById(editInspectorTeamRequest.getId());
        userGroupVoResult.throwExceptionIfFailed();
        UserGroupVo data = userGroupVoResult.getData();
        List<UserVo> users = data.getUsers();
        userGroupNameChange(data, editInspectorTeamRequest);
        handlerUserAddToGroup(users, editInspectorTeamRequest.getUserIds(), editInspectorTeamRequest.getId());
        handlerUserDeleteFromGroup(users, editInspectorTeamRequest.getUserIds(), editInspectorTeamRequest.getId());
    }

    @Override
    public List<RoleVo> queryInspectorRoles(Long tenantId) {
        ApiResult<List<RoleVo>> userRoles = eemCloudAuthService.getUserRoles(tenantId);
        userRoles.throwExceptionIfFailed();
        List<RoleVo> data = userRoles.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            data = data.stream().filter(this::isInspectorRole).collect(Collectors.toList());
        }
        return data;
    }

    @Override
    public UserVo editInspectorUser(EditInspectorUserRequest editInspectorUserRequest) {
        ApiResult<UserVo> userVoResult = eemCloudAuthService.queryByUserId(editInspectorUserRequest.getId());
        userVoResult.throwExceptionIfFailed();
        UserVo userVo = userVoResult.getData();
        Assert.notNull(userVo, String.format("找不到id为%d的巡检人员", editInspectorUserRequest.getId()));
        userVo.setName(AesUtils.decryptLoginKey(editInspectorUserRequest.getName()));
        userVo.setNicName(AesUtils.decryptLoginKey(editInspectorUserRequest.getName()));
        userVo.setRoles(Collections.singletonList(editInspectorUserRequest.getRole()));
        ApiResult<Object> updateUser = eemCloudAuthService.updateUser(userVo);
        if (!updateUser.isSuccess()) {
            throw new ValidationException(updateUser.getMsg());
        }
        return userVo;
    }

    @Override
    public void deleteInspectorUser(Long id, String name, Long tenantId) {
        ApiResult<Object> result = eemCloudAuthService.deleteUser(id, name, tenantId);
        result.throwExceptionIfFailed();
    }

    @Override
    public void deleteInspectorTeam(Long id, String name, Long tenantId) {
        ApiResult<UserGroupVo> group = eemCloudAuthService.queryUserGroupById(id);
        group.throwExceptionIfFailed();
        Assert.notNull(group, "未查询到需要删除的用户组！");
        if (CollectionUtils.isNotEmpty(group.getData().getUsers())) {
            throw new ValidationException("当前班组下存在用户，不允许删除！");
        }

        ApiResult<Long> result = eemCloudAuthService.deleteUserGroup(id, name, tenantId);
        result.throwExceptionIfFailed();
    }

    @Override
    public List<UserVo> querySameGroupMember() {
        Long userId = GlobalInfoUtils.getUserId();
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        ApiResult<UserVo> userVoResult = eemCloudAuthService.queryByUserId(userId);
        userVoResult.throwExceptionIfFailed();
        UserVo data = userVoResult.getData();
        if (!isMaintenanceUser(data)) {
            throw new IllegalArgumentException("非巡检用户");
        }
        if (CollectionUtils.isEmpty(data.getRelativeUserGroup())) {
            return Collections.emptyList();
        }
        Long teamId = data.getRelativeUserGroup().stream().findFirst().orElse(null);
        ApiApiResultI18n<List<UserVo>> ApiResult = this.queryMaintenanceUser(new QueryInspectorRequest(teamId));
        SecurityUserInfoUtils.aesEncryptUserVo(ApiResult.getData());
        return ApiResult.getData();
    }

    @Override
    public List<UserGroupVo> queryInspectorTeam(Long tenantId) {
        return getInspectorUserGroup(tenantId);
    }

    @Override
    public List<UserGroupVo> queryInspectorTeamWithoutUser(Long tenantId) {
        return getInspectorUserGroupWithoutUser(tenantId);
    }

    @Override
    public List<UserVo> queryMaintenanceUser(Long groupId) {
        UserGroupVo userGroupVo = authUtils.queryUserGroupByIdAndCheck(groupId);
        SecurityUserInfoUtils.aesEncryptUserVo(userGroupVo.getUsers());
        return userGroupVo.getUsers();
    }

    private void handlerUserDeleteFromGroup(List<UserVo> users, List<Long> userIds, Long id) {
        List<UserVo> waitDeleteUser = null;
        if (CollectionUtils.isEmpty(userIds)) {
            waitDeleteUser = users;
        } else {
            waitDeleteUser = users.stream().filter(s -> !userIds.contains(s.getId())).collect(Collectors.toList());
        }
        for (UserVo userVo : waitDeleteUser) {
            if (CollectionUtils.isNotEmpty(userVo.getRelativeUserGroup())) {
                userVo.getRelativeUserGroup().remove(id);
            }
            ApiResult<Object> updateUserResult = eemCloudAuthService.updateUser(userVo);
            updateUserResult.throwExceptionIfFailed();
        }
    }

    private void handlerUserAddToGroup(List<UserVo> users, List<Long> userIds, Long id) {
        List<Long> waitAddUserIds = null;
        if (CollectionUtils.isEmpty(users)) {
            waitAddUserIds = userIds;
        } else {
            List<Long> existUserIds = users.stream().map(UserVo::getId).collect(Collectors.toList());
            waitAddUserIds = userIds.stream().filter(s -> !existUserIds.contains(s)).collect(Collectors.toList());
        }
        for (Long userId : waitAddUserIds) {
            ApiResult<UserVo> userVoResult = eemCloudAuthService.queryByUserId(userId);
            userVoResult.throwExceptionIfFailed();
            UserVo userVo = userVoResult.getData();
            if (Objects.isNull(userVo)) {
                continue;
            }
            userVo.setRelativeUserGroup(Collections.singletonList(id));
            ApiResult<Object> updateResult = eemCloudAuthService.updateUser(userVo);
            updateResult.throwExceptionIfFailed();
        }
    }

    private void userGroupNameChange(UserGroupVo data, EditInspectorTeamRequest editInspectorTeamRequest) {
        if (editInspectorTeamRequest.getName().equals(data.getName())) {
            return;
        }
        data.setName(editInspectorTeamRequest.getName());
        ApiResult<Object> objectResult = eemCloudAuthService.updateUserGroup(data);
        objectResult.throwExceptionIfFailed();
    }

    /**
     * 获取所有的巡检人员
     *
     * @param tenantId
     * @return
     * @deprecated 废弃
     */
    @Deprecated
    private List<UserVo> getAllMaintenanceUser(Long tenantId) {
        ApiApiResultI18n<List<UserVo>> allUsers = eemCloudAuthService.getAllUsers(tenantId);
        List<UserVo> data = allUsers.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            data = data.stream().filter(this::isMaintenanceUser).collect(Collectors.toList());
        }
        return data;
    }

    /**
     * 获取所有的巡检人员(包括没有分组的
     *
     * @param tenantId
     * @return
     */
    private List<UserVo> getAllMaintenanceUserWithOutGroup(Long tenantId) {
        ApiApiResultI18n<List<UserVo>> allUsers = eemCloudAuthService.getAllUsers(tenantId);
        List<UserVo> data = allUsers.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            data = data.stream().filter(this::isMaintenanceUserWithOutGroupCheck).collect(Collectors.toList());
        }
        return data;
    }

    @Override
    public boolean isMaintenanceUser(@NotNull UserVo userVo) {
        List<Long> groups = userVo.getRelativeUserGroup();
        if (CollectionUtils.isEmpty(groups)) {
            log.info("用户id为{}的用户无所属班组！", userVo.getId());
            return false;
        }

        return isMaintenanceUserWithOutGroupCheck(userVo);
    }

    private boolean isMaintenanceUserWithOutGroupCheck(@NotNull UserVo userVo) {
        if (StringUtils.isEmpty(userVo.getCustomConfig())) {
            return false;
        }
        JSONObject jsonObject = new JSONObject(userVo.getCustomConfig());
        Integer userType = jsonObject.getInt(ColumnDef.USER_TYPE);
        if (Objects.nonNull(userType) && INSPECTOR_USER_TYPE.equals(userType)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isInspectUser(@NotNull UserVo userVo) {
        if (!isMaintenanceUser(userVo)) {
            return false;
        }

        List<RoleVo> roles = userVo.getRoles();
        if (CollectionUtils.isEmpty(roles)) {
            log.info("用户id为{}的用户无所属角色！", userVo.getId());
            return false;
        }

        CustomConfigVo customConfig = JsonTransferUtils.parseObject(roles.get(0).getCustomConfig(), CustomConfigVo.class);
        if (customConfig == null) {
            return false;
        }

        return Objects.equals(customConfig.getDutyType(), DutyTypeDef.INSPECT);
    }

    @Override
    public boolean isInspectorUserByUserId() {
        Long userId = GlobalInfoUtils.getUserId();
        if (Objects.isNull(userId)) {
            return false;
        }
        ApiResult<UserVo> userVoResult = eemCloudAuthService.queryByUserId(userId);
        userVoResult.throwExceptionIfFailed();
        return isMaintenanceUser(userVoResult.getData());
    }

    /**
     * 判断某个角色是否为运维角色：包括巡检、维修、维保
     *
     * @param roleVo 角色信息
     * @return 如果为运维角色返回true，否则返回false
     */
    private boolean isInspectorRole(RoleVo roleVo) {
        if (Objects.isNull(roleVo) || StringUtils.isEmpty(roleVo.getCustomConfig())) {
            return false;
        }

        CustomConfigVo config = JsonTransferUtils.parseObject(roleVo.getCustomConfig(), CustomConfigVo.class);
        // 角色类型为运维角色，但是不包括技术员
        if (Objects.nonNull(config) && Objects.nonNull(config.getRoleType()) && INSPECTOR_ROLE_TYPE.equals(config.getRoleType())) {
            return !(Objects.isNull(config.getDutyType()) || Objects.equals(config.getDutyType(), DutyTypeDef.TECHNICAL));
        }

        return false;
    }

    /**
     * 获取未分配班组的人员
     *
     * @param tenantId
     * @return
     */
    private List<UserVo> getAllFreeInspectorUser(Long tenantId) {
        List<UserGroupVo> inspectorUserGroup = getInspectorUserGroup(tenantId);
        if (inspectorUserGroup == null) {
            return Collections.emptyList();
        }
        List<Long> hasGroupUserIds = inspectorUserGroup.stream().map(UserGroupVo::getUsers).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).map(UserVo::getId).collect(Collectors.toList());
        List<UserVo> allInspectorUser = getAllMaintenanceUserWithOutGroup(tenantId);
        if (CollectionUtils.isNotEmpty(allInspectorUser)) {
            allInspectorUser = allInspectorUser.stream().filter(s -> !hasGroupUserIds.contains(s.getId())).collect(Collectors.toList());
        }
        return allInspectorUser;
    }

    private List<UserGroupVo> getInspectorUserGroupWithoutUser(Long tenantId) {
        ApiResult<List<UserGroupVo>> allUserGroups = eemCloudAuthService.getAllUserGroupsWithoutUser(tenantId);
        allUserGroups.throwExceptionIfFailed();
        List<UserGroupVo> data = allUserGroups.getData();
        return getUserGroupVos(data);
    }

    /**
     * 获取巡检专用的用户组
     *
     * @param tenantId
     * @return
     */
    private List<UserGroupVo> getInspectorUserGroup(Long tenantId) {
        ApiResult<List<UserGroupVo>> allUserGroups = eemCloudAuthService.getAllUserGroups(tenantId);
        allUserGroups.throwExceptionIfFailed();
        List<UserGroupVo> data = allUserGroups.getData();
        return getUserGroupVos(data);
    }

    private List<UserGroupVo> getUserGroupVos(List<UserGroupVo> data) {
        if (CollectionUtils.isEmpty(data)) {
            return data;
        }

        data = data.stream().filter(s -> {
            if (StringUtils.isEmpty(s.getCustomConfig())) {
                return false;
            }
            InspectorUserGroupCustomConfig inspectorUserGroupCustomConfig = JsonUtil.parseObject(s.getCustomConfig(), InspectorUserGroupCustomConfig.class);
            if (Objects.isNull(inspectorUserGroupCustomConfig) || !LoginDef.INSPECTOR_USER_GROUP_TYPE.equals(inspectorUserGroupCustomConfig.getUserGroupType())) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        for (UserGroupVo userGroupVo : data) {
            if (CollectionUtils.isNotEmpty(userGroupVo.getUsers())) {
                userGroupVo.setUsers(userGroupVo.getUsers().stream().filter(this::isMaintenanceUser).collect(Collectors.toList()));
            }
        }
        return data;
    }

    /**
     * @param inspectorRegistryRequest
     * @return
     * @deprecated 废弃
     */
    @Deprecated
    public UserVo convertToCloudUser(InspectorRegistryRequest inspectorRegistryRequest) {
        UserVo userVo = new UserVo();
        CustomConfigVo customConfigVo = new CustomConfigVo();
        customConfigVo.setCheckPassword(inspectorRegistryRequest.getCheckPassword());
        customConfigVo.setUserType(INSPECTOR_USER_TYPE);
        customConfigVo.setNotifyType(Collections.emptyList());
        userVo.setCustomConfig(JsonTransferUtils.toJSONString(customConfigVo));
        userVo.setName(inspectorRegistryRequest.getName());
        userVo.setNicName(inspectorRegistryRequest.getName());
        userVo.setPassword(inspectorRegistryRequest.getPassword());
        userVo.setTenantId(inspectorRegistryRequest.getRole().getTenantId());
        userVo.setRoles(Collections.singletonList(inspectorRegistryRequest.getRole()));
        addProjectNode(userVo);
        //勿删，有非空校验
        userVo.setEmail(StringUtils.EMPTY);
        userVo.setMobilePhone(getDynamicPhoneNumber(inspectorRegistryRequest.getRole().getTenantId()));
        return userVo;
    }

    public UserVo convertToCloudUserSecurity(InspectorRegistryRequest inspectorRegistryRequest) {
        UserVo userVo = new UserVo();
        CustomConfigVo customConfigVo = new CustomConfigVo();
        customConfigVo.setCheckPassword(AesUtils.encryptDBKey(AesUtils.decryptLoginKey(inspectorRegistryRequest.getCheckPassword())));
        customConfigVo.setUserType(INSPECTOR_USER_TYPE);
        customConfigVo.setNotifyType(Collections.emptyList());
        userVo.setCustomConfig(JsonTransferUtils.toJSONString(customConfigVo));
        userVo.setName(AesUtils.decryptLoginKey(inspectorRegistryRequest.getName()));
        userVo.setNicName(userVo.getName());
        userVo.setPassword(AesUtils.decryptLoginKey(inspectorRegistryRequest.getPassword()));
        userVo.setTenantId(inspectorRegistryRequest.getRole().getTenantId());
        userVo.setRoles(Collections.singletonList(inspectorRegistryRequest.getRole()));
        addProjectNode(userVo);
        //勿删，有非空校验
        userVo.setEmail(StringUtils.EMPTY);
        userVo.setMobilePhone(getDynamicPhoneNumber(inspectorRegistryRequest.getRole().getTenantId()));
        return userVo;
    }

    private String getDynamicPhoneNumber(Long tenantId) {
        ApiApiResultI18n<List<UserVo>> allUsers = eemCloudAuthService.getAllUsers(tenantId);
        allUsers.throwExceptionIfFailed();
        List<Long> allPhoneNumber = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allUsers.getData())) {
            allPhoneNumber = allUsers.getData().stream().map(UserVo::getMobilePhone).filter(StringUtils::isNotEmpty).map(Long::valueOf).collect(Collectors.toList());
        }
        long defaultPhoneNumber;
        do {
            defaultPhoneNumber = 15000000000L;
            int salt = RandomUtils.nextInt(1000000000);
            defaultPhoneNumber = defaultPhoneNumber + salt;
        } while (allPhoneNumber.contains(defaultPhoneNumber));
        return String.valueOf(defaultPhoneNumber);
    }

    @Override
    public boolean checkPassword(InspectorPasswordChangeRequest inspectorPasswordChangeRequest) {
        if (StringUtils.isBlank(inspectorPasswordChangeRequest.getPassword())) {
            return false;
        }

        UserVo user = authUtils.queryAndCheckUser(inspectorPasswordChangeRequest.getId());
        CustomConfigVo customConfig = JsonTransferUtils.parseObject(user.getCustomConfig(), CustomConfigVo.class);
        if (customConfig == null) {
            return false;
        }

        return Objects.equals(inspectorPasswordChangeRequest.getPassword(), customConfig.getCheckPassword());
    }

    @Override
    public boolean checkPasswordSecurity(InspectorPasswordCheckRequest inspectorPasswordChangeRequest) {
        UserVo user = authUtils.queryAndCheckUser(inspectorPasswordChangeRequest.getId());
        CustomConfigVo customConfig = JsonTransferUtils.parseObject(user.getCustomConfig(), CustomConfigVo.class);
        if (customConfig == null) {
            return false;
        }

        String pwdFormat = String.format("%s#%s#%s", AesUtils.KEY, AesUtils.decryptDBKey(customConfig.getCheckPassword()), inspectorPasswordChangeRequest.getTimestamps());
        return Objects.equals(inspectorPasswordChangeRequest.getPassword(), DigestUtils.md5DigestAsHex(pwdFormat.getBytes(StandardCharsets.UTF_8)));
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try (Workbook workbook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA)) {
            String fileName = IMPORT_INSPECTOR_FILE_NAME_PREFIX + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT);
            Sheet sheet = workbook.createSheet(IMPORT_INSPECTOR_FILE_NAME_PREFIX);
            sheet.setDefaultRowHeightInPoints(IMPORT_SCHEME_FIRST_COLUMN_WIDTH);
            PoiExcelUtils.setColumnWidth(0, 1, sheet, IMPORT_SCHEME_FIRST_COLUMN_WIDTH);
            PoiExcelUtils.setColumnWidth(1, 5, sheet, IMPORT_SCHEME_COLUMN_WIDTH);
            List<Row> rows = PoiExcelUtils.initRows(3, sheet, IMPORT_SCHEME_ROW_HEIGHT);
            Font font = workbook.createFont();
            font.setFontName(FONT_MICROSOFT_YAHEI);
            font.setFontHeightInPoints((short) 11);
            CellStyle firstRowStyle = workbook.createCellStyle();
            firstRowStyle.setWrapText(true);
            firstRowStyle.setFont(font);
            firstRowStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //生成第一行文字说明
            initDocumentation(IMPORT_INSPECTOR_CAUTION, firstRowStyle, sheet, rows.get(0));
            CellStyle contentStyle = workbook.createCellStyle();
            contentStyle.setAlignment(HorizontalAlignment.CENTER);
            contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentStyle.setFont(font);
            //生成表头
            writeHeaders(contentStyle, workbook, sheet, rows.get(1));
            //生成demo数据
            PoiExcelUtils.createCell(rows.get(2), 0, contentStyle, 1);
            PoiExcelUtils.createCell(rows.get(2), 1, contentStyle, INSPECT_TEAM_NAME_DEMO);
            PoiExcelUtils.createCell(rows.get(2), 2, contentStyle, INSPECT_TEAM_TYPE_DEMO);
            PoiExcelUtils.createCell(rows.get(2), 3, contentStyle, DUTY_OFFICER_DEMO);
            PoiExcelUtils.createCell(rows.get(2), 4, contentStyle, DUTY_STAFF_DEMO);

            FileUtils.downloadExcel(response, workbook, fileName, ContentTypeDef.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            ErrorUtils.exportError(IMPORT_INSPECTOR_FILE_NAME_PREFIX, e);
        }
    }

    @Override
    @Transactional
    public void importItem(MultipartFile file, Long tenantId) throws IOException {
        try {
            List<InspectorImportDto> data = parseFileToDto(file);
            validateData(data, tenantId);
            saveData(data, tenantId);
        } catch (Exception e) {
            throw e;
        }
    }

    private void saveData(List<InspectorImportDto> data, Long tenantId) {
        Map<String, Long> userGroupVoCache;
        ApiResult<List<UserGroupVo>> allUserGroupsResponse = eemCloudAuthService.getAllUserGroups(tenantId);
        Map<String, UserVo> allNameUserMap = eemCloudAuthService.getAllUsers(tenantId).getData()
                .stream().collect(Collectors.toMap(UserVo::getName, userVo -> userVo));
        if (!(ErrorCode.SUCCESS_CODE == (allUserGroupsResponse.getCode()))) {
            throw new RuntimeException("用户服务异常，请稍后再试");
        }
        if (CollectionUtils.isEmpty(allUserGroupsResponse.getData())) {
            userGroupVoCache = new HashMap<>();
        } else {
            userGroupVoCache = allUserGroupsResponse.getData()
                    .stream().collect(Collectors.toMap(UserGroupVo::getName, UserGroupVo::getId));
        }
        ApiResult<List<RoleVo>> userRolesResponse = eemCloudAuthService.getUserRoles(tenantId);
        if (!(ErrorCode.SUCCESS_CODE == userRolesResponse.getCode())) {
            throw new RuntimeException("用户服务异常，请稍后再试");
        }
        if (CollectionUtils.isEmpty(userRolesResponse.getData())) {
            roleVoCache = new HashMap<>();
        } else {
            roleVoCache = userRolesResponse.getData().stream().collect(Collectors.toMap(RoleVo::getName, roleVo -> roleVo));
        }
        LinkedList<UserVo> addUserList = new LinkedList<>();
        LinkedList<UserVo> updateUserList = new LinkedList<>();
        //创建用户组
        for (InspectorImportDto inspectorImportDto : data) {
            UserGroupVo userGroupVo = new UserGroupVo();
            ApiResult<Object> objectResult = buildAndAddUserGroup(inspectorImportDto, tenantId, userGroupVo);
            if (objectResult.getData() != null) {
                userGroupVoCache.put(userGroupVo.getName(), Long.valueOf(objectResult.getData().toString()));
            }
            //组长用户数据入库
            if (StringUtils.isNotEmpty(inspectorImportDto.getDutyOfficer())) {
                String[] dutyOfficers = inspectorImportDto.getDutyOfficer().split("、");
                for (int i = 0; i < dutyOfficers.length; i++) {
                    addUserInfoToList(dutyOfficers[i], inspectorImportDto, tenantId, addUserList, updateUserList, true, userGroupVoCache, allNameUserMap);
                }
            }
            if (StringUtils.isNotEmpty(inspectorImportDto.getDutystaff())) {
                //组员用户数据入库
                String[] dutyStaffs = inspectorImportDto.getDutystaff().split("、");
                for (int i = 0; i < dutyStaffs.length; i++) {
                    addUserInfoToList(dutyStaffs[i], inspectorImportDto, tenantId, addUserList, updateUserList, false, userGroupVoCache, allNameUserMap);
                }
            }
        }
        eemCloudAuthService.addUsers(addUserList);
        eemCloudAuthService.updateUsers(updateUserList);
    }

    void addUserInfoToList(String name, InspectorImportDto inspectorImportDto, Long tenantId, LinkedList<UserVo> addUserList, LinkedList<UserVo> updateUsers, boolean isOfficer, Map<String, Long> userGroupVoCache, Map<String, UserVo> allNameUserMap) {
        UserVo userVo = new UserVo();
        //消除字符串头尾的空格
        name = name.replaceAll(REMOVE_SPACES_RULE, "");
        if (StringUtils.isEmpty(name)) {
            return;
        }
        String roleName;
        if (isOfficer) {
            roleName = ObjectUtil.equal(inspectorImportDto.getInspectTeamType(), InspectionTeamType.INSPECT_TEAM.getValue()) ? ROLE_NAME_INSPECT_OFFICER : ROLE_NAME_REPAIR_OFFICER;
        } else {
            roleName = ObjectUtil.equal(inspectorImportDto.getInspectTeamType(), InspectionTeamType.INSPECT_TEAM.getValue()) ? ROLE_NAME_INSPECT_STAFF : ROLE_NAME_REPAIR_STAFF;
        }
        UserVo oldUser = allNameUserMap.get(name);
        if (oldUser == null) {
            userVo.setName(name);
            userVo.setNicName(name);
            userVo.setRelativeUserGroup(Collections.singletonList(userGroupVoCache.get(inspectorImportDto.getInspectTeamName())));
            RoleVo roleVo = roleVoCache.get(roleName);
            userVo.setRoles(Collections.singletonList(roleVo));
            CustomConfigVo customConfigVo = new CustomConfigVo();
            customConfigVo.setCheckPassword(AesUtils.encryptDBKey(DEFAULT_CHECK_PASSWORD));
            customConfigVo.setUserType(INSPECTOR_USER_TYPE);
            customConfigVo.setNotifyType(Collections.emptyList());
            userVo.setCustomConfig(JsonTransferUtils.toJSONString(customConfigVo));
            userVo.setPassword(DEFAULT_PASSWORD);
            userVo.setTenantId(tenantId);
            userVo.setMobilePhone(getDynamicPhoneNumber(tenantId));
            userVo.setEmail(StringUtils.EMPTY);
            addProjectNode(userVo);
            addUserList.add(userVo);

        } else if (oldUser.getRelativeUserGroup().size() == 0) {
            userVo = allNameUserMap.get(name);
            userVo.setRelativeUserGroup(Collections.singletonList(userGroupVoCache.get(inspectorImportDto.getInspectTeamName())));
            updateUsers.add(userVo);
        }
    }

    ApiResult<Object> buildAndAddUserGroup(InspectorImportDto inspectorImportDto, Long tenantId, UserGroupVo userGroupVo) {
        userGroupVo.setTenantId(tenantId);
        userGroupVo.setName(inspectorImportDto.getInspectTeamName());
        userGroupVo.setParentId(0L);
        InspectorUserGroupCustomConfig inspectorUserGroupCustomConfig = new InspectorUserGroupCustomConfig();
        inspectorUserGroupCustomConfig.setUserGroupType(INSPECTOR_USER_GROUP_TYPE);
        userGroupVo.setCustomConfig(JsonUtil.toJSONString(inspectorUserGroupCustomConfig));
        return eemCloudAuthService.addUserGroup(userGroupVo);
    }

    private List<InspectorImportDto> parseFileToDto(MultipartFile file) throws IOException {
// 实例化解析监听器
        InspectorImportListener listener = new InspectorImportListener();
        // 实例化 EasyExcel 读取对象
        EasyExcel.read(file.getInputStream(), InspectorImportDto.class, listener).sheet().doRead();
        // 获取解析结果
        List<InspectorImportDto> dataList = listener.getDataList();
        //移除表头数据
        dataList.remove(0);
        return dataList;

    }

    /**
     * 校验数据合法性
     *
     * @param data
     * @return 需要新增的用户 key-用户名 value-角色名
     */
    void validateData(List<InspectorImportDto> data, Long tenantId) {
        Map<String, Object> inspectorNameCache = new HashMap<>();
        List<UserVo> allUsersCache = eemCloudAuthService.getAllUsers(tenantId).getData();
        Map<Long, String> userGroupIdNameMap = eemCloudAuthService.getAllUserGroups(tenantId).getData().stream().collect(Collectors.toMap(userGroupVo -> userGroupVo.getId(), userGroupVo -> userGroupVo.getName()));
        allUsersCache.forEach(userVo -> {
            String username = userVo.getName();
            Long key = userVo.getRelativeUserGroup().size() == 0 ? -1L : userVo.getRelativeUserGroup().get(0);
            String groupName = userGroupIdNameMap.get(key);
            inspectorNameCache.put(username, groupName);
        });
        Map<String, List<RoleVo>> userRoleCache = allUsersCache.stream().filter(userVo -> userVo.getRoles() != null).collect(Collectors.toMap(UserVo::getName, UserVo::getRoles));
        String teamType;
        int rowNum = 3;
        for (InspectorImportDto inspectorImportDto : data) {
            generalValidation(inspectorImportDto, rowNum);
            teamType = inspectorImportDto.getInspectTeamType();
            if (StringUtils.isNotEmpty(inspectorImportDto.getDutyOfficer())) {
                String[] dutyOfficers = inspectorImportDto.getDutyOfficer().split(SplitCharDef.COMMA);
                //校验班组负责人
                for (int i = 0; i < dutyOfficers.length; i++) {
                    userRoleValidation(inspectorImportDto.getInspectTeamName(), teamType, dutyOfficers[i], userRoleCache, rowNum, true, inspectorNameCache);
                }
            }
            if (StringUtils.isNotEmpty(inspectorImportDto.getDutystaff())) {
                String[] dutyStaffs = inspectorImportDto.getDutystaff().split(SplitCharDef.COMMA);
                //校验班组成员
                for (int i = 0; i < dutyStaffs.length; i++) {
                    userRoleValidation(inspectorImportDto.getInspectTeamName(), teamType, dutyStaffs[i], userRoleCache, rowNum, false, inspectorNameCache);
                }
            }
            rowNum++;
        }
    }

    void generalValidation(InspectorImportDto inspectorImportDto, int rowNum) {
        Map<String, Object> inspectTeamNameCache = new HashMap<>();
        if (StringUtils.isEmpty(inspectorImportDto.getInspectTeamName()) || inspectorImportDto.getInspectTeamName().split(" ").length == 0) {
            throw new ValidationException("第" + rowNum + "行第2列班组名称不能为空");
        }
        if (inspectorImportDto.getInspectTeamName().length() >= 50) {
            throw new ValidationException("第" + rowNum + "行第2列班组名称长度超过50个字符");
        }
        if (inspectTeamNameCache.containsKey(inspectorImportDto.getInspectTeamName())) {
            throw new ValidationException("第" + rowNum + "行第2列班组名称重复");
        } else {
            inspectTeamNameCache.put(inspectorImportDto.getInspectTeamName(), null);
        }
        if (StringUtils.isNotEmpty(inspectorImportDto.getDutyOfficer()) && !inspectorImportDto.getDutyOfficer().matches(INSPECTOR_NAME_RULE)) {
            throw new ValidationException("第" + rowNum + "行第4列姓名不能含有特殊字符");
        }
        if (StringUtils.isNotEmpty(inspectorImportDto.getDutystaff()) && !inspectorImportDto.getDutystaff().matches(INSPECTOR_NAME_RULE)) {
            throw new ValidationException("第" + rowNum + "行第5列姓名不能含有特殊字符");
        }
        if ((StringUtils.isNotEmpty(inspectorImportDto.getDutyOfficer()) || StringUtils.isNotEmpty(inspectorImportDto.getDutystaff())) && StringUtils.isEmpty(inspectorImportDto.getInspectTeamType())) {
            throw new ValidationException("第" + rowNum + "行第3列班组类型不能为空");
        }
    }

    void userRoleValidation(String teamName, String teamType, String userName, Map<String, List<RoleVo>> userCache, int rowNum, boolean isOfficer, Map<String, Object> inspectorNameCache) {
        userName = userName.replaceAll(REMOVE_SPACES_RULE, "");
        if (StringUtils.isEmpty(userName)) {
            return;
        }
        String role;
        if (isOfficer) {
            role = teamType + "负责人";
        } else {
            role = teamType + "用户";
        }
        if (userName.length() > 30) {
            throw new ValidationException("第" + rowNum + "行第5列人员名称" + userName + "超过30字符");
        }
        if (userCache.containsKey(userName)) {
            RoleVo roleVo = userCache.get(userName).get(0);
            //维修人员
            if (ObjectUtil.notEqual(role, roleVo.getName())) {
                throw new ValidationException("第" + rowNum + "行" + role + ":" + userName + "用户已存在且绑定其他角色");
            }
        }
        if (inspectorNameCache.containsKey(userName) && !teamName.equals(inspectorNameCache.get(userName)) && inspectorNameCache.get(userName) != null) {
            throw new ValidationException("第" + rowNum + "行" + role + userName + "已绑定其他用户组");
        } else {
            inspectorNameCache.put(userName, teamName);
        }
    }

    private void initDocumentation(String documentation, CellStyle style, Sheet sheet, Row row) {
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));
        Cell first = initCell(row, 0, documentation, style);
        row.setHeightInPoints(sheet.getDefaultRowHeightInPoints() * (first.getStringCellValue().split("\n").length));
    }

    Cell initCell(Row row, int columnIndex, String value, CellStyle cellStyle) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(value);
        cell.setCellStyle(cellStyle);
        return cell;
    }

    void writeHeaders(CellStyle style, Workbook workbook, Sheet sheet, Row row) {
        LinkedHashMap<String, CellStyle> headerName = new LinkedHashMap<>();
        headerName.put(INSPECT_INDEX, style);
        headerName.put(INSPECT_TEAM_NAME, style);
        headerName.put(INSPECT_TEAM_TYPE, style);
        headerName.put(DUTY_OFFICER, style);
        headerName.put(DUTY_STAFF, style);
        String[] sparePartsnodeValidation = new String[]{InspectionTeamType.INSPECT_TEAM.getValue(), InspectionTeamType.REPAIR_TEAM.getValue()};
        ExcelValidationUtils.addValidationData(workbook, "班组类型", sheet, sparePartsnodeValidation, 2, 2);
        //生成表头
        PoiExcelUtils.createHeaderName(row, headerName);
    }
}





