# Fix SignInStatusDef import statements
Write-Host "Fixing SignInStatusDef import statements..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Define the import replacement for SignInStatusDef
$signInStatusDefReplacement = @{
    "import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInStatusDef;" = "import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInStatusDef;"
}

function Update-SignInStatusDefImport {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $updated = $false
    
    foreach ($oldImport in $signInStatusDefReplacement.Keys) {
        $newImport = $signInStatusDefReplacement[$oldImport]
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), $newImport
            $updated = $true
            Write-Host "  - Updated: $oldImport" -ForegroundColor Yellow
            Write-Host "    -> $newImport" -ForegroundColor Green
        }
    }
    
    if ($updated) {
        Set-Content -Path $filePath -Value $content -Encoding UTF8
        return $true
    }
    
    return $false
}

# List of files that have SignInStatusDef imports
$statusDefFiles = @(
    "ResetSignStatusRecord.java",
    "UpdateSignPointStatus.java",
    "SignInStatisticsTableMobileServiceImpl.java",
    "SignInStatusRecordServiceImpl.java"
)

Write-Host "Processing SignInStatusDef files..." -ForegroundColor Cyan
$updatedFiles = 0

foreach ($fileName in $statusDefFiles) {
    Write-Host "Looking for file: $fileName" -ForegroundColor White
    
    # Find the file in the directory structure
    $foundFiles = Get-ChildItem -Path $coreSourcePath -Filter $fileName -Recurse
    
    foreach ($file in $foundFiles) {
        Write-Host "Processing: $($file.FullName)" -ForegroundColor White
        
        if (Update-SignInStatusDefImport -filePath $file.FullName) {
            $updatedFiles++
            Write-Host "Updated: $($file.Name)" -ForegroundColor Green
        }
    }
}

Write-Host "`nSignInStatusDef fix completed!" -ForegroundColor Green
Write-Host "Total updated files: $updatedFiles" -ForegroundColor Cyan

# Final comprehensive check for any remaining old SignIn imports
Write-Host "`nFinal comprehensive check for all SignIn related imports..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$remainingIssues = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    if ($content -match "import com\.cet\.eem\.bll\.common\.model\..*SignIn") {
        Write-Host "Still has old SignIn import: $($file.Name)" -ForegroundColor Red
        $remainingIssues++
        
        # Show the specific imports
        $lines = Get-Content $file.FullName
        for ($i = 0; $i -lt $lines.Length; $i++) {
            if ($lines[$i] -match "import com\.cet\.eem\.bll\.common\.model\..*SignIn") {
                Write-Host "  Line $($i+1): $($lines[$i])" -ForegroundColor Yellow
            }
        }
    }
}

if ($remainingIssues -eq 0) {
    Write-Host "SUCCESS: All SignIn related imports have been updated correctly!" -ForegroundColor Green
} else {
    Write-Host "WARNING: Still have $remainingIssues files with old SignIn imports" -ForegroundColor Red
}

Write-Host "`nScript execution completed!" -ForegroundColor Green
