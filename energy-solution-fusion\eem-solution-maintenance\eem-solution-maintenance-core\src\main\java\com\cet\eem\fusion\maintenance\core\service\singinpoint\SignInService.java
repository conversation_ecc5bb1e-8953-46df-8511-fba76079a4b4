package com.cet.eem.fusion.maintenance.core.service.singinpoint;

import cn.hutool.json.JSONObject;
import com.cet.eem.fusion.common.model.Node;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInGroup;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInPoint;
import com.cet.eem.fusion.maintenance.core.model.SignInGroupWithSubLayer;
import com.cet.eem.fusion.maintenance.core.model.SignInPointWithSubLayer;
import com.cet.eem.fusion.maintenance.core.model.sign.*;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface SignInService {

    /**
     * 查询当前项目签到组和签到点信息
     *
     * @return
     */
    List<SignInGroupWithSubLayer> queryCurrentProjectSignInGroup();

    /**
     * 查询签到点下的设备
     * //TODO:JSONObject待优化
     *
     * @return
     */
    List<JSONObject> querySignInEquipmentInPoint(Long signInPointId);

    /**
     * 创建签到组
     *
     * @param createSignInGroupRequest
     * @return
     */
    SignInGroup createSignInGroup(CreateSignInGroupRequest createSignInGroupRequest);

    /**
     * 编辑签到组名
     *
     * @param editSignInGroupRequeste
     * @return
     */
    SignInGroup editSignInGroup(EditSignInGroupRequest editSignInGroupRequeste);

    /**
     * 删除签到组
     *
     * @param ids
     */
    void deleteSignInGroup(Collection<Long> ids);

    /**
     * 创建签到点
     *
     * @param createSignInGroupRequest
     * @return
     */
    Map<String, Object> createSignInPoint(CreateSignInPointRequest createSignInGroupRequest);

    void createSignInPointBatch(List<CreateSignInPointRequest> createSignInPointRequests,Long projectId);
    /**
     * 编辑签到点
     *
     * @param editSignInGroupRequeste
     * @return
     */
    SignInPoint editSignInPoint(EditSignInPointRequest editSignInGroupRequeste);

    /**
     * 删除签到组下的签到点
     *
     * @param signInGroupId
     * @param ids
     */
    void deleteSignInPoint(Long signInGroupId, Collection<Long> ids);

    /**
     * 签到点排序
     *
     * @return
     */
    List<SignInPoint> sortSignInPoint(Long signInGroupId, List<SortSignInPointRequest> sortSignInPointRequestList);

    /**
     * 删除某个签到点下的签到设备
     *
     * @param signInPointId
     * @param baseEntities
     * @return
     */
    List<Map<String, Object>> deleteSignInEquipment(Long signInPointId, List<EntityWithName> baseEntities);

    /**
     * 查询签到组下所有的签到设备树节点返回
     *
     * @param signInGroupId
     * @return
     */
    List<Map<String, Object>> querySignInEquipmentInGroup(Long signInGroupId, EemQueryCondition condition);

    /**
     * 根据签掉点分组和节点，查询所属的签到点信息
     *
     * @param signInPointGroupId
     * @param node
     * @return
     */
    SignInPointWithSubLayer querySignInPoint(@NotNull Long signInPointGroupId, BaseVo node);

    /**
     * 根据项目id查询签到点信息
     *
     * @return
     */
    List<BaseVo> querySignInPointByProjectId(String name);

    /**
     * 给签到点分组关联图形
     *
     * @param signGroupId
     * @param node
     */
    void relateGraph(Long signGroupId, Node node);

    /**
     * 下载签到点二维码
     *
     * @param signPointId 签到点id
     * @param response    响应体
     * @throws IOException
     */
    void downloadSignPointQrCode(Long signPointId, HttpServletResponse response) throws IOException;

    /**
     * 根据签到点分组导出二维码
     *
     * @param signGroupId 签到点分组
     * @param response    响应体
     */
    void downloadSignGroupQrCode(Long signGroupId, HttpServletResponse response);

    /**
     * 根据项目导出二维码
     *
     * @param projectId 项目id
     * @param response  响应体
     */
    void downProjectLoadQrCode(Long projectId, HttpServletResponse response);

}



