package com.cet.eem.fusion.maintenance.core.model.workorder.inspection;

import com.cet.eem.fusion.maintenance.core.common.model.params.ReviewTaskParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 工单审核
 *
 * <AUTHOR>
 * @date 2021/5/7
 */
@Getter
@Setter
@ApiModel(description = "工单审核")
public class WorkOrderReviewVo {
    @ApiModelProperty("工单编号")
    private String code;

    @ApiModelProperty("参数")
    @NotNull(message = "审核参数不允许为空！")
    private ReviewTaskParams params;
}

