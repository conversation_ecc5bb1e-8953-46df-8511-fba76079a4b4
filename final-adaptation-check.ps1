# Final Fusion Adaptation Completeness Check
Write-Host "=== Final Fusion Adaptation Completeness Check ===" -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Count total Java files
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$totalFiles = $javaFiles.Count

Write-Host "`nFile Statistics:" -ForegroundColor Cyan
Write-Host "  Total Java files: $totalFiles" -ForegroundColor White

# Comprehensive checks
$checks = @{
    "Old Result imports" = @{
        Pattern = "import.*\.Result;"
        Expected = 0
        Description = "Old Result import statements should be removed"
    }
    "Old ResultWithTotal imports" = @{
        Pattern = "import.*\.ResultWithTotal;"
        Expected = 0
        Description = "Old ResultWithTotal import statements should be removed"
    }
    "Result.ok() calls" = @{
        Pattern = "\bResult\.ok\("
        Expected = 0
        Description = "Result.ok() calls should be replaced with ApiResult.ok()"
    }
    "ResultWithTotal.ok() calls" = @{
        Pattern = "\bResultWithTotal\.ok\("
        Expected = 0
        Description = "ResultWithTotal.ok() calls should be replaced with ApiResult.ok()"
    }
    "ApiResult imports" = @{
        Pattern = "import.*ApiResult;"
        Expected = "positive"
        Description = "Files should have ApiResult imports where needed"
    }
    "Fusion common imports" = @{
        Pattern = "import com\.cet\.eem\.fusion\.common\."
        Expected = "positive"
        Description = "Files should use fusion framework common imports"
    }
    "Old TimeUtil imports" = @{
        Pattern = "import com\.cet\.eem\.common\.utils\.TimeUtil;"
        Expected = 0
        Description = "Old TimeUtil imports should be updated"
    }
    "New TimeUtil imports" = @{
        Pattern = "import com\.cet\.electric\.baseconfig\.sdk\.common\.utils\.TimeUtil;"
        Expected = "positive"
        Description = "New TimeUtil imports should be used"
    }
    "Old auth imports" = @{
        Pattern = "import com\.cet\.eem\.auth\.aspect\.OperationPermission;"
        Expected = 0
        Description = "Old auth imports should be updated"
    }
    "New auth imports" = @{
        Pattern = "import com\.cet\.electric\.matterhorn\.cloud\.authservice\.sdk\.common\.annotation\.OperationPermission;"
        Expected = "positive"
        Description = "New auth imports should be used"
    }
    "getTenantId() calls" = @{
        Pattern = "getTenantId\(\)"
        Expected = "positive"
        Description = "Should use getTenantId() instead of getProjectId()"
    }
    "getProjectId() calls" = @{
        Pattern = "getProjectId\(\)"
        Expected = "few"
        Description = "Most getProjectId() calls should be replaced with getTenantId()"
    }
}

$issuesFound = 0
$totalIssues = 0

Write-Host "`nRunning comprehensive checks:" -ForegroundColor Cyan

foreach ($checkName in $checks.Keys) {
    $check = $checks[$checkName]
    $pattern = $check.Pattern
    $expected = $check.Expected
    $description = $check.Description
    
    $matchCount = 0
    $matchingFiles = @()
    
    foreach ($file in $javaFiles) {
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        if ($content -match $pattern) {
            $matchCount++
            $matchingFiles += $file.Name
        }
    }
    
    $status = "OK"
    $color = "Green"

    if ($expected -eq 0 -and $matchCount -gt 0) {
        $status = "ERROR"
        $color = "Red"
        $issuesFound++
        $totalIssues += $matchCount
    } elseif ($expected -eq "positive" -and $matchCount -eq 0) {
        $status = "WARN"
        $color = "Yellow"
    } elseif ($expected -eq "few" -and $matchCount -gt 10) {
        $status = "WARN"
        $color = "Yellow"
    }
    
    Write-Host "  $status $checkName`: $matchCount files" -ForegroundColor $color
    
    if ($matchCount -gt 0 -and ($expected -eq 0 -or $expected -eq "few")) {
        Write-Host "    Files: $($matchingFiles -join ', ')" -ForegroundColor Gray
    }
}

Write-Host "`nAdaptation Summary:" -ForegroundColor Yellow
if ($issuesFound -eq 0) {
    Write-Host "  SUCCESS: All fusion framework adaptations completed successfully!" -ForegroundColor Green
    Write-Host "  Total files processed: $totalFiles" -ForegroundColor Green
    Write-Host "  No remaining issues found" -ForegroundColor Green
    Write-Host "  Ready for compilation and testing" -ForegroundColor Cyan
} else {
    Write-Host "  WARNING: Found $issuesFound types of issues affecting $totalIssues instances" -ForegroundColor Red
    Write-Host "  Please review and fix the issues above" -ForegroundColor Yellow
}

Write-Host "`n=== Check Complete ===" -ForegroundColor Green
