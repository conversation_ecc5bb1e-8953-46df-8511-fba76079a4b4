package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : SignInPoint
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 11:02
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.REGISTRATION_POINT)
public class SignInPoint extends EntityWithName {

    /**
     * 签到点时间间隔
     */
    private Integer interval;

    /**
     * 签到点地址
     */
    private String address;

    /**
     * 签到点nfc
     */
    private String nfc;

    /**
     * 签到点图片存放路径
     */
    private String image;

    public SignInPoint() {
        this.modelLabel = ModelLabelDef.REGISTRATION_POINT;
    }
}
