package com.cet.eem.fusion.maintenance.core.model.devicemanage.component;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.DeviceComponent;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.model.BaseVo;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.el.lang.ELArithmetic.isNumber;

/**
 * @ClassName : DeviceComponentListener
 * @Description : 文件形式导入零件
 * <AUTHOR> jiangzixuan
 * @Date: 2021-05-17 14:42
 */
public class DeviceComponentListener extends AnalysisEventListener<DeviceComponentImport> {
    List<DeviceComponent> deviceComponentList;

    public DeviceComponentListener(List<DeviceComponent> deviceComponentList) {
        this.deviceComponentList = deviceComponentList;
    }

    Map<Integer, String> errorMsgMap = new HashMap<>();

    @Override
    public void invoke(DeviceComponentImport deviceComponentImport, AnalysisContext analysisContext) {
        int index = 0;
        DeviceComponent d = new DeviceComponent();
        deviceComponentList.add(d);
        if (StringUtils.isEmpty(deviceComponentImport.getBrand())) {
            errorMsgMap.put(index++, "导入的数据厂家不能为空！");
        } else {
            d.setBrand(deviceComponentImport.getBrand());
        }
        if (StringUtils.isEmpty(deviceComponentImport.getModel())) {
            errorMsgMap.put(index++, "导入的数据规格不能为空！");
        } else {
            d.setModel(deviceComponentImport.getModel());
        }
        if (StringUtils.isEmpty(deviceComponentImport.getUnit())) {
            errorMsgMap.put(index++, "导入的数据单位不能为空！");
        } else {
            d.setUnit(deviceComponentImport.getUnit());
        }
        if (null == deviceComponentImport.getNumber() || !isNumber(deviceComponentImport.getNumber())) {
            errorMsgMap.put(index++, "导入的数据零件数量不能为空！");
        } else {
            d.setNumber(deviceComponentImport.getNumber());
        }
        if (StringUtils.isEmpty(deviceComponentImport.getDevice())) {
            errorMsgMap.put(index++, "导入的数据选择的节点信息不能为空！");
        } else {
            BaseVo baseVo = CommonUtils.getDeviceNameAndIdAndModelLabel(deviceComponentImport.getDevice());
            if (null == baseVo) {
                errorMsgMap.put(index++, "选择节点信息不符合要求!");
            }else {
                d.setObjectLabel(baseVo.getModelLabel());
                d.setObjectId(baseVo.getId());
            }
        }
        if (StringUtils.isEmpty(deviceComponentImport.getName())) {
            errorMsgMap.put(index, "导入的数据零件名称不能为空！");
        } else {
            d.setName(deviceComponentImport.getName());
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (errorMsgMap.size() <= 0) {
            return;
        }
        if (errorMsgMap.size() > 0) {
            throw new ValidationException(errorMsgMap.toString());
        }
    }
}

