package com.cet.eem.fusion.maintenance.core.model.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 工单数量统计
 *
 * <AUTHOR>
 * @date 4/12/2021
 */
@Getter
@Setter
@ApiModel(description = "根据工单类型统计数量")
public class WOCountByTaskTypeVo {
    @ApiModelProperty("工单状态")
    private Integer taskType;

    @ApiModelProperty("工单状态名称")
    private String taskTypeName;

    @ApiModelProperty("数量")
    private Integer count;

    public WOCountByTaskTypeVo() {
    }

    public WOCountByTaskTypeVo(Integer workOrderStatus, String workOrderStatusName) {
        this.taskType = workOrderStatus;
        this.taskTypeName = workOrderStatusName;
    }

    public WOCountByTaskTypeVo(Integer workOrderStatus, String workOrderStatusName, Integer count) {
        this.taskType = workOrderStatus;
        this.taskTypeName = workOrderStatusName;
        this.count = count;
    }
}

