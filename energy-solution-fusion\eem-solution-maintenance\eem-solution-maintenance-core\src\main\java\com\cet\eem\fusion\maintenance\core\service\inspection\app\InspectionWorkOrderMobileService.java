package com.cet.eem.fusion.maintenance.core.service.inspection.app;

import com.cet.eem.fusion.maintenance.core.model.wo.MobileWorkOrderQueryVO;
import com.cet.eem.fusion.maintenance.core.model.workorder.WoStatusCountDTO;
import com.cet.eem.fusion.maintenance.core.model.workorder.app.InspectionWorkOrderDetailDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.app.SignPointWithWorkOrder;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/20
 */
public interface InspectionWorkOrderMobileService {
    /**
     * 查询巡检工单首页详情
     *
     * @return
     */
    InspectionWorkOrderDetailDto queryWorkOrderDetail(Page page);

    /**
     * 根据工单状态查询工单数量
     *
     * @return
     */
    List<WoStatusCountDTO> queryWoStatusCount(Long userId);

    List<InspectionWorkOrderDto> queryWorkOrderList(MobileWorkOrderQueryVO query);

    List<SignPointWithWorkOrder> querySignPoint(Long userId);
}


