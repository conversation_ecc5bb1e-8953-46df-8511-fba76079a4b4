package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import lombok.*;

@Getter
@Setter
public class SparePartsReplaceRecordVo extends SparePartsReplaceRecord {
    /**
     * 备件名称
     */
    private String sparePartsName;
    /**
     * 备件单位
     */
    private String unit;
    /**
     * 备件型号
     */
    private String model;
    /**
     * 备件厂家
     */
    private String brand;
    /**
     * 系统名称
     */
    private String deviceSystemName;
    /**
     * 设备名称
     */
    private String objectName;
    /**
     * 设备类型在
     *枚举类中对应的id
     */
    private Long objectLabelId;
    /**
     * 工单编号
     */
    private String code;
    /**
     * 设备类型对应的描述
     */
    private String objectLabelText;
    public SparePartsReplaceRecordVo(SparePartsReplaceRecord sparePartsReplaceRecord) {

    }

    public SparePartsReplaceRecordVo() {

    }
}
