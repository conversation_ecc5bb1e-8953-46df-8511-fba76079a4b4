# Fix ApiResult issues
Write-Host "Fixing ApiResult issues..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Find all Java files with ApiApiResult issues
$javaFiles = Get-ChildItem -Path $coreSourcePath -Recurse -Filter "*.java"

$fixedCount = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    # Fix ApiApiResult to ApiResult
    $content = $content -replace "ApiApiResult", "ApiResult"
    
    # Fix duplicate Api prefixes
    $content = $content -replace "ApiApiResult", "ApiResult"
    $content = $content -replace "Api\.ApiResult", "ApiResult"
    
    if ($content -ne $originalContent) {
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
        $fixedCount++
        Write-Host "Fixed ApiResult issues in: $($file.Name)" -ForegroundColor Yellow
    }
}

Write-Host "Fixed ApiResult issues in $fixedCount files" -ForegroundColor Green
