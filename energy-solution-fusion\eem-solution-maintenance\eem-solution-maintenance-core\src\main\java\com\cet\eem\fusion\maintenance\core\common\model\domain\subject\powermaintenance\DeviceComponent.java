package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@ModelLabel(ModelLabelDef.DEVICE_COMPONENT)
public class DeviceComponent extends EntityWithName {

    private String brand;

    private String model;

    private String unit;
    @JsonProperty("projectid")
    private Long projectId;
    private Double number;
    @JsonProperty("objectid")
    private Long objectId;
    @JsonProperty("objectlabel")
    private String objectLabel;

    public DeviceComponent() {
        this.modelLabel = ModelLabelDef.DEVICE_COMPONENT;
    }

    public DeviceComponent(String name, String model, String brand, String unit, Double number) {
        this.name = name;
        this.model = model;
        this.brand = brand;
        this.unit = unit;
        this.number = number;
        this.modelLabel = ModelLabelDef.DEVICE_COMPONENT;
    }
    public DeviceComponent(String name, String model, String brand, String unit, Double number,String objectLabel,Long objectId) {
        this.name = name;
        this.model = model;
        this.brand = brand;
        this.unit = unit;
        this.number = number;
        this.objectId=objectId;
        this.objectLabel=objectLabel;
        this.modelLabel = ModelLabelDef.DEVICE_COMPONENT;
    }
    public DeviceComponent(String name, String model, String brand, String unit, Double number,String objectLabel,Long objectId,Long id) {
        this.name = name;
        this.model = model;
        this.brand = brand;
        this.unit = unit;
        this.number = number;
        this.objectId=objectId;
        this.objectLabel=objectLabel;
        this.id=id;
        this.modelLabel = ModelLabelDef.DEVICE_COMPONENT;
    }
}
