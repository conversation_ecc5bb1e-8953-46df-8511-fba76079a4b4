package com.cet.eem.solution.common.feign.impl;

import com.cet.eem.solution.common.feign.FlinkRedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> (2025/3/21 16:50)
 */
@Service
public class FlinkRedisServiceImpl implements FlinkRedisService {
    private final RedisTemplate<String, Object> redisTemplate;
    private HashOperations<String, String, Object> hashOperations;

    @Autowired
    public FlinkRedisServiceImpl( RedisTemplate<String, Object> flinkRedisTemplate) {
        this.redisTemplate = flinkRedisTemplate;
        this.hashOperations = redisTemplate.opsForHash();
    }

    @Override
    public Map<String, Object> getHashSet(String key) {
        return hashOperations.entries(key);
    }

    public void hset(String key, String hashKey, Object value) {
        hashOperations.put(key, hashKey, value);
    }

    public Object hget(String key, String hashKey) {
        return hashOperations.get(key, hashKey);
    }
}
