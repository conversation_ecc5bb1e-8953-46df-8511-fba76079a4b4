package com.cet.eem.solution.common.def.common.label;

/**
 * <AUTHOR>
 * @description 数据库名称列定义
 */
public class TableColumnNameDef {
    //使用前查看com.cet.eem.fusion.common.def.common.ColumnDef;是否存在列名
    private TableColumnNameDef() {
        throw new IllegalStateException("TableColumnNameDef class");
    }

    public static final String LOGTIME = "logtime";

    public static final String PUMP_MODEL_NAME = "泵";
    public static final String GASFURNACE_MODEL_NAME = "加热炉";
    public static final String WELL_MODEL_NAME = "油井";
    public static final String OILWELL_ID = "oilwell_id";
    public static final String WELL_NUMBER = "wellnumber";
    public static final String GASWELL_ID = "gaswell_id";
    public static final String OILWELLID = "oilwellid";
    public static final String WELL_TYPE = "welltype";
    public static final String ROOM_TYPE = "roomtype";

    public static final String DEVICEID = "deviceid";
    public static final String WELL_ID = "well_id";
    /**
     * 表
     */
    public static final String MODEL = "_model";
    public static final String ID = "_id";
    public static final String MODEL_LABEL = "modelLabel";
    public static final String OBJECT_LABEL = "objectlabel";


    public static final String COLUME_MONITORED_ID = "monitoredid";
    public static final String COLUME_MONITORED_LABEL = "monitoredlabel";
    /**
     * 列
     */
    //计量方式
    public static final String COLUMN_MEASUREMENT_MODE = "measurementmode";
    //采暖方式
    public static final String COLUMN_HEATING_MODE = "heatingmode";
    //输液量设计规模
    public static final String COLUMN_DESIGNLIQUID_TRANSMISSION = "designliquidtransmission";
    //处理量设计规模
    public static final String COLUMN_DESIGNLIQUID_PROCESS = "designliquidprocess";
    //地理位置
    public static final String COLUMN_GEOGRAPHICAL_POSITION = "geographicalposition";
    //装机负荷
    public static final String COLUNM_INSTALLEDLOAD = "installedload";
    //热负荷
    public static final String COLUMN_HEATLOAD = "heatload";
    //热负荷
    public static final String COLUMN_THERMAL_LOAD = "thermalload";
    //运行状态
    public static final String COLUMN_OPERATING_STATUS = "operatingstatus";
    //状态
    public static final String COLUMN_STATUS = "status";
    //投运日期
    public static final String COLUMN_OPERATION_DATE = "operationdate";

    public static final String COLUMN_OBJECTID = "objectid";
    //投运日期
    public static final String COLUMN_USAGE_DATE = "usagedate";
    //报废日期
    public static final String COLUMN_SCRAP_DATE = "scrapdate";
    //所属区块
    public static final String COLUMN_SUBORDINATEBLOCK = "subordinateblock";
    //管辖区块
    public static final String COLUMN_WELL_NUMBER = "wellnumber";
    //燃气类型
    public static final String COLUMN_GAS_TYPE = "gastype";
    //油品
    public static final String COLUMN_OIL_QUALITY = "oilquality";
    //工艺流程
    public static final String COLUMN_TECHNOLOGICAL_PROCESS = "technologicalprocess";
    //输液量设计规模
    public static final String COLUMN_DESIGNIN_FUSIONCAPACITY = "designinfusioncapacity";
    //处理量设计规模
    public static final String COLUMN_DESIGN_PROCESS_CAPACITY = "designprocesscapacity";
    //注水水质种类(种)
    public static final String COLUMN_WATER_INJECTION_QUALITY = "waterinjectionquality";
    //设计注水压力
    public static final String COLUMN_DESIGN_WATER_INJECTION_PRESSURE = "designwaterinjectionpressure";
    //资产编号
    public static final String COLUMN_ASSET_NUMBER = "assetnumber";
    //采出方式
    public static final String COLUMN_MINING_METHOD = "miningmethod";
    //井号编码
    public static final String COLUMN_WELLCODE = "wellcode";
    //从式井组
    public static final String COLUMN_CLUSTER_WELL = "clusterwell";
    //所属平台
    public static final String COLUMN_PLATFORM = "platform";
    //所属人工岛
    public static final String COLUMN_ARTIFICIAL_ISLAND = "artificialisland";
    //所属计量站（阀组）
    public static final String COLUMN_METERINGSTATION = "meteringstation";
    //保温方式
    public static final String COLUMN_INSULATION_MODE = "insulationmode";
    //伴热方式
    public static final String COLUMN_HEATTRACING_MODE = "heattracingmode";
    //集油工艺流程
    public static final String COLUMN_OIL_GATHERING_PROCESS = "oilgatheringprocess";
    //是否数字化
    public static final String COLUMN_ISDIGITAL = "isdigital";
    //是否删除
    public static final String IS_DELETED = "isdeleted";
    //渗透性
    public static final String COLUMN_PERMEABILITY = "permeability";
    //所属转接站
    public static final String COLUMN_TRANSFERSTATION = "transferstation";
    //生产厂家
    public static final String COLUMN_MANUFACTURER = "manufacturer";
    //电机厂家
    public static final String COLUMN_MOTORMANU_FACTURER = "motormanufacturer";
    //电机型号
    public static final String COLUNM_MOTORTYPE = "motortype";
    //效率限定值
    public static final String CLOUMN_EFFLIMIT = "efflimit";
    //额定冲次
    public static final String COLUMN_RATEDBLANKING_TIMES = "ratedblankingtimes";
    //额定冲程
    public static final String COLUMN_RATED_STROKE = "ratedstroke";
    //井号编码
    public static final String COLUMN_OILWELL_CODE = "oilwellcode";
    //能效节能评价值
    public static final String COLUMN_EFFEVALUATION = "effevaluation";
    //柔性参数
    public static final String COLUMN_SOFTPARAM = "softparam";
    //注气时刻
    public static final String COLUMN_INJECTPERIOD = "injectperiod";
    //功图表字段
    public static final String DYNAMOMETERCARD_TYPE = "dynamometercardtype";
    //下游站
    public static final String COLUMN_DOWNSTREAMSTATION = "downstreamstation";
    //集气站类型
    public static final String COLUMN_STATIONTYPE = "stationtype";
    //采气时段
    public static final String COLUMN_RECOVERY_PERIOD = "recoveryperiod";
    //电机厂家
    public static final String COLUMN_ELECTRICALMACHINERYMANU_FACTURER = "electricalmachinerymanufacturer";
    //冷却方式
    public static final String COLUMN_COOLING_MODE = "coolingmode";
    //润滑方式
    public static final String COLUMN_LUBRICATION_MODE = "lubricationmode";
    //是否报废
    public static final String COLUMN_ISSCRAP = "isscrap";
    //是否报废
    public static final String COLUMN_ISSCRAPPED = "isscrapped";
    //更换日期
    public static final String COLUMN_CHANGEDATE = "changedate";
    //设备型号
    public static final String COLUMN_EQUIPMENT_TYPE = "equipmenttype";
    //生产日期
    public static final String COLUMN_MANUFACTURE_DATE = "manufacturedate";
    //额定扬程
    public static final String COLUMN_RATED_LIFT = "ratedlift";
    //额定转数
    public static final String COLUMN_RATED_SPEED = "ratedspeed";
    //额定排气压力
    public static final String COLUMN_RATED_DISCHARGE_PRESSURE = "rateddischargepressure";
    //额定功率
    public static final String COLUMN_RATED_POWER = "ratedpower";
    //额定燃气流量
    public static final String COLUMN_RATED_FUEL_FLOW = "ratedfuelflow";
    //编号
    public static final String COLUMN_NUMBER = "number";
    public static final String COLUMN_PROCESS_TYPE = "processtype";
    public static final String COLUMN_ENERGY_CONSUMP = "energyconsump";
    public static final String COLUMN_LOG_TIME = "logtime";
    public static final String COLUMN_LIQUID_PRODUCTION = "liquidproduction";
    public static final String COLUMN_OIL_PRODUCTION = "oilproduction";
    public static final String COLUMN_WATER_PRODUCTION = "waterproduction";
    public static final String COLUMN_WELL_ID = "well_id";
    public static final String COLUMN_WATER_UNIT_ID = "waterunit_id";
    public static final String COLUMN_GASFURNACE_ID = "gasfurnace_id";
    public static final String COLUMN_ID = "id";
    public static final String COLUMN_CHILDREN = "children";
    public static final String COLUMN_KPI_CODE = "kpicode";
    public static final String COLUMN_NAME = "name";
    public static final String COLUMN_MEASUREMENT_NAME = "measurementname";
    public static final String COLUMN_CODE = "code";
    public static final String COLUMN_VALUE = "value";
    public static final String COLUMN_UNIT = "unit";
    public static final String COLUMN_BALANCE = "balance";
    public static final String COLUMN_OIL_VISCOSITY = "oilviscosity";
    public static final String COLUMN_STROKETIMES = "stroketimes";
    public static final String COLUMN_SUBMERGENCE = "submergence";
    public static final String COLUMN_OIL_PRESSURE = "oilpressure";
    public static final String COLUMN_AGGREGATION_CYCLE = "aggregationcycle";
    public static final String COLUMN_AGGREGATION_TYPE = "aggregationtype";
    public static final String COLUMN_STAMDARD_VALUE = "standardvalue";
    public static final String COLUMN_CREATE_TIME = "createtime";
    public static final String COLUMN_GATHERING_UNIT_ID = "gatheringunit_id";
    public static final String COLUMN_PUMP_ID = "pump_id";
    //报警方案表字段
    public static final String ALARM_TYPE = "alarmtype";
    public static final String COLUMN_MECHAN_PRODUCEFFIC = "mechanproduceffic";
    public static final String COLUMN_PUMPINGTONN_CONSUMP = "pumpingtonnhmconsump";
    public static final String COLUMN_UNIT_LIQUID_CONSUMP = "unitliquidconsump";
    public static final String COLUMN_UNIT_OIL_CONSUMP = "unitoilconsump";
    public static final String COLUMN_UNIT_WATER_CONSUMP = "unitwaterconsump";
    public static final String COLUMN_UNIT_PRESSURE_CONSUMP = "unitpressureconsump";
    public static final String COLUMN_COMPREHENSIVE_CONSUMP = "comprehensiveconsump";
    public static final String COLUMN_EFFIC = "effic";
    public static final String COLUMN_UNITPROCESSCONSUMP = "unitprocessconsump";
    public static final String COLUMN_KPI = "kpi";
    public static final String COLUMN_GAS_CONSUMP = "gasconsump";
    public static final String COLUMN_OPERATIONAREA_ID = "operationarea_id";
    public static final String COLUMN_OILFIELD = "oilfield";
    public static final String COLUMN_OILFIELD_ID = "oilfield_id";
    public static final String COLUMN_OIL_FACTORY_ID = "oilfactory_id";
    public static final String COLUME_MONITOR_ID = "monitorid";
    public static final String COLUME_MONITOR_LABEL = "monitorlabel";
    public static final String COLUME_MANAGEMENT_NUMBER = "managementnumber";
    public static final String COLUMN_IS_OVER_PEAK = "isoverpeak";
    public static final String COLUMN_IS_BUSINESS_INDEX = "isbusinessindex";
    public static final String COLUMN_WELLID = "wellid";
    public static final String COLUMN_IS_FINISH = "isfinish";
    public static final String WELL_WASHING_PLAN_ID = "wellwashingplanid";
    public static final String PLAN_RINSE_TIME = "planrinsetime";

    //使用状态
    public static final String COLUME_USAGE_STATE = "usagestate";
    public static final String ENERGY_TYPE = "energytype";
    public static final String PRODUCT_TYPE = "producttype";
    public static final String IDENTIFICATION = "identification";
    public static final String COLUMN_PIPETYPE = "pipetype";
    public static final String DOCKING_TYPE = "dockingtype";
    public static final String COLUMN_COMBINEDSTATION_ID = "combinedstation_id";
    public static final String COLUMN_DEHYDRATINGSTATION_ID = "dehydratingstation_id";
    public static final String COLUMN_GASCOMPRESSOR_ID = "gascompressor_id";
    public static final String COLUMN_GASCOMPRESSOR_TYPE = "compressortype";
    public static final String COLUMN_RATEDFLOW = "ratedflow";
    public static final String COLUMN_PRINCIPLETYPE = "principletype";
    public static final String COLUMN_GASGATHERINGSTATION_ID = "gasgatheringstation_id";
    public static final String COLUNM_GASOPERATIONAREA_ID = "gasoperationarea_id";
    public static final String COLUMN_GASPLATFORM_ID = "gasplatform_id";
    public static final String COLUMN_HEATINGFURNACE_ID = "heatingfurnace_id";
    public static final String COLUMN_MECHANICALMININGMACHINE_ID = "mechanicalminingmachine_id";
    public static final String COLUMN_OILTRANSFERSTATION_ID = "oiltransferstation_id";
    public static final String COLUMN_PLATFORM_ID = "platform_id";
    public static final String COLUMN_PURIFICATIONPLANT_ID = "purificationplant_id";
    public static final String COLUMN_WATERINJECTIONSTATION_ID = "waterinjectionstation_id";
    //额定处理量
    public static final String COLUMN_RATEDPROCESSCA_PACITY = "ratedprocesscapacity";
    //额定热负荷
    public static final String COLUMN_RATED_HEAT_LOAD = "ratedheatload";
    //换热盘管数
    public static final String COLUMN_HEAT_EXCHANGE_COIL_NUMBER = "heatexchangecoilnumber";
    //燃烧器厂家
    public static final String COLUMN_BURNERMANU_FACTURER = "burnermanufacturer";
    //燃烧器型号
    public static final String COLUMN_BURNER_MODEL = "burnermodel";
    //燃烧器功率
    public static final String COLUMN_RATEDBURNER_POWER = "ratedburnerpower";
    //燃料类型
    public static final String COLUMN_FUEL_TYPE = "fueltype";
    //安全保护类型
    public static final String COLUMN_SAFETY_PROTECTION_TYPE = "safetyprotectiontype";
    //设计温度
    public static final String COLUMN_DESIGN_TEMPERATURE = "designtemperature";
    public static final String COLUMN_MATERIAL_TYPE = "materialtype";
    public static final String COLUMN_COMPOSITION = "composition";
    public static final String COLUMN_PROPORTION = "proportion";
    public static final String COLUMN_AGGLABEL = "agglabel";
    public static final String COLUMN_TYPE = "type";
    public static final String COLUMN_PROJECTID = "projectid";
    public static final String PROJECT_ID = "projectId";
    public static final String COLUMN_PROJECTUNITCLASSIFY = "projectunitclassify";
    //泵径
    public static final String COLUMN_PUMPDIAMETER = "pumpdiameter";
    //抽油机类型
    public static final String COLUMN_PUMPINGUNITTYPE = "pumpingunittype";
    //泵深
    public static final String COLUMN_PUMPDEPTH = "pumpdepth";
    //杆柱结构
    public static final String COLUMN_RODSTRUCTRUE = "rodstructure";
    //泵类型
    public static final String COLUMN_MACHINE_PUMP_TYPE = "machinepumptype";
    //设备类型
    public static final String COLUMN_FUNCTIONTYPE = "functiontype";
    //泵类型
    public static final String COLUMN_PHYSICALTYPE = "physicaltype";
    //额定流量
    public static final String COLUMN_RATEDDISCHARGE = "rateddischarge";
    //额定工作压力
    public static final String COLUMN_RATEDWORKINGPRESSURE = "ratedworkingpressure";
    //电机额定功率
    public static final String COLUMN_RATEDMOTORPOWER = "ratedmotorpower";
    //电机额定电流
    public static final String COLUMN_RATED_MOTORCURRENT = "ratedmotorcurrent";
    //电机额定功率因数
    public static final String COLUMN_RATEDMOTORPOWER_FACTOR = "ratedmotorpowerfactor";
    //电机额定效率
    public static final String COLUMN_RATED_MOTOREFFICIENCY = "ratedmotorefficiency";
    //工作介质类型
    public static final String COLUMN_OPERATIONMEDIUMTYPE = "operationmediumtype";
    //生效时间
    public static final String EFFECTIVE_TIME = "effectivetime";
    //平均平衡度
    public static final String AVG_BALANCE = "avgbalance";
    public static final String NODE_ID = "nodeid";
    public static final String NODE_LABEL = "nodelabel";
    //天然气压缩机ID
    public static final String COLUM_GAS_COMPRESSOR_PARAM_ID = "gascompressorparam_id";
    //一级天然气压缩因子
    public static final String COLUM_FIRST_GRADE_GAS_COMPRESSION_FACTOR = "firstgradegascompressionfactor";
    //二级天然气压缩因子
    public static final String COLUM_SECOND_GRADE_GAS_COMPRESSION_FACTOR = "secondgradecompresssionfactor";
    //天然气气体常数
    public static final String COLUM_GAS_CONSTANT = "gasconstant";
    //燃气低位发热量
    public static final String COLUM_LOW_CALORIFIC_VALUE_OF_GAS = "lowcalorificvalueofgas";

    //固体未完全燃烧热损失
    public static final String COLUM_SOLID_HEATLOSS = "solidheatloss";
    //散热损失
    public static final String COLUM_HEATLOSS = "heatloss";
    //灰渣物理热损失
    public static final String COLUM_ASH_HEATLOSS = "ashheatloss";
    //燃料收到基低位发热量
    public static final String COLUM_BASE_LOW_CALORIFICATION = "baselowcalorification";
    //燃料用量
    public static final String COLUM_FUEL_CALORIFIC = "fuelcalorific";

    /**
     * 指标 code
     */
    //单位油田油气生产综合能耗
    public static final String COLUMN_UNIT_OILFIELD_OIL_GAS_PROD_CONSUMPTION = "unitoilfieldoilgasprodconsumption";
    //机采系统平均效率
    public static final String COLUMN_MECHANICAL_SYSTEM_AVG_EFFICIENCY = "mechanicalsystemavgefficiency";
    //平均产液单耗
    public static final String COLUMN_AVG_UNIT_LIQUID_PROD_CONSUMPTION = "avgunitliquidprodconsumption";
    //单位液量集输综合能耗
    public static final String COLUMN_UNIT_LIQUID_GATHER_TRANS_CONSUMPTION = "unitliquidgathertransconsumption";
    //单位含油污水处理综合能耗
    public static final String COLUMN_UNIT_SEWAGE_TREATMENT_ENERGY_CONSUMP = "unitsewagetreatmentenergyconsump";
    //注水单耗
    public static final String COLUMN_WATER_INJECTION_UNIT_CONSUMP = "waterinjectionunitconsump";
    //机采系统效率
    public static final String COLUMN_MECHAN_PRODUC_EFFIC = "mechanproduceffic";
    //吨液百米单耗
    public static final String COLUMN_PUMP_INGTONNHM_CONSUMP = "pumpingtonnhmconsump";
    //单位气田生产综合能耗
    public static final String COLUMN_UNIT_GAS_FIELD_PROD_CONSUMPTION = "unitgasfieldprodconsumption";
    //电动机驱动压缩机组效率
    public static final String COLUMN_ELECTRIC_SET_EFFIC = "electricseteffic";
    //电动机驱动压缩机组单耗
    public static final String COLUMN_ELECTRIC_SET_CONSUMP = "electricsetconsump";
    //单位注气量综合能耗
    public static final String COLUMN_UNIT_GAS_INJECTION_ENERGY_CONSUMPTION = "unitgasinjectionenergyconsumption";
    //燃气驱动压缩机机组效率
    public static final String COLUMN_FUEL_GAS_SET_EFFIC = "fuelgasseteffic";
    //燃气驱动压缩机机组单耗
    public static final String COLUMN_FUEL_GAS_SET_CONSUNMP = "fuelgassetconsunmp";
    //单位天然气集输综合能耗
    public static final String COLUMN_UNIT_NATURAL_GAS_GATHERING_ENERGY_CONSUMPTION = "unitnaturalgasgatheringenergyconsumption";
    //单位注水量电耗
    public static final String COLUMN_UNIT_WATER_INJECTION_POWER_CONSUMPTION = "unitwaterinjectionpowerconsumption";
    //单位天然气净化处理综合能耗
    public static final String COLUMN_UNIT_PURIFIED_GAS_OVER_ALL_CONSUMP = "unitpurifiedgasoverallconsump";
    //单位液量集输电耗
    public static final String COLUMN_UNIT_LIQUID_VOLUME_POWER_CONSUMPTION = "unitliquidvolumepowerconsumption";
    //单位处理电耗
    public static final String COLUMN_UNIT_DISPOSE_POWER_CONSUMPTION = "unitdisposepowerconsumption";
    //uuid
    public static final String COLUMN_UUID = "uuid";

    // 批次号
    public static final String BATCH_NUMBER = "batchnumber";
    // 批次id
    public static final String BATCHID = "batchid";
    //流程id
    public static final String PROCESSID = "processid";
    //工序类型id
    public static final String PROCEDURE_TYPE_ID = "proceduretypeid";
    //产品大类id
    public static final String PRODUCT_CATEGORY_ID = "productcategoryid";
    //工序阈值方案id
    public static final String PROCEDURE_THRESHOLD_SCHEME_ID = "procedurethresholdschemeid";
    //最终产品类型
    public static final String FINAL_PRODUCT_TYPE = "finalproducttype";
    public static final String BATCH_ATTRIBUTE_ENUM = "batchattributeenum";
    //批次外键
    public static final String COLUMN_BATCH_BASE_CLASS_ID = "batchbaseclassid";
    // 自定义属性code
    public static final String COLUMN_ATTRIBUTE_CODE = "attributecode";
    //方案id
    public static final String COLUMN_SCHEME_ID = "schemeid";
    // 分组序号
    public static final String COLUMN_GROUP_COUNT = "groupcount";
    //群聊id
    public static final String COLUMN_CHAT_ID = "chatid";
    //群聊code
    public static final String COLUMN_CHAT_CODE = "chatcode";
    //群聊类型
    public static final String COLUMN_CHAT_TYPE = "chattype";
    //群聊是否有效
    public static final String COLUMN_EFFECTIVE = "effective";
    //消息类型
    public static final String COLUMN_CONTENT_TYPE = "contenttype";
    //数据更新时间
    public static final String COLUMN_UPDATE_TIME = "updatetime";
    //核心平台事件类型
    public static final String COLUMN_PECEVENT_TYPE = "peceventtype";
    //事件发生事件
    public static final String COLUMN_EVENT_TIME = "eventtime";
    //事件类型
    public static final String COLUMN_EVENT_TYPE = "eventtype";
    //事件等级
    public static final String COLUMN_LEVEL = "level";
    //设备类型
    public static final String NODE_TYPE = "nodetype";
    //工况类型
    public static final String WORKING_CONDITION_TYPE = "workingconditiontype";
    //机采优化方案id(外键)
    public static final String MODEL_TRAINING_SCHEME_ID = "modeltrainingschemeid";
    //策略描述
    public static final String STRATEGY_DESCRIPTION = "strategydescription";
    //关联设备类型
    public static final String SUPPLY_TO_LABEL = "supplytolabel";
    //关联设备id
    public static final String SUPPLY_TO_ID = "supplytoid";

    public static final String MODEL_GOAL = "modelgoal";
    //检修类型
    public static final String MAINTENANCE_TYPE = "maintenancetype";

    //班组类型
    public static final String CLASS_TEAM_TYPE = "classteamtype";

    public static final String START_TIME = "startTime";

    public static final String END_TIME = "endTime";

    public static final String RATIO = "ratio";

    //通用字段
    public static final String NAMR = "name";
    public static final String AGGREGATIONCYCLE = "aggregationcycle";
    public static final String OBJECTID = "objectid";
    public static final String OBJECTLABEL = "objectlabel";
    public static final String CHILDREN = "children";

    //能耗表字段
    public static final String TIMESHARE_PERIOD_IDENTIFICATION = "timeshareperiod_identification";


    //产液表字段
    public static final String PRODUCTTYPE = "producttype";

    //产液poi字段
    public static final String RECORDID = "recordid";
    public static final String RECORDLABEL = "recordlabel";

    //物理量聚合数据表
    public static final String QUANTITY_OBJECT_ID = "quantityobject_id";

    //机采设备能效数据表
    public static final String MECHANICAL_MINIGMACHINE_ID = "mechanicalminingmachine_id";

    //平台能效数据表

    //报警方案表字段
    public static final String IS_ALARM = "isAlarm";

    //天然气压缩机数据字段
    public static final String GASCOMPRESSORPARAM_ID= "gascompressorparam_id";

    //下端管道关联设备
    public static final String OUTFLOWLABEL = "outflowlabel";

    //上端管道关联设备
    public static final String INFLOWLABEL = "inflowlabel";

    //下端管道关联id
    public static final String INFLOWID = "inflowid";

    //上端管道关联id
    public static final String OUTFLOWID = "outflowid";

    //设备效率节能评价值
    public static final String EFFEVALUATION = "effevaluation";

    //工作原理类型
    public static final String PRINCIPLETYPE = "principletype";


    //额定排气压力
    public static final String RATEDDISCHARGEPRESSURE = "rateddischargepressure";

    //额定功率
    public static final String RATEDPOWER = "ratedpower";

    //压缩机设备类型
    public static final String COMPRESSOR_TYPE = "compressortype";

    //效率限定值
    public static final String EFFLIMIT = "efflimit";

    //功能类型
    public static final String FUNCTIONTYPE = "functiontype";

    //额定流量
    public static final String RATEDFLOW = "ratedflow";

    //额定处理量
    public static final String RATED_PROCESS_CAPACITY = "ratedprocesscapacity";

    //额定热负荷
    public static final String RATEDHEAT_LOAD = "ratedheatload";

    //燃烧器额定功率
    public static final String RATEDBURNER_POWER = "ratedburnerpower";

    //额定工作压力
    public static final String RATED_WORKING_PRESSURE = "ratedworkingpressure";
    //设计温度
    public static final String DESIGNT_EMPERATURE = "designtemperature";

    //设备类型
    public static final String EQUIPMENT_TYPE = "equipmenttype";
    //额定冲程
    public static final String RATED_STROKE ="ratedstroke";

    //电机额定功率
    public static final String RATED_MOTOR_POWER ="ratedmotorpower";

    //电机额定电流
    public static final String RATED_MOTOR_CURRENT ="ratedmotorcurrent";

    //电机额定功率因数
    public static final String RATED_MOTOR_POWER_FACTOR ="ratedmotorpowerfactor";

    //电机额定效率
    public static final String RATED_MOTOR_EFFICIENCY ="ratedmotorefficiency";

    //额定冲次
    public static final String RATED_BLANKING_TIMES ="ratedblankingtimes";

    //泵类型
    public static final String PHYSICAL_TYPE ="physicaltype";

    //额定扬程
    public static final String RATED_LIFT ="ratedlift";

    //额定流量
    public static final String RATEDDIS_CHARGE ="rateddischarge";

    //额定转数
    public static final String RATED_SPEED ="ratedspeed";

    //抽油机类型
    public static final String PUMPINGUNIT_TYPE = "pumpingunittype";

    //杆柱结构
    public static final String RODSTRUCTURE = "rodstructure";

    //井号编码
    public static final String OILWELL_CODE = "oilwellcode";

    //泵类型
    public static final String MACHINEPUMP_TYPE = "machinepumptype";

    //泵径
    public static final String PUMP_DIAMETER= "pumpdiameter";

    //泵深
    public static final String PUMP_DEPTH = "pumpdepth";

    //柔性控制参数
    public static final String SOFT_PARAM = "softparam";
}
