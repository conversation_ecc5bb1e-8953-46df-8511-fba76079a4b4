# Fix remaining import statements based on compilation errors
Write-Host "Fixing remaining import statements..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Function to update imports in files
function Update-Imports {
    param(
        [string]$filePath,
        [hashtable]$importMappings
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    try {
        $content = Get-Content $filePath -Raw -Encoding UTF8
        $updated = $false
        
        foreach ($oldImport in $importMappings.Keys) {
            $newImport = $importMappings[$oldImport]
            if ($content -match [regex]::Escape($oldImport)) {
                $content = $content -replace [regex]::Escape($oldImport), $newImport
                $updated = $true
            }
        }
        
        if ($updated) {
            # Write without BOM
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($filePath, $content, $utf8NoBom)
            Write-Host "  - Updated: $(Split-Path $filePath -Leaf)" -ForegroundColor Green
            return $true
        }
        
        return $false
    }
    catch {
        Write-Host "  - Error updating $(Split-Path $filePath -Leaf): $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Define import mappings based on compilation errors
$importMappings = @{
    # Update old BLL imports to new fusion imports
    "import com.cet.eem.bll.common.model.domain.subject.powermaintenance" = "import com.cet.eem.fusion.common.model.domain.subject.powermaintenance"
    "import com.cet.eem.bll.common.model.domain.subject.activiti" = "import com.cet.eem.fusion.common.model.domain.subject.activiti"
    "import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance" = "import com.cet.eem.fusion.common.model.enumeration.subject.powermaintenance"
    
    # Common model imports
    "import com.cet.eem.common.model.auth.user" = "import com.cet.eem.fusion.common.model.auth.user"
    "import com.cet.eem.common.definition" = "import com.cet.eem.fusion.common.definition"
    "import com.cet.eem.model.base" = "import com.cet.eem.fusion.common.model.base"
    "import com.cet.eem.annotation" = "import com.cet.eem.fusion.common.annotation"
    "import com.cet.eem.model.model" = "import com.cet.eem.fusion.common.model.model"
    "import com.cet.eem.common.exception" = "import com.cet.eem.fusion.common.exception"
    "import com.cet.eem.common.constant" = "import com.cet.eem.fusion.common.constant"
    
    # Auth service imports
    "import com.cet.eem.auth.service" = "import com.cet.eem.fusion.common.service.auth"
    
    # BLL common imports
    "import com.cet.eem.bll.common.log.annotation" = "import com.cet.eem.fusion.common.log.annotation"
    "import com.cet.eem.bll.common.log.constant" = "import com.cet.eem.fusion.common.log.constant"
    "import com.cet.eem.bll.datamaintain.service" = "import com.cet.eem.fusion.common.service.datamaintain"
    "import com.cet.eem.bll.common.model.domain.object.physicalquantity" = "import com.cet.eem.fusion.common.model.domain.object.physicalquantity"
    
    # Toolkit import
    "import com.cet.eem.toolkit" = "import com.cet.eem.fusion.common.toolkit"
}

# Get all Java files in the core module
Write-Host "Scanning Java files for import updates..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Recurse -Filter "*.java"

$updatedCount = 0
foreach ($file in $javaFiles) {
    if (Update-Imports -filePath $file.FullName -importMappings $importMappings) {
        $updatedCount++
    }
}

Write-Host "`nImport update completed!" -ForegroundColor Green
Write-Host "Total files updated: $updatedCount" -ForegroundColor Cyan

# Now create missing workflow classes as stubs
Write-Host "`nCreating missing workflow classes..." -ForegroundColor Cyan

$workflowPath = "$coreSourcePath\com\cet\eem\fusion\common\workflow"
New-Item -ItemType Directory -Path $workflowPath -Force | Out-Null
New-Item -ItemType Directory -Path "$workflowPath\common\model" -Force | Out-Null
New-Item -ItemType Directory -Path "$workflowPath\common\model\node\config" -Force | Out-Null
New-Item -ItemType Directory -Path "$workflowPath\common\model\params" -Force | Out-Null

# Create UserTaskConfig stub
$userTaskConfigContent = @"
package com.cet.electric.workflow.common.model.node.config;

/**
 * User task configuration stub for fusion framework compatibility
 */
public class UserTaskConfig {
    private String taskId;
    private String taskName;
    private String assignee;
    
    // Getters and setters
    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }
    
    public String getTaskName() { return taskName; }
    public void setTaskName(String taskName) { this.taskName = taskName; }
    
    public String getAssignee() { return assignee; }
    public void setAssignee(String assignee) { this.assignee = assignee; }
}
"@

$utf8NoBom = New-Object System.Text.UTF8Encoding $false
[System.IO.File]::WriteAllText("$workflowPath\common\model\node\config\UserTaskConfig.java", $userTaskConfigContent, $utf8NoBom)

# Create ProcessInstanceResponse stub
$processInstanceResponseContent = @"
package com.cet.electric.workflow.common.model;

/**
 * Process instance response stub for fusion framework compatibility
 */
public class ProcessInstanceResponse {
    private String processInstanceId;
    private String processDefinitionId;
    private String businessKey;
    
    // Getters and setters
    public String getProcessInstanceId() { return processInstanceId; }
    public void setProcessInstanceId(String processInstanceId) { this.processInstanceId = processInstanceId; }
    
    public String getProcessDefinitionId() { return processDefinitionId; }
    public void setProcessDefinitionId(String processDefinitionId) { this.processDefinitionId = processDefinitionId; }
    
    public String getBusinessKey() { return businessKey; }
    public void setBusinessKey(String businessKey) { this.businessKey = businessKey; }
}
"@

[System.IO.File]::WriteAllText("$workflowPath\common\model\ProcessInstanceResponse.java", $processInstanceResponseContent, $utf8NoBom)

# Create TableTriggerParams stub
$tableTriggerParamsContent = @"
package com.cet.electric.workflow.common.model.params;

import java.util.Map;

/**
 * Table trigger parameters stub for fusion framework compatibility
 */
public class TableTriggerParams {
    private String tableName;
    private Map<String, Object> variables;
    
    // Getters and setters
    public String getTableName() { return tableName; }
    public void setTableName(String tableName) { this.tableName = tableName; }
    
    public Map<String, Object> getVariables() { return variables; }
    public void setVariables(Map<String, Object> variables) { this.variables = variables; }
}
"@

[System.IO.File]::WriteAllText("$workflowPath\common\model\params\TableTriggerParams.java", $tableTriggerParamsContent, $utf8NoBom)

# Create UserTaskParams stub
$userTaskParamsContent = @"
package com.cet.electric.workflow.common.model.params;

import java.util.Map;

/**
 * User task parameters stub for fusion framework compatibility
 */
public class UserTaskParams {
    private String taskId;
    private String assignee;
    private Map<String, Object> variables;
    
    // Getters and setters
    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }
    
    public String getAssignee() { return assignee; }
    public void setAssignee(String assignee) { this.assignee = assignee; }
    
    public Map<String, Object> getVariables() { return variables; }
    public void setVariables(Map<String, Object> variables) { this.variables = variables; }
}
"@

[System.IO.File]::WriteAllText("$workflowPath\common\model\params\UserTaskParams.java", $userTaskParamsContent, $utf8NoBom)

# Create MultiTableTriggerParams stub
$multiTableTriggerParamsContent = @"
package com.cet.electric.workflow.common.model.params;

import java.util.List;
import java.util.Map;

/**
 * Multi-table trigger parameters stub for fusion framework compatibility
 */
public class MultiTableTriggerParams {
    private List<String> tableNames;
    private Map<String, Object> variables;
    
    // Getters and setters
    public List<String> getTableNames() { return tableNames; }
    public void setTableNames(List<String> tableNames) { this.tableNames = tableNames; }
    
    public Map<String, Object> getVariables() { return variables; }
    public void setVariables(Map<String, Object> variables) { this.variables = variables; }
}
"@

[System.IO.File]::WriteAllText("$workflowPath\common\model\params\MultiTableTriggerParams.java", $multiTableTriggerParamsContent, $utf8NoBom)

Write-Host "Created workflow stub classes" -ForegroundColor Green

Write-Host "`nRemaining import fixes completed!" -ForegroundColor Green
Write-Host "Next step: Run compilation again to check for remaining issues" -ForegroundColor Yellow
