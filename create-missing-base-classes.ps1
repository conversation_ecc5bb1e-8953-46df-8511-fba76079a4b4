# Create missing base classes for fusion framework
Write-Host "Creating missing base classes..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$utf8NoBom = New-Object System.Text.UTF8Encoding $false

# Create fusion common directories
$fusionCommonPath = "$coreSourcePath\com\cet\eem\fusion\common"
New-Item -ItemType Directory -Path "$fusionCommonPath\annotation" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\model\model" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\model\base" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\definition" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\exception" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\constant" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\service\auth" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\log\annotation" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\log\constant" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\service\datamaintain" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\model\domain\object\physicalquantity" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\toolkit" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\model\auth\user" -Force | Out-Null

# Create ModelLabel annotation
$modelLabelContent = @"
package com.cet.eem.fusion.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Model label annotation for fusion framework
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModelLabel {
    String value() default "";
    String description() default "";
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\annotation\ModelLabel.java", $modelLabelContent, $utf8NoBom)

# Create BaseEntity
$baseEntityContent = @"
package com.cet.eem.fusion.common.model.model;

import java.io.Serializable;
import java.util.Date;

/**
 * Base entity class for fusion framework
 */
public abstract class BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    
    protected String id;
    protected Date createTime;
    protected Date updateTime;
    protected String createBy;
    protected String updateBy;
    protected Boolean deleted = false;
    
    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    
    public String getCreateBy() { return createBy; }
    public void setCreateBy(String createBy) { this.createBy = createBy; }
    
    public String getUpdateBy() { return updateBy; }
    public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }
    
    public Boolean getDeleted() { return deleted; }
    public void setDeleted(Boolean deleted) { this.deleted = deleted; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\model\BaseEntity.java", $baseEntityContent, $utf8NoBom)

# Create Order class
$orderContent = @"
package com.cet.eem.fusion.common.model.base;

/**
 * Order class for sorting
 */
public class Order {
    private String property;
    private Direction direction;
    
    public enum Direction {
        ASC, DESC
    }
    
    public Order(String property, Direction direction) {
        this.property = property;
        this.direction = direction;
    }
    
    public static Order asc(String property) {
        return new Order(property, Direction.ASC);
    }
    
    public static Order desc(String property) {
        return new Order(property, Direction.DESC);
    }
    
    // Getters and setters
    public String getProperty() { return property; }
    public void setProperty(String property) { this.property = property; }
    
    public Direction getDirection() { return direction; }
    public void setDirection(Direction direction) { this.direction = direction; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\base\Order.java", $orderContent, $utf8NoBom)

# Create ModelDao annotation
$modelDaoContent = @"
package com.cet.eem.fusion.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Model DAO annotation for fusion framework
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModelDao {
    String value() default "";
    Class<?> entityClass() default Object.class;
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\annotation\ModelDao.java", $modelDaoContent, $utf8NoBom)

# Create AuthUtils
$authUtilsContent = @"
package com.cet.eem.fusion.common.service.auth;

import com.cet.eem.fusion.common.model.auth.user.RoleVo;
import com.cet.eem.fusion.common.model.auth.user.UserGroupVo;
import com.cet.eem.fusion.common.result.ApiResult;

/**
 * Authentication utilities for fusion framework
 */
public class AuthUtils {
    
    public ApiResult<UserGroupVo> queryUserGroupById(String groupId) {
        // Stub implementation
        UserGroupVo userGroup = new UserGroupVo();
        userGroup.setId(groupId);
        return ApiResult.ok(userGroup);
    }
    
    public ApiResult<RoleVo> queryRoleById(String roleId) {
        // Stub implementation
        RoleVo role = new RoleVo();
        role.setId(roleId);
        return ApiResult.ok(role);
    }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\service\auth\AuthUtils.java", $authUtilsContent, $utf8NoBom)

# Create ProcessFlowUnit
$processFlowUnitContent = @"
package com.cet.electric.workflow.common.model;

/**
 * Process flow unit stub for fusion framework compatibility
 */
public class ProcessFlowUnit {
    private String flowId;
    private String flowName;
    private String processDefinitionId;
    
    // Getters and setters
    public String getFlowId() { return flowId; }
    public void setFlowId(String flowId) { this.flowId = flowId; }
    
    public String getFlowName() { return flowName; }
    public void setFlowName(String flowName) { this.flowName = flowName; }
    
    public String getProcessDefinitionId() { return processDefinitionId; }
    public void setProcessDefinitionId(String processDefinitionId) { this.processDefinitionId = processDefinitionId; }
}
"@

$workflowModelPath = "$coreSourcePath\com\cet\electric\workflow\common\model"
New-Item -ItemType Directory -Path $workflowModelPath -Force | Out-Null
[System.IO.File]::WriteAllText("$workflowModelPath\ProcessFlowUnit.java", $processFlowUnitContent, $utf8NoBom)

# Create missing entity classes that are still referenced
$attachmentContent = @"
package com.cet.eem.fusion.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.common.model.model.BaseEntity;

/**
 * Attachment entity for fusion framework
 */
@ModelLabel("附件")
public class Attachment extends BaseEntity {
    private String fileName;
    private String filePath;
    private String fileType;
    private Long fileSize;
    private String businessId;
    private String businessType;
    
    // Getters and setters
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    
    public String getFilePath() { return filePath; }
    public void setFilePath(String filePath) { this.filePath = filePath; }
    
    public String getFileType() { return fileType; }
    public void setFileType(String fileType) { this.fileType = fileType; }
    
    public Long getFileSize() { return fileSize; }
    public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
    
    public String getBusinessId() { return businessId; }
    public void setBusinessId(String businessId) { this.businessId = businessId; }
    
    public String getBusinessType() { return businessType; }
    public void setBusinessType(String businessType) { this.businessType = businessType; }
}
"@

$attachmentPath = "$coreSourcePath\com\cet\eem\fusion\common\model\domain\subject\powermaintenance"
[System.IO.File]::WriteAllText("$attachmentPath\Attachment.java", $attachmentContent, $utf8NoBom)

Write-Host "Created missing base classes successfully!" -ForegroundColor Green
Write-Host "- ModelLabel annotation" -ForegroundColor Cyan
Write-Host "- BaseEntity class" -ForegroundColor Cyan
Write-Host "- Order class" -ForegroundColor Cyan
Write-Host "- ModelDao annotation" -ForegroundColor Cyan
Write-Host "- AuthUtils service" -ForegroundColor Cyan
Write-Host "- ProcessFlowUnit class" -ForegroundColor Cyan
Write-Host "- Attachment entity" -ForegroundColor Cyan
