package com.cet.eem.fusion.maintenance.core.service.repair.app;

import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/3
 */
public interface RepairWorkOrderMobileService {
    /**
     * 查询工单
     *
     * @param user   用户id
     * @param teamId 班组id
     * @param clazz  类信息
     * @return 工单信息
     */
    <T extends InspectionWorkOrderDto> List<T> queryWorkOrder(UserVo user, Long teamId,boolean inspectUser, Class<T> clazz);
}


