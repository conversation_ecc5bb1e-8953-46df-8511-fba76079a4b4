package com.cet.eem.fusion.maintenance.core.model.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 工单数量统计
 *
 * <AUTHOR>
 * @date 4/12/2021
 * @deprecated 使用 {@link WoStatusCountDTO}
 */
@Getter
@Setter
@ApiModel(description = "工单状态数量统计")
@Deprecated
public class WorkOrderCountDto {
    @ApiModelProperty("工单状态")
    private Integer workOrderStatus;

    @ApiModelProperty("工单状态名称")
    private String workOrderStatusName;

    @ApiModelProperty("数量")
    private Integer count;

    public WorkOrderCountDto() {
    }

    public WorkOrderCountDto(Integer workOrderStatus, String workOrderStatusName) {
        this.workOrderStatus = workOrderStatus;
        this.workOrderStatusName = workOrderStatusName;
    }

    public WorkOrderCountDto(Integer workOrderStatus, String workOrderStatusName, Integer count) {
        this.workOrderStatus = workOrderStatus;
        this.workOrderStatusName = workOrderStatusName;
        this.count = count;
    }
}

