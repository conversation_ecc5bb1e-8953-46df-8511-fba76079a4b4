# Fix SignIn related classes import statements
Write-Host "Fixing SignIn related classes import statements..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Define the import replacements for SignIn related classes
$signInImportReplacements = @{
    # SignIn Group related classes
    "import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInGroup;" = "import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInGroup;"
    "import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithSubLayer;" = "import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInGroupWithSubLayer;"
    "import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithAllSubLayer;" = "import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInGroupWithAllSubLayer;"
    "import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithEquipment;" = "import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInGroupWithEquipment;"
    
    # SignIn Point related classes
    "import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInPoint;" = "import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInPoint;"
    "import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInPointWithSubLayer;" = "import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInPointWithSubLayer;"
    "import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInPointWithSupLayer;" = "import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInPointWithSupLayer;"
    
    # SignIn Equipment and other related classes
    "import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInEquipment;" = "import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInEquipment;"
    "import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInPointSequence;" = "import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInPointSequence;"
    "import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInStatisticsTable;" = "import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInStatisticsTable;"
}

function Update-SignInImports {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $updated = $false
    
    foreach ($oldImport in $signInImportReplacements.Keys) {
        $newImport = $signInImportReplacements[$oldImport]
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), $newImport
            $updated = $true
            Write-Host "  - Updated: $oldImport" -ForegroundColor Yellow
            Write-Host "    -> $newImport" -ForegroundColor Green
        }
    }
    
    if ($updated) {
        Set-Content -Path $filePath -Value $content -Encoding UTF8
        return $true
    }
    
    return $false
}

# Process all Java files
Write-Host "Scanning Java files..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$updatedFiles = 0

foreach ($file in $javaFiles) {
    Write-Host "Checking file: $($file.Name)" -ForegroundColor White

    if (Update-SignInImports -filePath $file.FullName) {
        $updatedFiles++
        Write-Host "Updated: $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "`nFix completed!" -ForegroundColor Green
Write-Host "Total updated files: $updatedFiles" -ForegroundColor Cyan

# Check if there are still any old SignIn imports
Write-Host "`nChecking for remaining SignIn related imports..." -ForegroundColor Cyan
$remainingIssues = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    if ($content -match "import com\.cet\.eem\.bll\.common\.model\..*SignIn") {
        Write-Host "Found remaining SignIn import: $($file.Name)" -ForegroundColor Red
        $remainingIssues++
    }
}

if ($remainingIssues -eq 0) {
    Write-Host "All SignIn related imports have been updated correctly!" -ForegroundColor Green
} else {
    Write-Host "Still have $remainingIssues files with old SignIn imports" -ForegroundColor Red
}

Write-Host "`nScript execution completed!" -ForegroundColor Green
