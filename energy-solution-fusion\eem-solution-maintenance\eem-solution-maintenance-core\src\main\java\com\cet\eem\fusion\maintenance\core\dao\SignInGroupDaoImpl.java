﻿package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInGroup;
import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInGroupWithAllSubLayer;
import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInGroupWithEquipment;
import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInGroupWithSubLayer;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * @ClassName : SignInGroupDaoImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 14:09
 */
@Repository
public class SignInGroupDaoImpl extends ModelDaoImpl<SignInGroup> implements SignInGroupDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public SignInGroupWithEquipment querySignInGroupWithEquipment(Long signInGroupId) {
        ParentQueryConditionBuilder<SignInGroup> ParentQueryConditionBuilder = new ParentQueryConditionBuilder<SignInGroup>(ModelLabelDef.REGISTRATION_GROUP, signInGroupId);
        ParentQueryConditionBuilder.selectChildByLabels(Collections.singleton(ModelLabelDef.REGISTRATION_EQUIPMENT));
        ApiResult<List<Map<String, Object>>> query = this.eemModelDataService.query(ParentQueryConditionBuilder.build());
        List<Map<String, Object>> data = query.getData();
        List<SignInGroupWithEquipment> signInGroupWithEquipments = JsonTransferUtils.parseList(data, SignInGroupWithEquipment.class);
        return signInGroupWithEquipments.stream().findFirst().orElse(null);
    }

    @Override
    public List<SignInGroupWithAllSubLayer> querySignInGroupWithAllSubLayer(Collection<Long> signInGroupIds) {
        ParentQueryConditionBuilder<SignInGroup> builder = new ParentQueryConditionBuilder<SignInGroup>(ModelLabelDef.REGISTRATION_GROUP, signInGroupIds)
                .selectChildByLabels(Arrays.asList(ModelLabelDef.REGISTRATION_POINT, ModelLabelDef.REGISTRATION_POINT_SEQUENCE, ModelLabelDef.REGISTRATION_EQUIPMENT))
                .queryAsTree(false);

        return modelServiceUtils.query(builder.build(), SignInGroupWithAllSubLayer.class);
    }

    @Override
    public SignInGroupWithSubLayer querySignInGroupWithSubLayer(Long signGroupId) {
        LambdaQueryWrapper<SignInGroup> wrapper = LambdaQueryWrapper.of(SignInGroup.class)
                .eq(SignInGroup::getId, signGroupId);

        List<SignInGroupWithSubLayer> list = this.selectRelatedList(SignInGroupWithSubLayer.class, wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }

    @Override
    public List<SignInGroupWithSubLayer> querySignInGroupWithSubLayerByProjectId(Long projectId) {
        LambdaQueryWrapper<SignInGroup> wrapper = LambdaQueryWrapper.of(SignInGroup.class)
                .eq(SignInGroup::getProjectId, projectId);

        return this.selectRelatedList(SignInGroupWithSubLayer.class, wrapper);
    }
}



