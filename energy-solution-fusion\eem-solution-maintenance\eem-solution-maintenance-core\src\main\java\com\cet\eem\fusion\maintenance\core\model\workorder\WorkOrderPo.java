package com.cet.eem.fusion.maintenance.core.model.workorder;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderPlanDef;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-5-6
 */
@Getter
@Setter
@ApiModel(value = "工单实体", description = "工单实体")
@ModelLabel(WorkOrderDef.MODEL)
public class WorkOrderPo extends EntityWithName {
    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述", name = "taskDescription", dataType = "string", example = "1")
    @JsonProperty(WorkOrderDef.TASK_DESCRIPTION)
    private String taskDescription;

    /**
     * 工单编号
     */
    private String code;
    /**
     * 操作票ID
     */
    @JsonProperty(WorkOrderDef.OPERATION_ORDER_ID)
    private String operationOrderId;
    /**
     * 危险源分析
     */
    @JsonProperty(WorkOrderDef.HAZARD_ANALYSIS)
    private String hazardAnalysis;
    /**
     * 危险源附件
     */
    @JsonProperty(WorkOrderDef.HAZARD_ATTACHMENT)
    private String hazardAttachment;
    /**
     * 安全措施
     */
    @JsonProperty(WorkOrderDef.SAFETY_MEASURE)
    private String safetyMeasure;
    /**
     * 安全措施附件
     */
    @JsonProperty(WorkOrderDef.SAFETY_MEASURE_ATTACHMENT)
    private String safetyMeasureAttachment;
    /**
     * 参训人员
     */
    private String trainee;
    /**
     * 处理描述
     */
    @JsonProperty(WorkOrderDef.PROCESS_DESCRIPITON)
    private String processDescription;
    /**
     * 审核意见
     */
    private String opinion;
    /**
     * 计划耗时
     */
    @JsonProperty(WorkOrderDef.TIME_CONSUME_PLAN)
    private Long timeConsumePlan;
    /**
     * 创建人ID
     */
    @NotNull
    private Long creator;
    /**
     * 创建时间
     */
    @NotNull
    @JsonProperty(WorkOrderDef.CREATE_TIME)
    private Long createTime;
    /**
     * 指派人员
     */
    private Long staff;
    /**
     * 任务等级
     */
    @Range(min = 1, max = 3)
    @JsonProperty(WorkOrderDef.TASK_LEVEL)
    private Integer taskLevel;
    /**
     * 计划表ID
     */
    @JsonProperty(WorkOrderDef.PLANSHEET_ID)
    private Long planSheetId;

    @JsonProperty(WorkOrderPlanDef.PLAN_NAME)
    private String planName;

    /**
     * 区域ID
     */
    @NotNull
    @JsonProperty(WorkOrderDef.SECTIONAREA_ID)
    private Long sectionAreaId;
    /**
     * 区域名称
     */
    @NotEmpty
    @JsonProperty(WorkOrderDef.SECTIONAREA_NAME)
    private String sectionAreaName;
    /**
     * 项目ID
     */
    @NotNull
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
    /**
     * 项目名称
     */
    @NotEmpty
    @JsonProperty(WorkOrderDef.PROJECT_NAME)
    private String projectName;
    /**
     * 项目位置
     */
    @NotEmpty
    @JsonProperty(WorkOrderDef.ROOM_LOCATION)
    private String roomLocation;

    @JsonProperty(WorkOrderDef.ROOM_NAME)
    private String roomName;

    @JsonProperty(WorkOrderDef.ROOM_ID)
    private Long roomId;

    /**
     * 工单类型
     */
    @JsonProperty(WorkOrderDef.TASK_TYPE)
    private Integer taskType;
    /**
     * 任务内容
     */
    @JsonProperty(WorkOrderDef.TASK_CONTENT)
    private String taskContent;

    /**
     * 执行时间
     */
    @JsonProperty(WorkOrderDef.EXECUTE_TIME)
    private Long executeTime;
    /**
     * 完成时间
     */
    @JsonProperty(WorkOrderDef.FINISH_TIME)
    private Long finishTime;
    /**
     * 实际耗时
     */
    @JsonProperty(WorkOrderDef.TIME_CONSUME)
    private Long timeConsume;
    /**
     * 流程定义ID
     */
    @JsonProperty(WorkOrderDef.PROCESS_DEFINITION_ID)
    private String processDefinitionId;
    /**
     * 流程实例ID
     */
    @JsonProperty(WorkOrderDef.PROCESS_INSTANCE_ID)
    private String processInstanceId;
    /**
     * 流程定义KEY
     */
    @JsonProperty(WorkOrderDef.PROCESS_DEFINITION_KEY)
    private String processDefinitionKey;
    /**
     * 工单状态
     */
    @Range(min = 1, max = 3)
    @JsonProperty(WorkOrderDef.WORKSHEET_STATUS)
    private Integer workSheetStatus;
    /**
     * 工单附件
     */
    private String attachment;
    /**
     * 创建人姓名
     */
    @NotEmpty
    @JsonProperty(WorkOrderDef.CREATOR_NAME)
    private String creatorName;

    /**
     * 巡检
     */
    @JsonProperty(WorkOrderDef.MAINTENANCE_CONTENT)
    private String maintenanceContent;

    /**
     * 执行人员
     */
    @JsonProperty(WorkOrderDef.GIVEN_STAFF_NAME)
    private String givenStaffName;

    /**
     * 超时时间
     */
    @JsonProperty(WorkOrderDef.CANCEL_TIME_OUT)
    private Long cancelTimeOut;

    /**
     * 执行人员
     */
    @JsonProperty(WorkOrderDef.ASSIGN_TIME)
    private Long assignTime;

    @JsonProperty(value = WorkOrderDef.TASK_ID)
    private String taskId;

    @JsonProperty(WorkOrderDef.REJECT_ENABLED)
    private Boolean rejectEnabled;

    @ApiModelProperty("计划开始时间")
    @JsonProperty(WorkOrderDef.EXECUTE_TIME_PLAN)
    private Long executeTimePlan;

    @JsonProperty("deviceplanrelationship_model")
    private List<DevicePlanRelationshipPo> deviceplanrelationship_model;

    @ApiModelProperty("责任班组id")
    @JsonProperty(WorkOrderDef.TEAM_ID)
    private Long teamId;

    @ApiModelProperty("巡检方案id")
    @JsonProperty(WorkOrderDef.INSPECTION_SCHEME_ID)
    private Long inspectionSchemeId;

    @ApiModelProperty("故障及处理描述")
    @JsonProperty(WorkOrderDef.FAULT_DESCRIPTION)
    private String faultDescription;

    public WorkOrderPo() {
        this.modelLabel = WorkOrderDef.MODEL;
    }
}


