package com.cet.eem.fusion.maintenance.core.model.workorder.app;

import com.cet.eem.fusion.maintenance.core.model.workorder.maintenance.InputMaintenanceWorkOrderRequest;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : AddMaintenanceWorkOrder
 * @Description : app端录入工单数据
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-21 15:54
 */
@Getter
@Setter
@ApiModel(description = "录入维保工单")
public class AddMaintenanceWorkOrder extends InputMaintenanceWorkOrderRequest {
    /**
     * 执行人
     */
    private String staffName;
}
