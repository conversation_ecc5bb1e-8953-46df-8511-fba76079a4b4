package com.cet.eem.fusion.maintenance.core.model.devicemanage;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.cet.eem.fusion.common.exception.ValidationException;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-06-01
 */
public class TechParamListener extends AnalysisEventListener<Map<Integer, String>> {

    /**
     * 存储当前sheet的变量
     */
    private ReadSheetHolder currentReadSheetHolder = null;
    List<TechParamValue> techParamValues;
    private LinkedList<Long> techIds = new LinkedList<>();

    public TechParamListener(List<TechParamValue> techParamValue) {
        this.techParamValues = techParamValue;
    }

    Map<Integer, String> errorMsgMap = new HashMap<>();

    @Override
    public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
        super.invokeHead(headMap, context);
        headMap.forEach((integer, cellData) -> {
            if (integer > 1) {
                String substring = cellData.getStringValue().substring(cellData.getStringValue().lastIndexOf("（") + 1, cellData.getStringValue().lastIndexOf("）"));
                techIds.add(Long.parseLong(substring));
            }
        });
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        if (currentReadSheetHolder == null) {
            currentReadSheetHolder = context.readSheetHolder();
        }
        int index = 0;
        data.forEach(((integer, s) -> {
            if (null == data.get(0)) {
                errorMsgMap.put(index, "设备名称为空");

            } else {
                String idAndLabel = data.get(0).substring(data.get(0).indexOf("(") + 1, data.get(0).indexOf(")"));
                String[] split = idAndLabel.split("-");
                if (integer == data.size() - 1 && data.size()<3) {
                    for (Long id : techIds) {
                        TechParamValue value = new TechParamValue();
                        value.setObjectLabel(split[split.length - 2]);
                        value.setObjectId(Long.parseLong(split[split.length - 1]));
                        value.setTechParamTemplateId(id);
                        techParamValues.add(value);
                    }
                }
                if (integer > 1) {
                    TechParamValue value = new TechParamValue();
                    value.setObjectLabel(split[split.length - 2]);
                    value.setObjectId(Long.parseLong(split[split.length - 1]));
                    value.setTechParamTemplateId(techIds.get(integer - 2));
                    //入库的字段改为value，字符串类型
                    value.setValue(s);
                    techParamValues.add(value);
                }
            }


        }));
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        techIds.clear();
        currentReadSheetHolder = null;
        if (errorMsgMap.size() <= 0) {
            return;
        }
        if (errorMsgMap.size() > 0) {
            throw new ValidationException(errorMsgMap.toString());
        }
    }
}

