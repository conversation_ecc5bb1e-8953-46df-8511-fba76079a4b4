package com.cet.eem.fusion.maintenance.core.schedule.strategy;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.PlanSheet;
import org.quartz.CalendarIntervalScheduleBuilder;
import org.quartz.CalendarIntervalTrigger;
import org.quartz.ScheduleBuilder;
import org.springframework.stereotype.Component;

/**
 * @ClassName : HalfYearTriggerStrategy
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-26 10:37
 */
@Component(PlanSheetTriggerStrategyKey.HALF_YEAR)
public class HalfYearTriggerStrategy implements PlanSheetTriggerStrategy<CalendarIntervalTrigger> {
    @Override
    public ScheduleBuilder<CalendarIntervalTrigger> buildSchedule(PlanSheet planSheet) {
        return CalendarIntervalScheduleBuilder
                .calendarIntervalSchedule().
                withIntervalInMonths(6)
                .withMisfireHandlingInstructionDoNothing();
    }
}

