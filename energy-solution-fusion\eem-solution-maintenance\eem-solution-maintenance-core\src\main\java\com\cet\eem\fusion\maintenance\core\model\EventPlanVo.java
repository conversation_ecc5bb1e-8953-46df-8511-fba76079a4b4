package com.cet.eem.fusion.maintenance.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/7/14
 */
@Getter
@Setter
@ApiModel(description = "预案信息")
public class EventPlanVo {
    @ApiModelProperty("计划id，如果是新增的预案，那么改为null或者不传")
    private Long eventPlanId;

    @ApiModelProperty("计划名称")
    private String eventPlanName;

    @ApiModelProperty("解决方案")
    private String solution;

    @ApiModelProperty("项目id")
    private Long projectId;
}

