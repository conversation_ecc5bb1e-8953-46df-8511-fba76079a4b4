package com.cet.eem.fusion.maintenance.core.model.plan;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.EnabledDays;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : AddInspectionPlanRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-21 09:32
 */
@Getter
@Setter
@ApiModel(value = "AddInspectionPlanRequest", description = "新增巡检计划")
public class AddInspectionPlanRequest {

    /**
     * 周期
     */
    @JsonProperty("aggregationcycle")
    private Integer aggregationCycle;

    /**
     * 工单提前生成时长
     */
    @JsonProperty("aheadduration")
    private Integer aheadDuration;

    /**
     * 时间表达式
     */
    private String cycle;

    /**
     * 生效日设置
     */
    @JsonProperty("enableddays")
    private EnabledDays enabledDays;

    /**
     * 首次执行时间
     */
    @JsonProperty("executetime")
    private Long executeTime;

    /**
     * 计划结束时间
     */
    @JsonProperty("finishtime")
    private Long finishTime;

    /**
     * 巡检方案id
     */
    @JsonProperty("inspectionschemeid")
    private Long inspectionSchemeId;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 签到组id
     */
    @JsonProperty("signgroupid")
    private Long signGroupId;

    /**
     * 班组id
     */
    @JsonProperty("teamid")
    private Long teamId;

    /**
     * 预计耗时
     */
    @JsonProperty("timeconsumeplan")
    private Long timeConsumePlan;

    /**
     * 设备列表
     */
    @JsonProperty("devicelist")
    private List<DevicePlanRelationship> devicePlanRelationshipList;
}

