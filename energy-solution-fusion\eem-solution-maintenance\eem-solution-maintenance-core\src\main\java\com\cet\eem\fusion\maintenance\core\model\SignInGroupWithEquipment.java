package com.cet.eem.fusion.maintenance.core.model;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInEquipment;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : SignInGroupWithEquipment
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-25 08:53
 */
@Getter
@Setter
public class SignInGroupWithEquipment extends SignInGroup {

    @JsonProperty("registrationequipment_model")
    private List<SignInEquipment> signInEquipmentList;
}
