package com.cet.eem.fusion.maintenance.core.model;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInPointSequence;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12
 */
@Getter
@Setter
public class SignInGroupWithAllSubLayer extends SignInGroup {
    @JsonProperty(ModelLabelDef.REGISTRATION_POINT_SEQUENCE + "_model")
    private List<SignInPointSequence> signInPointSequenceList;

    @JsonProperty(ModelLabelDef.REGISTRATION_POINT + "_model")
    private List<SignInPointWithSubLayer> signInPointWithSubLayerList;

}
