package com.cet.eem.fusion.maintenance.core.controller.bff.maintenance;

import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;
import com.cet.eem.fusion.common.def.OperationAuthDef;
import com.cet.eem.fusion.maintenance.common.log.annotation.OperationLog;
import com.cet.eem.fusion.maintenance.common.log.constant.EnumOperationSubType;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCheckInfoVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderPo;
import com.cet.eem.fusion.maintenance.core.model.workorder.maintenance.MaintenanceWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.WorkOrderBatchReviewVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.WorkOrderReviewVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.maintenance.*;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderServiceCallBackParam;
import com.cet.eem.fusion.maintenance.core.service.maintenance.MaintenanceWorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderService;
import com.cet.electric.commons.ApiResult;

import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.ProcessInstanceResponse;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName : MaintenanceWorkOrderBffController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-27 10:33
 */
public class MaintenanceWorkOrderBffController {

    @Autowired
    private MaintenanceWorkOrderService maintenanceWorkOrderService;
    @Autowired
    private WorkOrderService workOrderService;

    @ApiOperation(value = "查询维保工单")
    @PostMapping("/query")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_BROWSER})
    public ApiResult<List<MaintenanceWorkOrderDto>> queryMaintenanceWorkOrderList(@RequestBody QueryMaintenanceWorkOrderRequest queryMaintenanceWorkOrderRequest) {
        return maintenanceWorkOrderService.queryMaintenanceWorkOrderList(queryMaintenanceWorkOrderRequest);
    }

    /**
     * @deprecated 废弃
     * @param addMaintenanceWorkOrderRequest
     * @return
     */
    @ApiOperation(value = "手动创建维保工单")
    @OperationLog(operationType = OperationLogType.MAINTENANCE_WORK_ORRDER, subType = EnumOperationSubType.ADD, description = "【新增维保工单】")
    @PostMapping("/create")
    @Deprecated
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_CREATE})
    public ApiResult<ProcessInstanceResponse> createWorkOrderManually(@Valid @RequestBody AddMaintenanceWorkOrderRequest addMaintenanceWorkOrderRequest) {
        return Result.ok(maintenanceWorkOrderService.createWorkOrderManually(addMaintenanceWorkOrderRequest));
    }

    @ApiOperation(value = "批量手动创建维保工单")
    @OperationLog(operationType = OperationLogType.MAINTENANCE_WORK_ORRDER, subType = EnumOperationSubType.ADD, description = "【新增维保工单】")
    @PostMapping("/create/batch")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_CREATE})
    public ApiResult<List<ProcessInstanceResponse>> createWorkOrderManuallyBatch(@Valid @RequestBody AddMaintenanceWorkOrderRequest addMaintenanceWorkOrderRequest) {
        return Result.ok(maintenanceWorkOrderService.createWorkOrderManuallyBatch(addMaintenanceWorkOrderRequest));
    }

    @ApiOperation(value = "保存维保工单信息")
    @OperationLog(operationType = OperationLogType.MAINTENANCE_WORK_ORRDER, subType = EnumOperationSubType.UPDATE, description = "【编辑维保工单】")
    @PostMapping("/save")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_MAINTENANCE})
    public ApiResult<Void> saveInputWorkOrder(@RequestBody InputMaintenanceWorkOrderRequest inputMaintenanceWorkOrderRequest) {
        maintenanceWorkOrderService.saveInputWorkOrder(inputMaintenanceWorkOrderRequest);
        return Result.ok();
    }

    @ApiOperation(value = "提交维保工单信息")
    @OperationLog(operationType = OperationLogType.MAINTENANCE_WORK_ORRDER, subType = EnumOperationSubType.UPDATE, description = "【编辑维保工单】")
    @PostMapping("/submit")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_MAINTENANCE})
    public ApiResult<Void> submitInputWorkOrder(@RequestBody InputMaintenanceWorkOrderRequest inputMaintenanceWorkOrderRequest) {
        maintenanceWorkOrderService.submitInputWorkOrder(inputMaintenanceWorkOrderRequest);
        return Result.ok();
    }

    @ApiOperation(value = "根据工单号查询工单详情")
    @GetMapping("/detailByCode")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_BROWSER})
    public ApiResult<MaintenanceWorkOrderDto> queryMaintenanceWorkOrderByCode(@RequestParam String code) {
        return Result.ok(maintenanceWorkOrderService.queryMaintenanceWorkOrderByCode(code));
    }

    @ApiOperation(value = "查询工单统计信息")
    @PostMapping("/count")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_BROWSER})
    public ApiResult<List<WorkOrderCountDto>> queryMaintenanceWorkOrderCount(@RequestBody QueryMaintenanceWorkOrderCountRequest queryMaintenanceWorkOrderCountRequest) {
        return Result.ok(maintenanceWorkOrderService.queryMaintenanceWorkOrderCount(queryMaintenanceWorkOrderCountRequest));
    }

    @ApiOperation(value = "审核工单")
    @OperationLog(operationType = OperationLogType.MAINTENANCE_WORK_ORRDER, subType = EnumOperationSubType.UPDATE, description = "【编辑维保工单】")
    @PostMapping("/workOrder/check")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_MONITOR_CHECK, OperationAuthDef.MAINTENANCE_WORK_ORDER_TECHNICIAN_CHECK}, andOr = EnumAndOr.OR)
    public ApiResult<List<WorkOrderPo>> reviewForm(
            @RequestBody WorkOrderReviewVo workOrderReviewVo) {
        workOrderService.reviewForm(workOrderReviewVo);
        return Result.ok();
    }

    @ApiOperation(value = "批量审核工单")
    @OperationLog(operationType = OperationLogType.MAINTENANCE_WORK_ORRDER, subType = EnumOperationSubType.UPDATE, description = "【编辑维保工单】")
    @PostMapping("/workOrder/check/batch")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_MONITOR_CHECK, OperationAuthDef.MAINTENANCE_WORK_ORDER_TECHNICIAN_CHECK}, andOr = EnumAndOr.OR)
    public ApiResult<List<WorkOrderPo>> reviewForm(
            @RequestBody WorkOrderBatchReviewVo workOrderReviewVo) {
        workOrderService.reviewFormBatch(workOrderReviewVo);
        return Result.ok();
    }

    @ApiOperation(value = "暂存审核信息")
    @PostMapping("/workOrder/check/stash")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_MONITOR_CHECK, OperationAuthDef.MAINTENANCE_WORK_ORDER_TECHNICIAN_CHECK}, andOr = EnumAndOr.OR)
    public ApiResult<List<WorkOrderPo>> saveReviewForm(
            @RequestBody WorkOrderReviewVo workOrderReviewVo) {
        workOrderService.saveReviewForm(workOrderReviewVo);
        return Result.ok();
    }

    @ApiOperation(value = "查询暂存的审核信息")
    @GetMapping("/workOrder/check/stash/{code}")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_MONITOR_CHECK, OperationAuthDef.MAINTENANCE_WORK_ORDER_TECHNICIAN_CHECK}, andOr = EnumAndOr.OR)
    public ApiResult<WorkOrderCheckInfoVo> checkOrder(
            @PathVariable String code) {
        WorkOrderCheckInfoVo workOrderCheckInfoVo = workOrderService.queryWorkOrderCheckInfo(code);
        return Result.ok(workOrderCheckInfoVo);
    }

    @ApiOperation(value = "获取流程状态图")
    @GetMapping("/workOrder/processDiagram/{code}")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_BROWSER})
    public ApiResult<Object> updateWorkOrder(@PathVariable String code, @RequestParam(required = false) Boolean isLightStyle) throws Exception {
        workOrderService.getProcessDiagram(code, isLightStyle);
        return null;
    }

    @ApiOperation(value = "工单导出")
    @PostMapping("/export")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_BROWSER})
    public ApiResult<Object> exportWorkOrder(@RequestBody QueryMaintenanceWorkOrderRequest queryMaintenanceWorkOrderRequest, HttpServletResponse response) throws Exception {
        maintenanceWorkOrderService.exportWorkOrder(queryMaintenanceWorkOrderRequest, response);
        return null;
    }

    @ApiOperation(value = "根据工单写入备件记录")
    @PostMapping("/sparePartsReplaceRecordByMaintenanceWorkOrder")
    public ApiResult<Object> insertSparePartsReplaceRecord(@RequestBody @ApiParam(name = "param", value = "回调参数", required = true) WorkOrderServiceCallBackParam param) {
         maintenanceWorkOrderService.insertSparePartsReplaceRecord(param);
        return Result.ok();
    }
}




