package com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : QueryTemplate
 * @Description : 巡检记录表查询模板
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-10 11:21
 */
@Getter
@Setter
@ModelLabel(RecordSheetModelLabel.QUERY_TEMPLATE)
public class QueryTemplate extends EntityWithName {
    private TemplateDetail detail;
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
    public QueryTemplate(){
        this.modelLabel=RecordSheetModelLabel.QUERY_TEMPLATE;
    }
}

