package com.cet.eem.fusion.maintenance.core.service.device;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SparePartsReplaceRecord;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SparePartsReplaceRecordVo;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;

/**
 * 备件更换记录
 *
 * <AUTHOR>
 * @date 2021/6/9
 */
public interface SparePartsReplaceRecordService {
    /**
     * 写入备件更换记录数据
     *
     * @param data        记录数据
     * @param workOrderId 工单id
     */
    void writeSparePartsReplaceRecord(List<? extends SparePartsReplaceRecord> data, Long workOrderId);

    /**
     * 查询备件更换记录
     *
     * @param wordOrderIds 工单id
     * @return 备件更换记录
     */
    List<SparePartsReplaceRecordVo> querySparePartsReplaceRecordByWorkOrders(@NotNull Collection<Long> wordOrderIds);
}

