package com.cet.eem.fusion.maintenance.core.schedule.event;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : CreateInspectionOrderCommand
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-25 21:39
 */
@Getter
@Setter
public class CreateOrderCommand {

    /**
     * 巡检计划id
     */
    private Long id;

    /**
     * 下次执行时间
     */
    private Long nextFireTime;

    /**
     * 计划执行时间
     */
    private Long scheduledFireTime;

    /**
     * 实际执行时间
     */
    private Long fireTime;

}

