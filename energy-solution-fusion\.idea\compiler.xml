<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="eem-solution-demo-core" />
        <module name="eem-solution-common" />
        <module name="eem-solution-demo-service" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="eem-solution-maintenance" target="1.8" />
      <module name="eem-solution-maintenance-core" target="1.8" />
      <module name="eem-solution-maintenance-service" target="1.8" />
      <module name="eem-solution-production-expansion" target="1.8" />
      <module name="eem-solution-production-expansion-core" target="1.8" />
      <module name="eem-solution-production-expansion-service" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="eem-solution-common" options="-parameters" />
      <module name="eem-solution-demo" options="-parameters" />
      <module name="eem-solution-demo-core" options="-parameters" />
      <module name="eem-solution-demo-service" options="-parameters" />
      <module name="eem-solution-maintenance" options="-parameters" />
      <module name="eem-solution-maintenance-core" options="-parameters" />
      <module name="eem-solution-maintenance-service" options="-parameters" />
      <module name="eem-solution-production-expansion" options="-parameters" />
      <module name="eem-solution-production-expansion-core" options="-parameters" />
      <module name="eem-solution-production-expansion-service" options="-parameters" />
    </option>
  </component>
</project>