package com.cet.eem.fusion.maintenance.core.service.inspection.impl;

import com.cet.eem.fusion.maintenance.core.entity.po.InspectionParameter;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetailVo;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.InspectionSchemeWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.InspectionParameterDao;
import com.cet.eem.fusion.maintenance.core.dao.InspectionSchemeDao;
import com.cet.eem.fusion.maintenance.core.def.ParameterTypeDef;
import com.cet.eem.fusion.maintenance.core.def.WorkSheetStatusDef;
import com.cet.eem.fusion.maintenance.core.model.param.AddInspectionParameterRequest;
import com.cet.eem.fusion.maintenance.core.model.param.QueryInspectionParameterByDevice;
import com.cet.eem.fusion.maintenance.core.model.param.QueryInspectionParameterRequest;
import com.cet.eem.fusion.maintenance.core.model.param.UpdateInspectionParameterRequest;
import com.cet.eem.fusion.maintenance.core.model.scheme.QueryInspectionDetailResult;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionParameterService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionWorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorSchemeService;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : InspectionParameterServiceImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 08:48
 */
@Service
public class InspectionParameterServiceImpl implements InspectionParameterService {


    @Autowired
    private InspectionSchemeDao inspectionSchemeDao;

    @Autowired
    private InspectionParameterDao inspectionParameterDao;

    @Autowired
    private InspectionWorkOrderService inspectionWorkOrderService;
    @Value("${cet.eem.event.inspection.export-max-size: 10000}")
    private int exportMaxCount;
    @Autowired
    private InspectorSchemeService inspectorSchemeService;
    public static final Integer ANALOG_QUANTITY = 2;

    @Override
    public InspectionParameter addInspectionParameter(AddInspectionParameterRequest addInspectionParameterRequest) {
        checkNameRepeatWhileCreate(addInspectionParameterRequest.getName());
        InspectionParameter inspectionParameter = new InspectionParameter();
        inspectionParameter.setName(addInspectionParameterRequest.getName());
        inspectionParameter.setProjectId(GlobalInfoUtils.getTenantId());
        inspectionParameter.setType(addInspectionParameterRequest.getType());
        inspectionParameterDao.insert(inspectionParameter);
        return inspectionParameter;
    }

    @Override
    public ApiResult<List<InspectionParameter>> queryAllInspectionParameter(QueryInspectionParameterRequest queryInspectionParameterRequest) {
        LambdaQueryWrapper<InspectionParameter> queryWrapper = LambdaQueryWrapper.of(InspectionParameter.class);
        queryWrapper.eq(InspectionParameter::getProjectId, GlobalInfoUtils.getTenantId());
        if (StringUtils.isNotEmpty(queryInspectionParameterRequest.getName())) {
            queryWrapper.like(InspectionParameter::getName, queryInspectionParameterRequest.getName());
        }
        if (Objects.nonNull(queryInspectionParameterRequest.getType())) {
            queryWrapper.eq(InspectionParameter::getType, queryInspectionParameterRequest.getType());
        }
        ApiResult<List<InspectionParameter>> listResultWithTotal = inspectionParameterDao.selectPage(queryWrapper, queryInspectionParameterRequest.getPage());
        List<InspectionParameter> collect = listResultWithTotal.getData().stream().sorted(Comparator.comparing(InspectionParameter::getId).reversed()).collect(Collectors.toList());
        listResultWithTotal.setData(collect);
        return listResultWithTotal;
    }

    @Override
    public void deleteInspectionParameter(List<Long> ids) {
        checkParameterUserByScheme(ids);
        inspectionParameterDao.deleteBatchIds(ids);
    }

    private void checkParameterUserByScheme(Collection<Long> inspectionParameterIds) {
        LambdaQueryWrapper<InspectionSchemeDetail> queryWrapper = LambdaQueryWrapper.of(InspectionSchemeDetail.class);
        queryWrapper.in(InspectionSchemeDetail::getInspectionParameterId, inspectionParameterIds);
        List<InspectionSchemeWithSubLayer> inspectionSchemeWithSubLayers = inspectionSchemeDao.selectRelatedList(InspectionSchemeWithSubLayer.class, null, Collections.singletonList(queryWrapper));
        if (CollectionUtils.isEmpty(inspectionSchemeWithSubLayers)) {
            return;
        }
        for (InspectionSchemeWithSubLayer inspectionSchemeWithSubLayer : inspectionSchemeWithSubLayers) {
            List<InspectionSchemeDetail> schemeDetails = inspectionSchemeWithSubLayer.getSchemeDetails();
            if (CollectionUtils.isEmpty(schemeDetails)) {
                continue;
            }
            Optional<InspectionSchemeDetail> any = schemeDetails.stream().filter(s -> inspectionParameterIds.contains(s.getInspectionParameterId())).findAny();
            if (any.isPresent()) {
                InspectionSchemeDetail inspectionSchemeDetail = any.get();
                InspectionParameter inspectionParameter = inspectionParameterDao.selectById(inspectionSchemeDetail.getInspectionParameterId());
                throw new IllegalArgumentException(inspectionParameter.getName() + "已被" + inspectionSchemeWithSubLayer.getName() + "使用");
            }
        }
    }

    @Override
    public InspectionParameter updateInspectionParameter(UpdateInspectionParameterRequest updateInspectionParameterRequest) {
        checkNameRepeatWhileUpdate(updateInspectionParameterRequest.getId(), updateInspectionParameterRequest.getName());
        checkTypeChange(updateInspectionParameterRequest);

        InspectionParameter inspectionParameter = inspectionParameterDao.selectById(updateInspectionParameterRequest.getId());
        BeanUtils.copyProperties(updateInspectionParameterRequest, inspectionParameter);
        inspectionParameterDao.updateById(inspectionParameter);
        return inspectionParameter;
    }

    @Override
    public List<InspectionSchemeDetailVo> queryInspectionParameterByDevice(QueryInspectionParameterByDevice queryInspectionParameterByDevice) {
        List<InspectionWorkOrderDto> inspectionWorkOrderDtos = queryWorkOrderByTime(queryInspectionParameterByDevice);
        List<InspectionWorkOrderDto> collect = inspectionWorkOrderDtos.stream().filter(inspectionWorkOrderDto -> Objects.equals(inspectionWorkOrderDto.getInspectionSchemeId(), queryInspectionParameterByDevice.getSchemeId())).collect(Collectors.toList());
        List<InspectionSchemeDetailVo> result = new ArrayList<>();
        addFinishTime(result, collect, queryInspectionParameterByDevice);
        //返回跟时间排序的值
        return result.stream().sorted(Comparator.comparing(InspectionSchemeDetailVo::getFinishTime)).collect(Collectors.toList());

    }

    private List<InspectionWorkOrderDto> queryWorkOrderByTime(QueryInspectionParameterByDevice queryInspectionParameterByDevice) {
        InspectionSearchDto dto = new InspectionSearchDto();
        BeanUtils.copyProperties(queryInspectionParameterByDevice, dto);
        dto.setWorkSheetStatus(WorkSheetStatusDef.ACCOMPLISHED);
        dto.setPage(new Page(0, exportMaxCount));
        ApiResult<List<InspectionWorkOrderDto>> listResultWithTotal = inspectionWorkOrderService.queryFinshWorkOrderList(dto);
        List<InspectionWorkOrderDto> data = listResultWithTotal.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        return data.stream().filter(inspectionWorkOrderDto -> CollectionUtils.isNotEmpty(inspectionWorkOrderDto.getDevicePlanRelationshipList())).filter(inspectionWorkOrderDto -> Objects.equals(inspectionWorkOrderDto.getDevicePlanRelationshipList().get(0).getDeviceId(), queryInspectionParameterByDevice.getObjectId())).filter(inspectionWorkOrderDto -> Objects.equals(inspectionWorkOrderDto.getDevicePlanRelationshipList().get(0).getDeviceLabel(), queryInspectionParameterByDevice.getObjectLabel())).collect(Collectors.toList());

    }

    /**
     * 根据设备和时间信息筛选巡检方案
     *
     * @param queryInspectionParameterByDevice
     * @return
     */
    @Override
    public List<InspectionScheme> queryInspectionSchemeByDevice(QueryInspectionParameterByDevice queryInspectionParameterByDevice) {
        List<InspectionWorkOrderDto> inspectionWorkOrderDtos = queryWorkOrderByTime(queryInspectionParameterByDevice);
        Set<InspectionScheme> collect = inspectionWorkOrderDtos.stream().map(inspectionWorkOrderDto -> new InspectionScheme(inspectionWorkOrderDto.getInspectionSchemeId(), inspectionWorkOrderDto.getInspectionSchemeName())).collect(Collectors.toSet());
        //返回值按巡检方案id排序
        return collect.stream().sorted(Comparator.comparing(InspectionScheme::getId).reversed()).collect(Collectors.toList());

    }

    /**
     * 根据巡检方案和巡检参数类型查询巡检参数列表
     *
     * @param inspectionSchemeId
     * @param inspectionParameterType
     * @return
     */
    @Override
    public List<InspectionSchemeDetail> queryDetailByScheme(Long inspectionSchemeId, Integer inspectionParameterType) {
        QueryInspectionDetailResult queryInspectionDetailResult = inspectorSchemeService.queryInspectionSchemeAndDetail(inspectionSchemeId);
        if (Objects.isNull(queryInspectionDetailResult)) {
            return Collections.emptyList();
        }
        if (Objects.equals(ANALOG_QUANTITY, inspectionParameterType)) {
            return queryInspectionDetailResult.getAnalogQuantity();
        } else if (Objects.equals(ParameterTypeDef.STATUS_QUANTITY, inspectionParameterType)) {
            return queryInspectionDetailResult.getStatusQuantity();
        } else {
            return queryInspectionDetailResult.getTextQuantity();
        }

    }





    private void addFinishTime(List<InspectionSchemeDetailVo> inspectionSchemeDetailVos, List<InspectionWorkOrderDto> dtos, QueryInspectionParameterByDevice queryInspectionParameterByDevice) {
        for (InspectionWorkOrderDto dto : dtos) {
            if (CollectionUtils.isNotEmpty(dto.getInspectionSchemeDetails())) {
                for (InspectionSchemeDetail detail : dto.getInspectionSchemeDetails()) {

                    if (Objects.equals(queryInspectionParameterByDevice.getParameterId(), detail.getInspectionParameterId())) {
                        InspectionSchemeDetailVo inspectionSchemeDetailVo = new InspectionSchemeDetailVo();
                        BeanUtils.copyProperties(detail, inspectionSchemeDetailVo);
                        inspectionSchemeDetailVo.setFinishTime(TimeUtil.localDateTime2timestamp(dto.getFinishTime()));
                        inspectionSchemeDetailVos.add(inspectionSchemeDetailVo);
                    }
                }
            }
        }

    }

    private void checkTypeChange(UpdateInspectionParameterRequest updateInspectionParameterRequest) {
        InspectionParameter inspectionParameter = inspectionParameterDao.selectById(updateInspectionParameterRequest.getId());
        if (inspectionParameter.getType().equals(updateInspectionParameterRequest.getType())) {
            return;
        }
        LambdaQueryWrapper<InspectionSchemeDetail> queryWrapper = LambdaQueryWrapper.of(InspectionSchemeDetail.class);
        queryWrapper.eq(InspectionSchemeDetail::getInspectionParameterId, updateInspectionParameterRequest.getId());
        List<InspectionSchemeWithSubLayer> inspectionSchemeWithSubLayers = inspectionSchemeDao.selectRelatedList(InspectionSchemeWithSubLayer.class, null, Collections.singletonList(queryWrapper));
        if (CollectionUtils.isEmpty(inspectionSchemeWithSubLayers)) {
            return;
        }
        for (InspectionSchemeWithSubLayer inspectionSchemeWithSubLayer : inspectionSchemeWithSubLayers) {
            List<InspectionSchemeDetail> schemeDetails = inspectionSchemeWithSubLayer.getSchemeDetails();
            if (CollectionUtils.isEmpty(schemeDetails)) {
                continue;
            }
            Optional<InspectionSchemeDetail> any = schemeDetails.stream().filter(s -> updateInspectionParameterRequest.getId().equals(s.getInspectionParameterId())).findAny();
            if (any.isPresent()) {
                throw new IllegalArgumentException(inspectionParameter.getName() + "已被" + inspectionSchemeWithSubLayer.getName() + "使用,无法更改巡检参数类型");
            }
        }
    }


    private void checkNameRepeatWhileCreate(String name) {
        LambdaQueryWrapper<InspectionParameter> queryWrapper = LambdaQueryWrapper.of(InspectionParameter.class);
        queryWrapper.eq(InspectionParameter::getProjectId, GlobalInfoUtils.getTenantId()).eq(InspectionParameter::getName, name);
        InspectionParameter inspectionParameter = inspectionParameterDao.selectOne(queryWrapper);
        Assert.isNull(inspectionParameter, "巡检参数名重复");
    }

    private void checkNameRepeatWhileUpdate(Long id, String name) {
        LambdaQueryWrapper<InspectionParameter> queryWrapper = LambdaQueryWrapper.of(InspectionParameter.class);
        queryWrapper.eq(InspectionParameter::getProjectId, GlobalInfoUtils.getTenantId()).ne(InspectionParameter::getId, id).eq(InspectionParameter::getName, name);
        InspectionParameter inspectionParameter = inspectionParameterDao.selectOne(queryWrapper);
        Assert.isNull(inspectionParameter, "巡检参数名重复");
    }
}



