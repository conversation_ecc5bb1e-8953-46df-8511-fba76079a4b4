# 方法调用和常量适配脚本
Write-Host "Starting method and constant adaptation..." -ForegroundColor Green

$coreDir = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$javaFiles = Get-ChildItem -Path $coreDir -Filter "*.java" -Recurse

$modifiedCount = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $modified = $false

    # 方法调用替换
    if ($content -match "GlobalInfoUtils\.getProjectId\(\)") {
        $content = $content -replace "GlobalInfoUtils\.getProjectId\(\)", "GlobalInfoUtils.getTenantId()"
        $modified = $true
        Write-Host "Updated getProjectId() to getTenantId() in: $($file.Name)" -ForegroundColor Yellow
    }

    if ($content -match "CommonUtils\.calcDouble\(") {
        $content = $content -replace "CommonUtils\.calcDouble\(", "NumberCalcUtils.calcDouble("
        # 添加import如果不存在
        if ($content -notmatch "import com\.cet\.eem\.fusion\.common\.utils\.datatype\.NumberCalcUtils;") {
            $content = $content -replace "(package [^;]+;)", '$1' + "`n`nimport com.cet.eem.fusion.common.utils.datatype.NumberCalcUtils;"
        }
        $modified = $true
        Write-Host "Updated CommonUtils.calcDouble() to NumberCalcUtils.calcDouble() in: $($file.Name)" -ForegroundColor Yellow
    }

    if ($content -match "CommonUtils\.formatDoubleWithOutScientificNotation\(") {
        $content = $content -replace "CommonUtils\.formatDoubleWithOutScientificNotation\(", "StringFormatUtils.formatDoubleWithOutScientificNotation("
        # 添加import如果不存在
        if ($content -notmatch "import com\.cet\.eem\.fusion\.common\.utils\.datatype\.StringFormatUtils;") {
            $content = $content -replace "(package [^;]+;)", '$1' + "`n`nimport com.cet.eem.fusion.common.utils.datatype.StringFormatUtils;"
        }
        $modified = $true
        Write-Host "Updated CommonUtils.formatDoubleWithOutScientificNotation() in: $($file.Name)" -ForegroundColor Yellow
    }

    # 常量替换
    if ($content -match "Result\.SUCCESS_CODE") {
        $content = $content -replace "Result\.SUCCESS_CODE", "ErrorCode.SUCCESS_CODE"
        $modified = $true
        Write-Host "Updated Result.SUCCESS_CODE to ErrorCode.SUCCESS_CODE in: $($file.Name)" -ForegroundColor Yellow
    }

    if ($content -match "EemCommonUtils\.BLANK_STR") {
        $content = $content -replace "EemCommonUtils\.BLANK_STR", "StringFormatUtils.BLANK_STR"
        $modified = $true
        Write-Host "Updated EemCommonUtils.BLANK_STR to StringFormatUtils.BLANK_STR in: $($file.Name)" -ForegroundColor Yellow
    }

    if ($content -match "CommonUtils\.APPLICATION_MSEXCEL") {
        $content = $content -replace "CommonUtils\.APPLICATION_MSEXCEL", "ContentTypeDef.APPLICATION_MSEXCEL"
        $modified = $true
        Write-Host "Updated CommonUtils.APPLICATION_MSEXCEL to ContentTypeDef.APPLICATION_MSEXCEL in: $($file.Name)" -ForegroundColor Yellow
    }

    if ($content -match "CommonUtils\.APPLICATION_MS_EXCEL_07") {
        $content = $content -replace "CommonUtils\.APPLICATION_MS_EXCEL_07", "ContentTypeDef.APPLICATION_MS_EXCEL_07"
        $modified = $true
        Write-Host "Updated CommonUtils.APPLICATION_MS_EXCEL_07 to ContentTypeDef.APPLICATION_MS_EXCEL_07 in: $($file.Name)" -ForegroundColor Yellow
    }

    if ($content -match "CommonUtils\.DOUBLE_CONVERSION_COEFFICIENT") {
        $content = $content -replace "CommonUtils\.DOUBLE_CONVERSION_COEFFICIENT", "NumberCalcUtils.DOUBLE_CONVERSION_COEFFICIENT"
        $modified = $true
        Write-Host "Updated CommonUtils.DOUBLE_CONVERSION_COEFFICIENT to NumberCalcUtils.DOUBLE_CONVERSION_COEFFICIENT in: $($file.Name)" -ForegroundColor Yellow
    }

    # 类名替换
    if ($content -match "\bTableNameDef\.") {
        $content = $content -replace "\bTableNameDef\.", "ModelLabelDef."
        $modified = $true
        Write-Host "Updated TableNameDef to ModelLabelDef in: $($file.Name)" -ForegroundColor Yellow
    }

    if ($content -match "\bBaseEntity\b") {
        $content = $content -replace "\bBaseEntity\b", "EntityWithName"
        $modified = $true
        Write-Host "Updated BaseEntity to EntityWithName in: $($file.Name)" -ForegroundColor Yellow
    }

    if ($content -match "\bQueryConditionBuilder\b") {
        $content = $content -replace "\bQueryConditionBuilder\b", "ParentQueryConditionBuilder"
        $modified = $true
        Write-Host "Updated QueryConditionBuilder to ParentQueryConditionBuilder in: $($file.Name)" -ForegroundColor Yellow
    }

    # Service层产品查询方法更新
    if ($content -match "productDao\.queryProducts\(GlobalInfoUtils\.getTenantId\(\)\)") {
        $content = $content -replace "productDao\.queryProducts\(GlobalInfoUtils\.getTenantId\(\)\)", "productDao.queryProducts(GlobalInfoUtils.getTenantId(), Product.class)"
        $modified = $true
        Write-Host "Updated productDao.queryProducts() method signature in: $($file.Name)" -ForegroundColor Yellow
    }

    # 返回类型更新 - Controller层
    if ($content -match "public\s+ResultWithTotal<") {
        $content = $content -replace "public\s+ResultWithTotal<", "public ApiResult<"
        $modified = $true
        Write-Host "Updated ResultWithTotal return type to ApiResult in: $($file.Name)" -ForegroundColor Yellow
    }

    if ($content -match "public\s+Result<") {
        $content = $content -replace "public\s+Result<", "public ApiResult<"
        $modified = $true
        Write-Host "Updated Result return type to ApiResult in: $($file.Name)" -ForegroundColor Yellow
    }
    
    # 如果内容有修改，写回文件
    if ($modified -and $content -ne $originalContent) {
        try {
            # 确保使用UTF-8无BOM编码
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
            $modifiedCount++
            Write-Host "Modified file: $($file.Name)" -ForegroundColor Green
        }
        catch {
            Write-Host "Failed to modify file: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "Method and constant adaptation completed! Total files modified: $modifiedCount" -ForegroundColor Green
