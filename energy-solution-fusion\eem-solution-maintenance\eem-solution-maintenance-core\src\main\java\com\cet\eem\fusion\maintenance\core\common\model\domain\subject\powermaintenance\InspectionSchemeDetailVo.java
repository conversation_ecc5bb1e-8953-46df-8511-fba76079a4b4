package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @className InspectionSchemeDetailVo
 * @description
 * @date 2021/8/24 9:50
 */
@Getter
@Setter
public class InspectionSchemeDetailVo extends InspectionSchemeDetail {
    /**
     * 工单完成时间
     */
    private Long finishTime;

    public InspectionSchemeDetailVo() {
    }

    public InspectionSchemeDetailVo(Long id, Double max, Double min, Double value, Integer sort, String paraName, Integer type, Long inspectionParameterId, Long finishTime) {
        this.id = id;
        this.setMax(max);
        this.setMin(min);
        this.setValue(value);
        this.setSort(sort);
        this.setParaName(paraName);
        this.setType(type);
        this.setInspectionParameterId(inspectionParameterId);
        this.finishTime = finishTime;
    }
}