package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-12
 */
@Data
@AllArgsConstructor
@ApiModel("模板运行参数分组表")
@ModelLabel(ModelLabelDef.RUNNING_PARAM_TEMPLATE_GROUP)
public class TemplateGroupDto extends EntityWithName {
    /**private String name;*/
    @JsonProperty("metertype")
    private Integer meterType;
    public TemplateGroupDto() {
        this.modelLabel = ModelLabelDef.RUNNING_PARAM_TEMPLATE_GROUP;
    }
}


