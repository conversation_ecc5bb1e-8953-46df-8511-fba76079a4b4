package com.cet.eem.fusion.maintenance.core.model.sign;

import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
public class SignGroupWithSignIn {
    @JsonProperty(WorkOrderDef.SIGN_POINT_ID)
    private Long signPointId;

    @JsonProperty(WorkOrderDef.SIGN_GROUP_ID)
    private Long signGroupId;
}

