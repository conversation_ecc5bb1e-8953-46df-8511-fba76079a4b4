package com.cet.eem.fusion.maintenance.core.schedule.strategy;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import org.quartz.ScheduleBuilder;
import org.quartz.Trigger;
import org.springframework.stereotype.Component;

/**
 * @ClassName : OneMonthTriggerStrategy
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-26 10:37
 */
@Component(PlanSheetTriggerStrategyKey.ONE_MONTH)
public class OneMonthTriggerStrategy implements PlanSheetTriggerStrategy<Trigger> {
    @Override
    public ScheduleBuilder<Trigger> buildSchedule(PlanSheet planSheet) {
        return CronUtils.handleMonthTrigger(planSheet, 1);
    }
}

