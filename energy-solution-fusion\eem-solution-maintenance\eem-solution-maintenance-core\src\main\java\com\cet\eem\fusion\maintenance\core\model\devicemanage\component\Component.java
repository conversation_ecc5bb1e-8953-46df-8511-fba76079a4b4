package com.cet.eem.fusion.maintenance.core.model.devicemanage.component;

import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Component extends EntityWithName {
    @JsonProperty("equipment_id")
    private Long equipmentId;
    @JsonProperty("equipmentlabel")
    private Long equipmentLabel;

    private String code;

    private String brand;

    private String unit;

    private String specification;


    public Component(String name, String code, String brand, String unit,String specification) {
        this.name = name;
        this.code = code;
        this.brand = brand;
        this.unit = unit;
        this.specification = specification;
    }

    public Component(Long equipmentId) {
        this.equipmentId = equipmentId;
    }
}


