package com.cet.eem.fusion.maintenance.core.dao.devicecomponent.impl;

import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.DeviceDao;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.SparePartsDao;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SpareParts;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SparePartsDevice;
import com.cet.eem.fusion.maintenance.core.bll.common.model.ext.subject.powermaintenance.DeviceWithSubLayer;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.maintenance.common.toolkit.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


import java.util.*;

@Repository
public class SparePartsDaoImpl extends ModelDaoImpl<SpareParts> implements SparePartsDao {
    @Autowired
    DeviceDao deviceDao;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public DeviceWithSubLayer queryByNameAndId(String name, Long id, Long deviceId) {
        LambdaQueryWrapper<SpareParts> queryWrapper = LambdaQueryWrapper.of(SpareParts.class)
                .eq(SpareParts::getName, name)
                .ne(SpareParts::getId, id);
        return deviceDao.selectRelatedById(DeviceWithSubLayer.class, deviceId, Collections.singletonList(queryWrapper));
    }

    @Override
    public List<SpareParts> queryByDeviceAndKeyWord(Long id, String keyWord) {
        LambdaQueryWrapper<SpareParts> wrapper = LambdaQueryWrapper.of(SpareParts.class).like(SpareParts::getName, keyWord);
        DeviceWithSubLayer deviceWithSubLayer = deviceDao.selectRelatedById(DeviceWithSubLayer.class, id, Collections.singletonList(wrapper));
        if (null == deviceWithSubLayer) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(deviceWithSubLayer.getSparePartsList())) {
            return Collections.emptyList();
        }
        return deviceWithSubLayer.getSparePartsList();
    }

    @Override
    public List<SpareParts> queryByDevice(String model, String objectLabel) {
        LambdaQueryWrapper<SparePartsDevice> wrapper = LambdaQueryWrapper.of(SparePartsDevice.class)
                .eq(SparePartsDevice::getObjectLabel, objectLabel)
                .eq(SparePartsDevice::getModel, model);
        List<DeviceWithSubLayer> deviceWithSubLayers = deviceDao.selectRelatedList(DeviceWithSubLayer.class, wrapper);
        if (CollectionUtils.isEmpty(deviceWithSubLayers)) {
            return Collections.emptyList();
        }
        List<SpareParts> spareParts = new ArrayList<>();
        for (DeviceWithSubLayer deviceWithSubLayer : deviceWithSubLayers) {
            if (CollectionUtils.isEmpty(deviceWithSubLayer.getSparePartsList())) {
                continue;
            }
            spareParts.addAll(deviceWithSubLayer.getSparePartsList());
        }
        return spareParts;
    }

    @Override
    public DeviceWithSubLayer queryByModel(String model, Long id, Long deviceId) {
        LambdaQueryWrapper<SpareParts> queryWrapper = LambdaQueryWrapper.of(SpareParts.class)
                .eq(SpareParts::getModel, model)
                .ne(SpareParts::getId, id);
        return deviceDao.selectRelatedById(DeviceWithSubLayer.class, deviceId, Collections.singletonList(queryWrapper));
    }

    @Override
    public List<Map<String, Object>> querySparePartsStorageWithSystem(Collection<Long> ids) {
        QueryCondition condition = new ParentQueryConditionBuilder<>(ModelLabelDef.SPARE_PARTS_STORAGE)
                .in(ColumnDef.ID, ids)
                .leftJoin(ModelLabelDef.DEVICE_SYSTEM)
                .queryAsTree()
                .build();

        return modelServiceUtils.query(condition);
    }
}


