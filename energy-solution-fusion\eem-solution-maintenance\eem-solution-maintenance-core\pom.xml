<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cet.electric</groupId>
        <artifactId>eem-solution-maintenance</artifactId>
        <version>4.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>eem-solution-maintenance-core</artifactId>
    <version>4.0.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>eem-solution-common</artifactId>
            <version>4.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>workflow-service-common</artifactId>
            <version>2.0.110.3-SHAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>workflow-service-api</artifactId>
            <version>2.0.110.3-SHAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>workflow-service-feign-spring-boot-starter</artifactId>
            <version>2.0.110.3-SHAPSHOT</version>
        </dependency>

    </dependencies>
</project>
