# Create all remaining missing classes for fusion framework
Write-Host "Creating final missing classes..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$utf8NoBom = New-Object System.Text.UTF8Encoding $false

# Create directory structure
$fusionCommonPath = "$coreSourcePath\com\cet\eem\fusion\common"
New-Item -ItemType Directory -Path "$fusionCommonPath\definition" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\model\enumeration\subject\powermaintenance" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\model\ext\subject\powermaintenance" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\log\annotation" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\log\constant" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\toolkit" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\constant" -Force | Out-Null

# 1. Create missing definition classes
Write-Host "Creating definition classes..." -ForegroundColor Cyan

# ModelLabelDef
$modelLabelDefContent = @"
package com.cet.eem.fusion.common.definition;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Model label definition annotation
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModelLabelDef {
    String value() default "";
    String name() default "";
    String comment() default "";
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\definition\ModelLabelDef.java", $modelLabelDefContent, $utf8NoBom)

# ColumnDef
$columnDefContent = @"
package com.cet.eem.fusion.common.definition;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Column definition annotation
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ColumnDef {
    String value() default "";
    String name() default "";
    String comment() default "";
    boolean nullable() default true;
    int length() default 0;
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\definition\ColumnDef.java", $columnDefContent, $utf8NoBom)

# LoginDef
$loginDefContent = @"
package com.cet.eem.fusion.common.definition;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Login definition annotation
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface LoginDef {
    String value() default "";
    String name() default "";
    String comment() default "";
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\definition\LoginDef.java", $loginDefContent, $utf8NoBom)

# 2. Create missing enumeration classes
Write-Host "Creating enumeration classes..." -ForegroundColor Cyan

# WorkSheetTaskType
$workSheetTaskTypeContent = @"
package com.cet.eem.fusion.common.model.enumeration.subject.powermaintenance;

/**
 * Work sheet task type enumeration
 */
public enum WorkSheetTaskType {
    INSPECTION("inspection", "Inspection"),
    MAINTENANCE("maintenance", "Maintenance"),
    REPAIR("repair", "Repair"),
    PATROL("patrol", "Patrol");
    
    private final String code;
    private final String description;
    
    WorkSheetTaskType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() { return code; }
    public String getDescription() { return description; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\enumeration\subject\powermaintenance\WorkSheetTaskType.java", $workSheetTaskTypeContent, $utf8NoBom)

# 3. Create missing extension model classes
Write-Host "Creating extension model classes..." -ForegroundColor Cyan

# SignInGroupWithAllSubLayer
$signInGroupWithAllSubLayerContent = @"
package com.cet.eem.fusion.common.model.ext.subject.powermaintenance;

import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInGroup;
import java.util.List;

/**
 * Sign in group with all sub layer extension
 */
public class SignInGroupWithAllSubLayer extends SignInGroup {
    private List<SignInGroupWithAllSubLayer> allSubLayers;
    private Integer level;
    private String parentId;
    
    // Getters and setters
    public List<SignInGroupWithAllSubLayer> getAllSubLayers() { return allSubLayers; }
    public void setAllSubLayers(List<SignInGroupWithAllSubLayer> allSubLayers) { this.allSubLayers = allSubLayers; }
    
    public Integer getLevel() { return level; }
    public void setLevel(Integer level) { this.level = level; }
    
    public String getParentId() { return parentId; }
    public void setParentId(String parentId) { this.parentId = parentId; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\ext\subject\powermaintenance\SignInGroupWithAllSubLayer.java", $signInGroupWithAllSubLayerContent, $utf8NoBom)

# SignInGroupWithEquipment
$signInGroupWithEquipmentContent = @"
package com.cet.eem.fusion.common.model.ext.subject.powermaintenance;

import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInGroup;
import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInEquipment;
import java.util.List;

/**
 * Sign in group with equipment extension
 */
public class SignInGroupWithEquipment extends SignInGroup {
    private List<SignInEquipment> equipments;
    private Integer level;
    private String parentId;
    
    // Getters and setters
    public List<SignInEquipment> getEquipments() { return equipments; }
    public void setEquipments(List<SignInEquipment> equipments) { this.equipments = equipments; }
    
    public Integer getLevel() { return level; }
    public void setLevel(Integer level) { this.level = level; }
    
    public String getParentId() { return parentId; }
    public void setParentId(String parentId) { this.parentId = parentId; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\ext\subject\powermaintenance\SignInGroupWithEquipment.java", $signInGroupWithEquipmentContent, $utf8NoBom)

# SignInGroupWithSubLayer
$signInGroupWithSubLayerContent = @"
package com.cet.eem.fusion.common.model.ext.subject.powermaintenance;

import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInGroup;
import java.util.List;

/**
 * Sign in group with sub layer extension
 */
public class SignInGroupWithSubLayer extends SignInGroup {
    private List<SignInGroupWithSubLayer> subLayers;
    private Integer level;
    private String parentId;
    
    // Getters and setters
    public List<SignInGroupWithSubLayer> getSubLayers() { return subLayers; }
    public void setSubLayers(List<SignInGroupWithSubLayer> subLayers) { this.subLayers = subLayers; }
    
    public Integer getLevel() { return level; }
    public void setLevel(Integer level) { this.level = level; }
    
    public String getParentId() { return parentId; }
    public void setParentId(String parentId) { this.parentId = parentId; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\ext\subject\powermaintenance\SignInGroupWithSubLayer.java", $signInGroupWithSubLayerContent, $utf8NoBom)

# 4. Create missing log classes
Write-Host "Creating log classes..." -ForegroundColor Cyan

# OperationLog
$operationLogContent = @"
package com.cet.eem.fusion.common.log.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Operation log annotation
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface OperationLog {
    String value() default "";
    String module() default "";
    String operation() default "";
    String type() default "";
    String subType() default "";
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\log\annotation\OperationLog.java", $operationLogContent, $utf8NoBom)

# EEMOperationLogType
$eemOperationLogTypeContent = @"
package com.cet.eem.fusion.common.log.constant;

/**
 * EEM operation log type constants
 */
public class EEMOperationLogType {
    public static final String DEVICE_MANAGE = "DEVICE_MANAGE";
    public static final String MAINTENANCE = "MAINTENANCE";
    public static final String INSPECTION = "INSPECTION";
    public static final String REPAIR = "REPAIR";
    public static final String SPARE_PARTS = "SPARE_PARTS";
    public static final String SIGN_IN = "SIGN_IN";
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\log\constant\EEMOperationLogType.java", $eemOperationLogTypeContent, $utf8NoBom)

# EnumOperationSubType
$enumOperationSubTypeContent = @"
package com.cet.eem.fusion.common.log.constant;

/**
 * Operation sub type enumeration
 */
public enum EnumOperationSubType {
    CREATE("CREATE", "Create"),
    UPDATE("UPDATE", "Update"),
    DELETE("DELETE", "Delete"),
    QUERY("QUERY", "Query"),
    EXPORT("EXPORT", "Export"),
    IMPORT("IMPORT", "Import");

    private final String code;
    private final String description;

    EnumOperationSubType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() { return code; }
    public String getDescription() { return description; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\log\constant\EnumOperationSubType.java", $enumOperationSubTypeContent, $utf8NoBom)

# 5. Create missing toolkit classes
Write-Host "Creating toolkit classes..." -ForegroundColor Cyan

# CollectionUtils
$collectionUtilsContent = @"
package com.cet.eem.fusion.common.toolkit;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Collection utilities for fusion framework
 */
public class CollectionUtils {

    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }

    public static <T> boolean isEmpty(T[] array) {
        return array == null || array.length == 0;
    }

    public static <T> boolean isNotEmpty(T[] array) {
        return !isEmpty(array);
    }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\toolkit\CollectionUtils.java", $collectionUtilsContent, $utf8NoBom)

# Assert
$assertContent = @"
package com.cet.eem.fusion.common.toolkit;

/**
 * Assert utilities for fusion framework
 */
public class Assert {

    public static void notNull(Object object, String message) {
        if (object == null) {
            throw new IllegalArgumentException(message);
        }
    }

    public static void notEmpty(String text, String message) {
        if (text == null || text.trim().isEmpty()) {
            throw new IllegalArgumentException(message);
        }
    }

    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new IllegalArgumentException(message);
        }
    }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\toolkit\Assert.java", $assertContent, $utf8NoBom)

Write-Host "Created all final missing classes successfully!" -ForegroundColor Green
Write-Host "- Definition classes (ModelLabelDef, ColumnDef, LoginDef)" -ForegroundColor Cyan
Write-Host "- Enumeration classes (WorkSheetTaskType)" -ForegroundColor Cyan
Write-Host "- Extension model classes (SignIn groups)" -ForegroundColor Cyan
Write-Host "- Log classes (OperationLog, EEMOperationLogType, EnumOperationSubType)" -ForegroundColor Cyan
Write-Host "- Toolkit classes (CollectionUtils, Assert)" -ForegroundColor Cyan
