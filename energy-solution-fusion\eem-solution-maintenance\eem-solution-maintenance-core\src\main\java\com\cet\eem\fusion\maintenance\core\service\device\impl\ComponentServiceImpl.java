﻿package com.cet.eem.fusion.maintenance.core.service.device.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.cet.eem.fusion.common.service.auth.TreeLevelService;
import com.cet.eem.fusion.common.service.auth.impl.CommonAuthService;
import com.cet.eem.fusion.config.sdk.model.node.RoomVo;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.DeviceComponent;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SpareParts;
import com.cet.eem.fusion.common.utils.excel.ExcelValidationUtils;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.DeviceComponentDao;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.DeviceDao;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.DeviceSystemDao;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.SparePartsDao;
import com.cet.eem.fusion.maintenance.core.dao.devicemanager.ArrayCabiNetDao;
import com.cet.eem.fusion.maintenance.core.dao.devicemanager.PowerDisCabinetDao;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.ArrayCabiNetWithLayer;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.LineSegmentVo;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.PowerDisCabinetWithLayer;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.*;
import com.cet.eem.fusion.maintenance.core.service.device.ComponentService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.common.ErrorUtils;
import com.cet.eem.fusion.common.constant.ExcelType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.conditions.query.QueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.maintenance.core.common.model.base.SingleModelConditionDTO;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.SubConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import com.cet.eem.fusion.common.def.common.ContentTypeDef;

/**
 * @Author: jzx
 * @Description:
 * @Data: Created in 2021-05-11
 */
@Service
public class ComponentServiceImpl implements ComponentService {
    @Autowired
    DeviceComponentDao componentDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    DeviceSystemDao deviceSystemDao;
    @Autowired
    SparePartsDao sparePartsDao;
    @Autowired
    DeviceDao deviceDao;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    PowerDisCabinetDao powerDisCabinetDao;
    @Autowired
    ArrayCabiNetDao arrayCabiNetDao;
    @Autowired
    CommonAuthService authManageService;
    @Autowired
    TreeLevelService treeLevelService;


    /**
     * 根据设备查零件库
     *
     * @param dto 查询条件
     * @return List<DeviceComponent>
     */
    @Override
    public List<DeviceComponent> queryComponent(QueryNodeComponentDto dto) {
        return componentDao.queryComponentByDevice(dto.getObjectLabel(), dto.getObjectId());
    }

    /**
     * 删除零件
     *
     * @param ids 删除节点列表
     * @return void
     */
    @Override
    public void delecteComponent(List<Long> ids) {
        modelServiceUtils.delete(ModelLabelDef.DEVICE_COMPONENT, ids);
    }

    /**
     * 新增零件
     *
     * @param addDeviceComponent 新增条件
     * @return DeviceComponent
     */
    @Override
    public DeviceComponent addComponent(AddDeviceComponent addDeviceComponent) {
        //零件库中存储的设备信息是具体的设备信息，新增时需要去查询该节点是否存在
        boolean b = checkObjectExist(addDeviceComponent.getObjectLabel(), addDeviceComponent.getObjectId());
        Assert.isTrue(!b, "找不到对应的设备节点");
        //根据名称查询重复信息
        checkComponentRepeatWhileAdd(addDeviceComponent);
        DeviceComponent component = new DeviceComponent();
        component.setBrand(addDeviceComponent.getBrand());
        component.setModel(addDeviceComponent.getModel());
        component.setNumber(addDeviceComponent.getNumber());
        component.setUnit(addDeviceComponent.getUnit());
        component.setName(addDeviceComponent.getName());
        component.setProjectId(GlobalInfoUtils.getTenantId());
        component.setObjectLabel(addDeviceComponent.getObjectLabel());
        component.setObjectId(addDeviceComponent.getObjectId());
        componentDao.insert(component);

        return component;
    }

    /**
     * 编辑零件
     *
     * @param editDeviceComponent 编辑条件
     * @return DeviceComponent
     */
    @Override
    public DeviceComponent editComponent(EditDeviceComponent editDeviceComponent) {
        checkRepeatWhileEdit(editDeviceComponent);
        DeviceComponent component = new DeviceComponent();
        component.setBrand(editDeviceComponent.getBrand());
        component.setModel(editDeviceComponent.getModel());
        component.setNumber(editDeviceComponent.getNumber());
        component.setUnit(editDeviceComponent.getUnit());
        component.setName(editDeviceComponent.getName());
        component.setId(editDeviceComponent.getId());
        component.setObjectId(editDeviceComponent.getObjectId());
        component.setObjectLabel(editDeviceComponent.getObjectLabel());
        component.setProjectId(GlobalInfoUtils.getTenantId());
        componentDao.updateById(component);
        return component;
    }

    /**
     * 导入零件数据
     *
     * @param file 文件
     * @throws IOException 异常
     */
    @Override
    public void importComponent(MultipartFile file) throws IOException {
        List<DeviceComponent> deviceComponents = new ArrayList<>();
        EasyExcelFactory.read(file.getInputStream(), DeviceComponentImport.class, new DeviceComponentListener(deviceComponents)).sheet().doRead();
        if (CollectionUtils.isEmpty(deviceComponents)) {
            return;
        }
        DeviceComponent repeatInFile = isRepeatInFile(deviceComponents);

        if (null != repeatInFile) {
            QueryCondition condition = new ParentQueryConditionBuilder<>(repeatInFile.getObjectLabel(), repeatInFile.getObjectId())
                    .build();
            List<BaseVo> query = modelServiceUtils.query(condition, BaseVo.class);
            String deviceName = null;
            for (BaseVo baseVo : query) {
                if (null != baseVo) {
                    deviceName = baseVo.getName();
                }
            }
            Assert.isNull(repeatInFile, "设备名称为" + deviceName + "id为" + repeatInFile.getObjectId().toString() + "设备类型为" + repeatInFile.getObjectLabel() + "的零件型号重复");
        }
        writeComponent(deviceComponents);
    }

    /**
     * 判断导入文件中的重复数据
     *
     * @param deviceComponents 导入的文件数据
     * @return DeviceComponent
     */
    private DeviceComponent isRepeatInFile(List<DeviceComponent> deviceComponents) {
        if (CollectionUtils.isEmpty(deviceComponents) || deviceComponents.size() == 1) {
            return null;
        }
        for (int i = 0; i < deviceComponents.size() - 1; i++) {
            DeviceComponent item = deviceComponents.get(i);
            for (int j = i + 1; j < deviceComponents.size(); j++) {
                DeviceComponent itemNow = deviceComponents.get(j);
                //导入的节点信息，零件的名称或者模型都不能重复。
                if (item.getObjectLabel().equals(itemNow.getObjectLabel()) && item.getObjectId().equals(itemNow.getObjectId()) && item.getModel().equals(itemNow.getModel())) {
                    return item;
                }
            }
        }
        return null;
    }

    /**
     * 页面选择类型时需要数据库的model数据。
     *
     * @param keyword 关键字
     * @return Set<String>
     */
    @Override
    public Set<String> queryModelList(String keyword) {

        Set<String> result = new HashSet<>();
        Set<String> filterModels;
        if (ModelLabelDef.SPARE_PARTS_STORAGE.equals(keyword)) {
            LambdaQueryWrapper<SpareParts> queryWrapper = LambdaQueryWrapper.of(SpareParts.class);
            List<SpareParts> spareParts = sparePartsDao.selectList(queryWrapper);
            filterModels = spareParts.stream().map(SpareParts::getModel).collect(Collectors.toSet());
        } else if (ModelLabelDef.DEVICE_COMPONENT.equals(keyword)) {
            LambdaQueryWrapper<DeviceComponent> queryWrapper = LambdaQueryWrapper.of(DeviceComponent.class);
            List<DeviceComponent> deviceComponents = componentDao.selectList(queryWrapper);
            filterModels = deviceComponents.stream().map(DeviceComponent::getModel).collect(Collectors.toSet());
        } else {
            QueryCondition condition = new ParentQueryConditionBuilder<>(keyword).build();
            List<Map<String, Object>> query = modelServiceUtils.query(condition);
            List<BaseVo> baseVos = JsonTransferUtils.transferList(query, BaseVo.class);
            List<DeviceTrans> deviceTrans = nodeDao.queryNodes(baseVos, DeviceTrans.class);
            filterModels = deviceTrans.stream().map(DeviceTrans::getModel).filter(Objects::nonNull).collect(Collectors.toSet());
        }
        if (!CollectionUtils.isEmpty(filterModels)) {
            result = filterModels;
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> queryDeviceTree(Long projectId) {
        return deviceSystemDao.queryDeviceTreeByProjectId(projectId);
    }

    private Integer getRoomType(Long id) {
        QueryCondition condition = new ParentQueryConditionBuilder<>(NodeLabelDef.ROOM, id).build();
        List<Map<String, Object>> query = modelServiceUtils.query(condition);
        if (CollectionUtils.isEmpty(query)) {
            return null;
        }
        List<RoomVo> roomVos = JsonTransferUtils.transferList(query, RoomVo.class);
        for (RoomVo roomVo : roomVos) {
            if (null != roomVo.getRoomtype()) {
                return roomVo.getRoomtype();
            }
        }
        return null;
    }

    private List<BaseVo> getProjectNodes(Long id) {
        List<BaseVo> deviceNodes = new ArrayList<>();
        SubConditionBuilder subConditionBuilder = new SubConditionBuilder(NodeLabelDef.ROOM);
        subConditionBuilder.where(ColumnDef.ROOM_TYPE, ConditionBlock.OPERATOR_NE, null);

        QueryCondition condition = new ParentQueryConditionBuilder<>(NodeLabelDef.PROJECT, id)
                .leftJoin(subConditionBuilder.queryDepth(1).build()).queryAsTree().build();
        List<Map<String, Object>> query = modelServiceUtils.query(condition);
        List<BaseVo> projectNodes = JsonTransferUtils.transferList(query, BaseVo.class);
        if (CollectionUtils.isEmpty(projectNodes)) {
            return Collections.emptyList();
        }
        List<BaseVo> roomNodes = new ArrayList<>();
        for (BaseVo item : projectNodes) {
            if (CollectionUtils.isNotEmpty(item.getChildren())) {
                roomNodes.addAll(item.getChildren());
            }
        }
        Set<Long> collect = roomNodes.stream().map(BaseVo::getId).collect(Collectors.toSet());
        QueryCondition queryCondition = new ParentQueryConditionBuilder<>(NodeLabelDef.ROOM)
                .where(ColumnDef.ID, ConditionBlock.OPERATOR_IN, collect)
                .leftJoin(NodeLabelDef.DEVICE_MANAGER)
                .queryAsTree(true)
                .build();
        List<Map<String, Object>> query1 = modelServiceUtils.query(queryCondition);
        List<BaseVo> baseVos = JsonTransferUtils.transferList(query1, BaseVo.class);
        for (BaseVo item : baseVos) {
            if (CollectionUtils.isNotEmpty(item.getChildren())) {
                deviceNodes.addAll(item.getChildren());
            }
        }
        return deviceNodes;
    }

    /**
     * 获得要导出的设备节点信息
     *
     * @param id         节点id
     * @param modelLabel 节点模型
     * @param roomType   房间类型
     * @return List<BaseVo>
     */
    private List<BaseVo> getResultNodes(Long id, @NotNull String modelLabel, Integer roomType) {


        //需要查询的节点信息
        List<BaseVo> resultNodes = new ArrayList<>();
        if (modelLabel.equals(NodeLabelDef.ROOM)) {
            List<BaseVo> emptyList = getBaseVoList(id, modelLabel, roomType, resultNodes);
            if (emptyList != null) return emptyList;
        } else if (modelLabel.equals(NodeLabelDef.PROJECT)) {
            resultNodes.addAll(getProjectNodes(id));
        } else {
            //根据前端传递过来的设备类型来判断数据
            List<BaseVo> emptyList = getBaseVoList(id, modelLabel, resultNodes);
            if (emptyList != null) return emptyList;
        }
        return resultNodes;
    }

    private List<BaseVo> getBaseVoList(Long id, String modelLabel, List<BaseVo> resultNodes) {
        if (modelLabel.equals(ModelLabelDef.POWER_DIS_CABINET)) {
            //根据设备类型，查询配电柜子层级的设备信息。
            PowerDisCabinetWithLayer powerDisCabinetWithLayer = powerDisCabinetDao.
                    selectRelatedById(PowerDisCabinetWithLayer.class, id, Collections.singletonList(QueryWrapper.of(LineSegmentVo.class)));
            if (null == powerDisCabinetWithLayer) {
                return Collections.emptyList();
            }
            //获得父层级的节点信息
            resultNodes.add(new BaseVo(powerDisCabinetWithLayer.getId(), powerDisCabinetWithLayer.getModelLabel(), powerDisCabinetWithLayer.getName()));
            //获得子层级的节点信息
            if (CollectionUtils.isNotEmpty(powerDisCabinetWithLayer.getList())) {
                List<BaseVo> nodeSubLayer = powerDisCabinetWithLayer.getList().stream().map(it -> new BaseVo(it.getId(), it.getModelLabel(), it.getName())).collect(Collectors.toList());
                resultNodes.addAll(nodeSubLayer);
            }

        } else if (modelLabel.equals(NodeLabelDef.ARRAY_CABINET)) {
            //根据设备类型，查询列头柜子层级的设备信息。
            ArrayCabiNetWithLayer arrayCabiNetWithLayer = arrayCabiNetDao.
                    selectRelatedById(ArrayCabiNetWithLayer.class, id, Collections.singletonList(QueryWrapper.of(LineSegmentVo.class)));
            if (null == arrayCabiNetWithLayer) {
                return Collections.emptyList();
            }
            //获得父层级的节点信息
            resultNodes.add(new BaseVo(arrayCabiNetWithLayer.getId(), arrayCabiNetWithLayer.getModelLabel(), arrayCabiNetWithLayer.getName()));
            //获得子层级的节点信息
            if (CollectionUtils.isNotEmpty(arrayCabiNetWithLayer.getList())) {
                List<BaseVo> nodeSubLayer = arrayCabiNetWithLayer.getList().stream().map(it -> new BaseVo(it.getId(), it.getModelLabel(), it.getName())).collect(Collectors.toList());
                resultNodes.addAll(nodeSubLayer);
            }

        } else {
            //查询其他类型的设备信息
            QueryCondition condition = new ParentQueryConditionBuilder<>(modelLabel, id).build();
            List<Map<String, Object>> query = modelServiceUtils.query(condition);
            resultNodes.addAll(JsonTransferUtils.transferList(query, BaseVo.class));
        }
        return resultNodes;
    }

    private List<BaseVo> getBaseVoList(Long id, String modelLabel, Integer roomType, List<BaseVo> resultNodes) {
        if (null == roomType) {
            roomType = getRoomType(id);
        }
        List<String> subLabels = treeLevelService.getSonLabel(NodeLabelDef.ROOM, roomType);
        List<SingleModelConditionDTO> subChildren = new ArrayList<>();
        for (String subLabel : subLabels) {
            subChildren.add(new SubConditionBuilder(subLabel).queryDepth(1).build());
        }
        QueryCondition condition = new ParentQueryConditionBuilder<>(modelLabel, id)
                .leftJoinCondition(subChildren).queryAsTree().build();
        List<Map<String, Object>> baseVos = modelServiceUtils.query(condition);
        if (CollectionUtils.isEmpty(baseVos)) {
            return Collections.emptyList();
        }
        List<BaseVo> baseVos1 = JsonTransferUtils.transferList(baseVos, BaseVo.class);
        for (BaseVo item : baseVos1) {
            if (CollectionUtils.isNotEmpty(item.getChildren())) {
                resultNodes.addAll(item.getChildren());
            }
        }
        return resultNodes;
    }

    /**
     * 为零件添加对应的设备名称
     *
     * @param resultNodes      设备节点
     * @param deviceComponents 零件信息
     * @return
     */
    private List<ComponentWithDeviceName> addDeviceName(List<BaseVo> resultNodes, List<DeviceComponent> deviceComponents) {
        if (CollectionUtils.isEmpty(resultNodes) || CollectionUtils.isEmpty(deviceComponents)) {
            return Collections.emptyList();
        }
        List<ComponentWithDeviceName> componentWithDeviceName = new ArrayList<>();
        for (DeviceComponent item : deviceComponents) {
            for (BaseVo itemModel : resultNodes) {
                if (item.getObjectLabel().equals(itemModel.getModelLabel()) && item.getObjectId().equals(itemModel.getId()) && null != itemModel.getName()) {
                    ComponentWithDeviceName component = new ComponentWithDeviceName();
                    component.setDeviceName(itemModel.getName());
                    component.setName(item.getName());
                    component.setModel(item.getModel());
                    component.setObjectLabel(item.getObjectLabel());
                    component.setUnit(item.getUnit());
                    component.setObjectId(item.getObjectId());
                    component.setBrand(item.getBrand());
                    component.setNumber(item.getNumber());
                    componentWithDeviceName.add(component);
                    break;
                }
            }
        }
        return componentWithDeviceName;
    }

    /**
     * 写入文件
     *
     * @param response         响应
     * @param resultNodes      下拉节点的数据
     * @param deviceComponents 查询的数据
     */
    private void write(HttpServletResponse response, List<BaseVo> resultNodes, List<DeviceComponent> deviceComponents) {
        String fileName = "零件导出" + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT);

        try (Workbook workBook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA)) {
            List<Integer> colWidth = Arrays.asList(18, 18, 18, 18, 18, 18);
            if (CollectionUtils.isEmpty(deviceComponents)) {
                PoiExcelUtils.createSheet(workBook, "零件模板", (sheet, baseCellStyle, rowIndex) -> {
                    int rowNum = 0;
                    // 写入导出连接关系表数据
                    writeHeaderByType(workBook, sheet, baseCellStyle, rowNum++, resultNodes);
                }, colWidth);

            } else {
                //为零件信息添加对应设备的name信息
                List<ComponentWithDeviceName> componentWithDeviceName = addDeviceName(resultNodes, deviceComponents);
                PoiExcelUtils.createSheet(workBook, fileName, (sheet, baseCellStyle, rowIndex) -> {
                    int rowNum = 0;
                    // 写入导出连接关系表数据
                    writeHeaderByType(workBook, sheet, baseCellStyle, rowNum++, resultNodes);
                    writeRecord(sheet, baseCellStyle, rowNum, componentWithDeviceName);
                }, colWidth);
            }
            FileUtils.downloadExcel(response, workBook, fileName, ContentTypeDef.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            ErrorUtils.exportError("零件导出", e);
        }
    }

    @Override
    public void exportDeviceComponentByDeviceType(HttpServletResponse response, Long id, String modelLabel, Integer type) {
        //设备节点信息
        List<BaseVo> resultNodes = getResultNodes(id, modelLabel, type);
        List<DeviceComponent> deviceComponents;
        //按设备类型查询零件库的信息
        LambdaQueryWrapper<DeviceComponent> wrapper = LambdaQueryWrapper.of(DeviceComponent.class);
        Map<String, List<BaseVo>> nodeMap = resultNodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        nodeMap.forEach((key, val) -> {
            Set<Long> ids = val.stream().map(BaseVo::getId).collect(Collectors.toSet());
            wrapper.or(it -> it.eq(DeviceComponent::getObjectLabel, key).in(DeviceComponent::getObjectId, ids));
        });
        deviceComponents = componentDao.selectList(wrapper);
        write(response, resultNodes, deviceComponents);
    }

    private CellStyle createRequiredStyle(Workbook workbook, CellStyle baseCellStyle) {
        CellStyle requiredCellStyle = workbook.createCellStyle();
        requiredCellStyle.cloneStyleFrom(baseCellStyle);
        Font font = PoiExcelUtils.createFont(workbook, true, null, null, HSSFColor.HSSFColorPredefined.RED.getIndex());
        requiredCellStyle.setFont(font);
        return requiredCellStyle;
    }

    private void writeHeaderByType(Workbook workbook, Sheet sheet, CellStyle baseCellStyle, int startRow, List<BaseVo> baseVos) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        int col = 0;
        List<String> nodeValidationList = new ArrayList<>();
        for (BaseVo baseVo : baseVos) {
            nodeValidationList.add(CommonUtils.setDeviceNameAndIdAndModelLabel(baseVo.getName(), baseVo.getModelLabel(), baseVo.getId()));
        }
        String[] nodeValidation = nodeValidationList.toArray(new String[nodeValidationList.size()]);
        CellStyle requiredStyle = createRequiredStyle(workbook, baseCellStyle);
        headerMap.put("设备节点", requiredStyle);
        sheet.setDefaultColumnStyle(col, baseCellStyle);
        ExcelValidationUtils.addValidationData(workbook, "设备信息", sheet, nodeValidation, startRow + 1, col);
        headerMap.put("零件名称", requiredStyle);
        headerMap.put("型号", requiredStyle);
        headerMap.put("厂家", requiredStyle);
        headerMap.put("数量", requiredStyle);
        headerMap.put("单位", requiredStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    private void writeRecord(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<ComponentWithDeviceName> componentWithDeviceName) {
        int col;

        for (ComponentWithDeviceName log : componentWithDeviceName) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, CommonUtils.setDeviceNameAndIdAndModelLabel(log.getDeviceName(), log.getObjectLabel(), log.getObjectId()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (log.getName()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (log.getModel()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (log.getBrand()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (log.getNumber().toString()));
            PoiExcelUtils.createCell(row, col, baseCellStyle, (log.getUnit()));
            rowNum++;
        }
    }

    private boolean checkObjectExist(String label, Long id) {
        BaseVo b = new BaseVo(id, label);
        return CollectionUtils.isEmpty(nodeDao.queryNodes(Collections.singletonList(b)));
    }

    /**
     * @param deviceComponentList
     * @return
     * @deprecated 已废弃
     */
    @Deprecated
    private List<DeviceComponent> writeComponent(List<DeviceComponent> deviceComponentList) {
        if (CollectionUtils.isEmpty(deviceComponentList)) {
            return Collections.emptyList();
        }
        for (DeviceComponent deviceComponent : deviceComponentList) {
            deviceComponent.setProjectId(GlobalInfoUtils.getTenantId());
        }
        List<BaseVo> nodes = deviceComponentList.stream().map(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())).collect(Collectors.toList());
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        LambdaQueryWrapper<DeviceComponent> wrapper = LambdaQueryWrapper.of(DeviceComponent.class);

        nodeMap.forEach((key, val) -> {
            Set<Long> ids = val.stream().map(BaseVo::getId).collect(Collectors.toSet());
            wrapper.or(it -> it.eq(DeviceComponent::getObjectLabel, key).in(DeviceComponent::getObjectId, ids));
        });
        //按类型对数据进行查询，因为有的情况类型可能只有一种或者两种。
        List<DeviceComponent> deviceComponents1 = componentDao.selectList(wrapper);
        if (CollectionUtils.isEmpty(deviceComponents1)) {
            return modelServiceUtils.writeData(deviceComponentList, DeviceComponent.class);
        }

        List<DeviceComponent> result = new ArrayList<>();
        for (DeviceComponent item : deviceComponentList) {
            boolean flag = true;
            for (DeviceComponent itemNow : deviceComponents1) {
                if (itemNow.getModel().equals(item.getModel()) && item.getObjectId().equals(itemNow.getObjectId()) && item.getObjectLabel().equals(itemNow.getObjectLabel())) {
                    //型号一样，作为对该零件信息的编辑
                    flag = false;
                    DeviceComponent upadate = new DeviceComponent(item.getName(), item.getModel(), item.getBrand(), item.getUnit(), item.getNumber(), item.getObjectLabel(), item.getObjectId(), itemNow.getId());
                    upadate.setProjectId(GlobalInfoUtils.getTenantId());
                    result.add(upadate);
                    break;
                }
            }
            if (flag) {
                result.add(item);
            }

        }
        return modelServiceUtils.writeData(result, DeviceComponent.class);

    }

    private void checkComponentRepeatWhileAdd(AddDeviceComponent addDeviceComponent) {
        DeviceComponent deviceComponent = componentDao.querySingle(addDeviceComponent.getObjectLabel(), addDeviceComponent.getObjectId(), addDeviceComponent.getName(), addDeviceComponent.getModel());
        Assert.isNull(deviceComponent, "零件型号重复");
    }

    private void checkRepeatWhileEdit(EditDeviceComponent editDeviceComponent) {
        DeviceComponent deviceComponent = componentDao.queryWhileEdit(editDeviceComponent.getObjectLabel(), editDeviceComponent.getObjectId(), editDeviceComponent.getName(), editDeviceComponent.getId(), editDeviceComponent.getModel());
        Assert.isNull(deviceComponent, "零件型号重复");
    }
}




