# 修复剩余的 authUtils.queryAndCheckUser 调用
Write-Host "Fixing remaining authUtils.queryAndCheckUser calls..." -ForegroundColor Green

$coreDir = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$javaFiles = Get-ChildItem -Path $coreDir -Filter "*.java" -Recurse

$modifiedCount = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $modified = $false
    
    # 查找所有 authUtils.queryAndCheckUser 调用
    if ($content -match "authUtils\.queryAndCheckUser\(") {
        Write-Host "Processing file: $($file.Name)" -ForegroundColor Yellow
        
        # 替换所有的 authUtils.queryAndCheckUser 调用
        $content = $content -replace "UserVo\s+(\w+)\s*=\s*authUtils\.queryAndCheckUser\(([^)]+)\);", "ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(`$2);`nUserVo `$1 = usrRes.getData();"
        
        # 处理没有变量声明的情况
        $content = $content -replace "authUtils\.queryAndCheckUser\(([^)]+)\)", "userRestApi.getUserByUserId(`$1).getData()"
        
        $modified = $true
        Write-Host "Updated authUtils.queryAndCheckUser calls in: $($file.Name)" -ForegroundColor Yellow
    }
    
    # 确保有正确的import语句
    if ($modified) {
        # 检查是否需要添加 UserRestApi import
        if ($content -notmatch "import.*UserRestApi;") {
            $content = $content -replace "(package [^;]+;)", "`$1`n`nimport com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;"
        }
        
        # 检查是否需要添加 ApiResultI18n import
        if ($content -notmatch "import.*ApiResultI18n;") {
            $content = $content -replace "(import.*UserRestApi;)", "`$1`nimport com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;"
        }
    }
    
    # 如果内容有修改，写回文件
    if ($modified -and $content -ne $originalContent) {
        try {
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
            $modifiedCount++
            Write-Host "Modified file: $($file.Name)" -ForegroundColor Green
        }
        catch {
            Write-Host "Failed to modify file: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "Remaining auth calls fix completed! Total files modified: $modifiedCount" -ForegroundColor Green
