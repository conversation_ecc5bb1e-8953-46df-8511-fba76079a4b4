package com.cet.eem.fusion.maintenance.core.service.inspection.impl;

import com.cet.eem.fusion.common.service.auth.NodeCorrelationCheckService;
import com.cet.eem.bll.common.dao.powersystem.DeviceCommonInfoDao;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.fusion.maintenance.core.entity.po.InspectionParameter;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.InspectionSchemeWithSubLayer;
import com.cet.eem.fusion.maintenance.core.entity.bo.PlanSheetWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.InspectionParameterDao;
import com.cet.eem.fusion.maintenance.core.dao.InspectionSchemeDao;
import com.cet.eem.fusion.maintenance.core.dao.PlanSheetDao;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderDao;
import com.cet.eem.fusion.maintenance.core.dao.devicemanager.TechParamDao;
import com.cet.eem.fusion.maintenance.core.dao.devicemanager.TechParamValueDao;
import com.cet.eem.fusion.maintenance.core.dao.inspectrecordsheet.QueryTemplateDao;
import com.cet.eem.fusion.maintenance.core.def.ParameterTypeDef;
import com.cet.eem.fusion.maintenance.core.def.WorkSheetStatusDef;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.TechParamValue;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.TechParam;
import com.cet.eem.fusion.maintenance.core.model.plan.QueryInspectionPlanRequest;
import com.cet.eem.fusion.maintenance.core.model.workorder.MaintenanceContent;
import com.cet.eem.fusion.maintenance.core.model.workorder.OperationUser;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet.*;
import com.cet.eem.fusion.maintenance.core.service.device.DeviceManagerService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectRecordSheetService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.common.ErrorUtils;
import com.cet.eem.fusion.common.constant.ExcelType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.LoginDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;

import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.maintenance.service.EemModelDataService;
import com.cet.electric.modelservice.common.entity.IdTextPair;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.cet.eem.fusion.common.def.common.ContentTypeDef;

/**
 * @ClassName : InspectRecordSheetServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-10 17:23
 */
@Service
public class InspectRecordSheetServiceImpl implements InspectRecordSheetService {
    @Autowired
    WorkOrderDao workOrderDao;
    @Resource
UserRestApi userRestApi;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    QueryTemplateDao queryTemplateDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    PlanSheetDao planSheetDao;
    @Autowired
    NodeCorrelationCheckService nodeCorrelationCheckService;
    @Autowired
    InspectionSchemeDao inspectionSchemeDao;
    @Autowired
    InspectionParameterDao inspectionParameterDao;
    @Autowired
    DeviceManagerService deviceManagerService;
    @Autowired
    private TechParamValueDao techParamValueDao;
    @Autowired
    private TechParamDao techParamDao;
    @Autowired
    private DeviceCommonInfoDao deviceCommonInfoDao;
    @Autowired
    protected EemModelDataService eemModelDataService;

    protected static final Map<String, String> DEVICE_MAP = new HashMap<>();
    protected static final String FILE_NAME = "巡检记录表";
    protected static final String TITLE_NAME = "设备巡检记录";
    protected static final String DAY = "日";
    protected static final String MONTH = "月";
    protected static final String YEAR = "年";
    protected static final String INTERVAL = "-";
    protected static final String REASON_ENUM = "abnormalreason";
    protected static final Integer TYPE = 1;
    protected static final String TRUE = "正常";
    protected static final String FALSE = "异常";
    protected static final Set<String> LABELS = new HashSet<>();

    static {
        LABELS.addAll(Arrays.asList(NodeLabelDef.PROJECT, NodeLabelDef.ROOM, NodeLabelDef.CIVIC_PIPE, NodeLabelDef.BUILDING, NodeLabelDef.FLOOR));
    }

    static {
        DEVICE_MAP.put("status", "deviceenablestatus");
        DEVICE_MAP.put("devicetype", "devicetypeenum");
        DEVICE_MAP.put("operationstatus", "deviceoperationstatus");
        DEVICE_MAP.put("functiontype", "pumpfunctiontype");
        DEVICE_MAP.put("operationmediumtype", "pumpoperationmediumtype");
        DEVICE_MAP.put("physicaltype", "pumptype");
        DEVICE_MAP.put("engineattr", "coldwaterengineattr");
        DEVICE_MAP.put("colddrymachineattr", "aircompressorattr");
        DEVICE_MAP.put("dryingmachineattr", "aircompressorattr");
        DEVICE_MAP.put("fueltype", "energytype");

    }

    /**
     * 导出工单最大数量
     */
    @Value("${cet.eem.event.inspection.export-max-size: 10000}")
    private int exportMaxCount;

    @Override
    public ApiResult<List<InspectRecordSheetVo>> queryRecordSheet(RecordSheetQueryParam param, Long projectId) {
        //1.查巡检工单先
        if (CollectionUtils.isEmpty(param.getSchemeId())) {
            throw new ValidationException("未关联巡检方案");
        }
        InspectionSearchDto dto = createInspectionSearchDto(param);
        ApiResult<List<InspectionWorkOrderDto>> listResultWithTotal = workOrderDao.queryFinishWorkOrderByNode(dto, LoginDef.USER_ROOT, projectId,
                param.getNodes(), param.getSchemeId());
        List<InspectionWorkOrderDto> data = listResultWithTotal.getData();
        // 查询用户信息
        Map<Long, String> userMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        Map<Long, String> groupMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        authUtils.getUserAndGroupMsg(LoginDef.TENANT_ROOT, userMap, groupMap);
        List<InspectionParameter> parameters = getParameters(param.getSchemeId());
        return Result.ok(filterByNodes(data, groupMap, parameters), listResultWithTotal.getTotal());
    }

    /**
     * 查询全部的文件夹and模板
     *
     * @param projectId
     * @return
     */
    @Override
    public List<QueryTemplateWithNodeName> queryTemplate(Long projectId) {
        List<QueryTemplateWithLayer> queryTemplateWithLayers = queryTemplateDao.queryTemplate(projectId);
        List<QueryTemplateWithNodeName> queryTemplateWithNodeNames = assembleNodeName(queryTemplateWithLayers);
        assembleTechParamData(queryTemplateWithNodeNames);
        return queryTemplateWithNodeNames;
    }

    private List<QueryTemplateWithNodeName> assembleNodeName(List<QueryTemplateWithLayer> queryTemplateWithLayers) {
        //巡检获取模板的节点信息，查出来再拼回去
        Set<BaseVo> allNodes = queryTemplateWithLayers.stream()
                .filter(queryTemplateWithLayer -> CollectionUtils.isNotEmpty(queryTemplateWithLayer.getQueryTemplates()))
                .flatMap(queryTemplateWithLayer -> queryTemplateWithLayer.getQueryTemplates().stream())
                .flatMap(queryTemplate -> queryTemplate.getDetail().getNode().stream())
                .map(nodeIdLabel -> new BaseVo(nodeIdLabel.getId(), nodeIdLabel.getModelLabel())).collect(Collectors.toSet());
        List<BaseVo> baseVos = nodeDao.queryNodeName(allNodes);
        List<QueryTemplateWithNodeName> result = new ArrayList<>();
        assembleNodeName(queryTemplateWithLayers, baseVos, result);
        return result;
    }

    private void assembleNodeName(List<QueryTemplateWithLayer> layers, List<BaseVo> baseVos, List<QueryTemplateWithNodeName> result) {
        for (QueryTemplateWithLayer layerVo : layers) {
            QueryTemplateWithNodeName queryTemplateWithNodeName = new QueryTemplateWithNodeName();
            BeanUtils.copyProperties(layerVo, queryTemplateWithNodeName);
            result.add(queryTemplateWithNodeName);
            //模板要拼节点名称
            if (CollectionUtils.isEmpty(layerVo.getQueryTemplates())) {
                continue;
            }
            List<QueryTemplateVo> queryTemplates = new ArrayList<>();
            for (QueryTemplate queryTemplate : layerVo.getQueryTemplates()) {
                QueryTemplateVo queryTemplateVo = new QueryTemplateVo();
                assembleNodeName(queryTemplateVo, queryTemplate, baseVos);
                queryTemplates.add(queryTemplateVo);
            }
            queryTemplateWithNodeName.setQueryTemplates(queryTemplates);
        }
    }

    private void assembleTechParamData(List<QueryTemplateWithNodeName> queryTemplates) {
        if (CollectionUtils.isEmpty(queryTemplates)) {
            return;
        }
        Set<Long> idList = queryTemplates.stream().filter(queryTemplateWithNodeName -> CollectionUtils.isNotEmpty(queryTemplateWithNodeName.getQueryTemplates()))
                .flatMap(queryTemplateWithNodeName -> queryTemplateWithNodeName.getQueryTemplates().stream())
                .filter(queryTemplateVo -> CollectionUtils.isNotEmpty(queryTemplateVo.getDetail().getTechParamDataId()))
                .flatMap(queryTemplateVo -> queryTemplateVo.getDetail().getTechParamDataId().stream()).collect(Collectors.toSet());
        List<TechParamValue> techParamValues = techParamValueDao.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(techParamValues)) {
            return;
        }
        Set<Long> templateIds = techParamValues.stream().map(TechParamValue::getTechParamTemplateId).collect(Collectors.toSet());
        List<TechParam> techParams = techParamDao.selectBatchIds(templateIds);
        assembleParamDataName(techParamValues, techParams);
        for (QueryTemplateWithNodeName template : queryTemplates) {
            List<QueryTemplateVo> templateVos = template.getQueryTemplates();
            if (CollectionUtils.isEmpty(templateVos)) {
                continue;
            }
            for (QueryTemplateVo queryTemplateVo : templateVos) {
                List<Long> techParamDataId = queryTemplateVo.getDetail().getTechParamDataId();
                if (CollectionUtils.isEmpty(techParamDataId)) {
                    continue;
                }
                HashSet<Long> longs = new HashSet<>(techParamDataId);
                List<TechParamValue> values = techParamValues.stream().filter(techParamValue -> longs.contains(techParamValue.getId()))
                        .collect(Collectors.toList());
                TemplateDetailVo detail = queryTemplateVo.getDetail();
                detail.setTechParamValues(values);
            }
        }
    }

    private void assembleParamDataName(List<TechParamValue> paramValues, List<TechParam> techParams) {
        if (CollectionUtils.isEmpty(paramValues) || CollectionUtils.isEmpty(techParams)) {
            return;
        }
        for (TechParamValue paramValue : paramValues) {
            TechParam techParam1 = techParams.stream().filter(techParam -> Objects.equals(techParam.getId(), paramValue.getTechParamTemplateId()))
                    .findAny().orElse(new TechParam());
            paramValue.setName(techParam1.getName());
            if (Objects.isNull(paramValue.getValue()) && Objects.nonNull(paramValue.getNumber())) {
                paramValue.setValue(String.valueOf(paramValue.getNumber().intValue()));
            }
        }
    }

    private void assembleNodeName(QueryTemplateVo queryTemplateWithNodeName, QueryTemplate layerVo, List<BaseVo> baseVos) {
        BeanUtils.copyProperties(layerVo, queryTemplateWithNodeName);
        TemplateDetail detail = layerVo.getDetail();
        TemplateDetailVo detailVo = new TemplateDetailVo();
        BeanUtils.copyProperties(detail, detailVo);
        List<BaseVo> nodes = new ArrayList<>();
        for (NodeIdLabel node : detail.getNode()) {
            List<BaseVo> baseVoList = baseVos.stream().filter(baseVo -> Objects.equals(baseVo.getId(), node.getId())
                            && Objects.equals(baseVo.getModelLabel(), node.getModelLabel()))
                    .distinct().collect(Collectors.toList());
            nodes.addAll(baseVoList);
        }
        detailVo.setNode(nodes);
        queryTemplateWithNodeName.setDetail(detailVo);

    }

    @Override
    public QueryTemplate addQueryTemplate(AddQuickQueryTemplate addQuickQueryTemplate, Long projectId) {
        checkSameName(addQuickQueryTemplate.getParentTemplateId(), addQuickQueryTemplate.getTemplateName());
        QueryTemplate queryTemplate = new QueryTemplate();
        queryTemplate.setProjectId(projectId);
        queryTemplate.setName(addQuickQueryTemplate.getTemplateName());
        if (Objects.nonNull(addQuickQueryTemplate.getTemplateDetail())) {
            queryTemplate.setDetail(addQuickQueryTemplate.getTemplateDetail());
        }
        //没有父节点
        if (Objects.isNull(addQuickQueryTemplate.getParentTemplateId())) {
            Long insert = queryTemplateDao.insert(queryTemplate);
            queryTemplate.setId(insert);
            return queryTemplate;
        }
        List<Map<String, Object>> maps = queryTemplateDao.insertChild(addQuickQueryTemplate.getParentTemplateId(), Collections.singletonList(queryTemplate));
        List<QueryTemplate> queryTemplates = JsonTransferUtils.transferList(maps, QueryTemplate.class);
        return queryTemplates.get(0);
    }

    private void checkSameName(Long parentId, String name) {
        List<QueryTemplate> templates;
        if (Objects.isNull(parentId)) {
            List<QueryTemplate> queryTemplates = queryTemplateDao.querySameNameTemplate(null, name);
            //过滤模板
            templates = queryTemplates.stream().filter(queryTemplate -> Objects.isNull(queryTemplate.getDetail()))
                    .collect(Collectors.toList());
        } else {
            templates = queryTemplateDao.queryChildrenData(parentId, null, name);
        }
        if (CollectionUtils.isNotEmpty(templates)) {
            throw new ValidationException("输入名称重复！");
        }
    }

    @Override
    public QueryTemplate updateQueryTemplate(UpdateQuickQueryTemplate queryTemplate) {
        List<QueryTemplate> queryTemplates;
        if (Objects.isNull(queryTemplate.getDetail())) {
            //是文件夹
            queryTemplates = queryTemplateDao.querySameNameTemplate(queryTemplate.getId(), queryTemplate.getName());

        } else {
            //模板，一个文件夹下的模板名字不可以重复
            queryTemplates = queryTemplateDao.queryChildrenData(queryTemplate.getParentTemplateId(), queryTemplate.getId(), queryTemplate.getName());
        }
        if (CollectionUtils.isEmpty(queryTemplates)) {
            List<QueryTemplate> queryTemplates1 = modelServiceUtils.writeData(Collections.singletonList(queryTemplate), QueryTemplate.class);
            return queryTemplates1.get(0);
        }
        throw new ValidationException("输入名称重复！");
    }

    @Override
    public void deleteTemplateAndFolder(List<Long> id, Long projectId) {
        List<QueryTemplate> queryTemplates = queryTemplateDao.selectBatchIds(id);
        List<Long> ids = queryTemplates.stream().filter(queryTemplate -> Objects.isNull(queryTemplate.getDetail()))
                .map(QueryTemplate::getId).distinct().collect(Collectors.toList());
        Set<Long> childs = queryTemplates.stream().filter(queryTemplate -> Objects.nonNull(queryTemplate.getDetail()))
                .map(QueryTemplate::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(childs)) {
            List<QueryTemplateWithLayer> queryTemplateWithLayers = queryTemplateDao.queryTemplate(childs);
            //要把关系表的数据也删了，先区分这些id的内容
            for (QueryTemplateWithLayer queryTemplateWithLayer : queryTemplateWithLayers) {
                queryTemplateDao.deleteChild(queryTemplateWithLayer.getId(), queryTemplateWithLayer.getQueryTemplates());
            }
        }
        queryTemplateDao.deleteBatchIds(ids);
    }

    @Override
    public void exportRecordSheet(RecordSheetQueryParam param, HttpServletResponse response, Long projectId) {
        //查找设备属性
        Map<BaseVo, List<DeviceParamData>> deviceParamDataMap;
        List<Map<String, Object>> maps;
        //拼接节点和设备属性，属性字段展示根据传参来，有的设备没有该属性也打印,过滤管理层级
        if (CollectionUtils.isEmpty(param.getTemplateIds())) {
            //直接导出的情况
            maps = nodeDao.queryNodes(param.getNodes());
            deviceParamDataMap = assembleDeviceParamData(maps, param.getDeviceParam(), param.getNodes());
        } else {
            List<QueryTemplate> queryTemplates = queryTemplateDao.selectBatchIds(param.getTemplateIds());
            List<BaseVo> nodes = queryTemplates.stream().flatMap(queryTemplate -> queryTemplate.getDetail().getNode().stream())
                    .map(nodeIdLabel -> new BaseVo(nodeIdLabel.getId(), nodeIdLabel.getModelLabel()))
                    .distinct().collect(Collectors.toList());

            List<DeviceFieldData> deviceFieldData = queryTemplates.stream().flatMap(queryTemplate -> queryTemplate.getDetail().getDashboard().stream())
                    .distinct().collect(Collectors.toList());
            maps = nodeDao.queryNodes(nodes);
            deviceParamDataMap = assembleDeviceParamData(maps, deviceFieldData, nodes);
        }
        //查巡检数据  -拼接再返回啥的
        //处理page
        param.setPage(new Page(0, exportMaxCount));
        //查询巡检记录
        ApiResult<List<InspectRecordSheetVo>> inspectRecordSheetVoResultWithTotal = queryRecordSheet(param, projectId);
        //拼接节点和设备技术参数，根据技术参数传参id列表来，有的设备没有该技术参数也打印，过滤管理层级
        Map<BaseVo, List<TechParamValue>> paramValueMap = queryTechParamValueByNodes(param.getNodes(), param.getTechParamDataId());
        exportNodesFile(inspectRecordSheetVoResultWithTotal.getData(), response, param, deviceParamDataMap, param.getSchemeId(), paramValueMap);

    }

    /**
     * 拼接节点和计算参数的关系
     *
     * @param nodes
     * @param techParamDataId
     * @return
     */
    private Map<BaseVo, List<TechParamValue>> queryTechParamValueByNodes(List<BaseVo> nodes, List<Long> techParamDataId) {
        if (CollectionUtils.isEmpty(techParamDataId)) {
            return Collections.emptyMap();
        }
        //查询设备技术参数
        List<TechParamValue> techParamValues = techParamValueDao.selectBatchIds(techParamDataId);
        if (CollectionUtils.isEmpty(techParamValues)) {
            return Collections.emptyMap();
        }
        Map<BaseVo, List<TechParamValue>> result = new HashMap<>();
        Set<Long> templateIds = techParamValues.stream().map(TechParamValue::getTechParamTemplateId).collect(Collectors.toSet());
        Map<BaseVo, List<TechParamValue>> map = techParamValues.stream().collect(Collectors.groupingBy(techParamValue -> new BaseVo(techParamValue.getObjectId(), techParamValue.getObjectLabel())));
        //查询设备技术参数模板
        List<TechParam> techParams = techParamDao.selectBatchIds(templateIds);
        //拼接数据，有的节点没有技术参数也拼一下，不赋值
        List<TechParamValue> notEmpty = assembleParamDataName(techParamValues, techParams, true);
        List<TechParamValue> empty = assembleParamDataName(techParamValues, techParams, false);
        for (BaseVo baseVo : nodes) {
            List<TechParamValue> paramValues = map.get(new BaseVo(baseVo.getId(), baseVo.getModelLabel()));
            if (CollectionUtils.isEmpty(paramValues)) {
                result.put(baseVo, empty);
            } else {
                result.put(baseVo, notEmpty);
            }

        }
        return result;
    }

    private List<TechParamValue> assembleParamDataName(List<TechParamValue> paramValues, List<TechParam> techParams, Boolean flag) {
        if (CollectionUtils.isEmpty(paramValues) || CollectionUtils.isEmpty(techParams)) {
            return Collections.emptyList();
        }
        List<TechParamValue> result = new ArrayList<>();
        for (TechParamValue paramValue : paramValues) {
            TechParam techParam1 = techParams.stream().filter(techParam -> Objects.equals(techParam.getId(), paramValue.getTechParamTemplateId()))
                    .findAny().orElse(new TechParam());
            TechParamValue value = new TechParamValue();
            if (Boolean.FALSE.equals(flag)) {
                value.setValue(CommonUtils.BLANK_STR);
            } else {
                if (Objects.isNull(paramValue.getValue())) {
                    if (Objects.nonNull(paramValue.getNumber())) {
                        value.setValue(String.valueOf(paramValue.getNumber().intValue()));
                    } else {
                        value.setValue(CommonUtils.BLANK_STR);
                    }
                } else {
                    value.setValue(paramValue.getValue());
                }
            }
            value.setName(techParam1.getName());
            result.add(value);
        }
        return result;
    }

    @Override
    public List<BaseVo> queryInspectionNodeTree(List<BaseVo> nodes) {
        //查询全部的巡检计划关联的节点
        QueryInspectionPlanRequest queryInspectionPlanRequest = new QueryInspectionPlanRequest();
        queryInspectionPlanRequest.setPage(new Page(0, exportMaxCount));
        ApiResult<List<PlanSheetWithSubLayer>> listResultWithTotal = planSheetDao.queryInspectionPlanSheetSubLayerWithPage(queryInspectionPlanRequest);
        List<PlanSheetWithSubLayer> data = listResultWithTotal.getData();
        List<BaseVo> query = data.stream().filter(planSheetWithSubLayer -> CollectionUtils.isNotEmpty(planSheetWithSubLayer.getDevicePlanRelationshipList()))
                .flatMap(planSheetWithSubLayer -> planSheetWithSubLayer.getDevicePlanRelationshipList().stream())
                .map(devicePlanRelationship -> new BaseVo(devicePlanRelationship.getDeviceId(), devicePlanRelationship.getDeviceLabel()))
                .distinct().collect(Collectors.toList());
        nodeCorrelationCheckService.filterNodeTree(nodes, new HashSet<>(query));
        return nodes;
    }

    private void exportNodesFile(List<InspectRecordSheetVo> inspectRecordSheetVos, HttpServletResponse response, RecordSheetQueryParam param
            , Map<BaseVo, List<DeviceParamData>> deviceParamDataMap, List<Long> schemeId, Map<BaseVo, List<TechParamValue>> paramValueMap) {
        Set<BaseVo> baseVos = deviceParamDataMap.keySet();
        //拼接文件名字，多设备就叫巡检记录表-周期-时间，单设备前面就是设备名字
        String fileName = assembleFileName(inspectRecordSheetVos.size(), param.getCycle(), param.getStartTime(), new ArrayList<>(baseVos).get(0).getName());

        try (Workbook workBook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA)) {
            //巡检记录表名称A-日-2022/10/09
            Map<BaseVo, InspectRecordSheetVo> map = inspectRecordSheetVos.stream().collect(Collectors.toMap(InspectRecordSheetVo::getBaseVo, Function.identity()));
            //巡检记录的表头
            List<String> title;
            if (CollectionUtils.isEmpty(inspectRecordSheetVos)) {
                title = queryTitle(schemeId);
            } else {
                title = inspectRecordSheetVos.stream().map(InspectRecordSheetVo::getTitle)
                        .filter(CollectionUtils::isNotEmpty).findAny().orElse(new ArrayList<>());
            }
            //判断是合并导出还是分sheet页导出
            if (param.isMerge()) {
                mergeExportNodesFile(param, map, title, workBook);
            } else {
                //不合并的情况
                exportNodesFileBySheet(param, baseVos, map, title, workBook, deviceParamDataMap, paramValueMap);
            }
            FileUtils.downloadExcel(response, workBook, fileName, ContentTypeDef.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            ErrorUtils.exportError("巡检工单记录表", e);
        }
    }

    /**
     * 单设备导出，或多设备分sheet页导出
     *
     * @param param
     * @param baseVos
     * @param map
     * @param title
     * @param workBook
     * @param deviceParamDataMap
     * @param paramValueMap
     */
    private void exportNodesFileBySheet(RecordSheetQueryParam param, Set<BaseVo> baseVos, Map<BaseVo, InspectRecordSheetVo> map, List<String> title, Workbook workBook
            , Map<BaseVo, List<DeviceParamData>> deviceParamDataMap, Map<BaseVo, List<TechParamValue>> paramValueMap) {
        for (BaseVo node : param.getNodes()) {
            BaseVo baseVo = baseVos.stream().filter(baseVo1 -> Objects.equals(baseVo1.getModelLabel(), node.getModelLabel()) &&
                    Objects.equals(baseVo1.getId(), node.getId())).findFirst().orElse(new BaseVo());
            //分组拿到巡检记录信息
            InspectRecordSheetVo inspectRecordSheetVos1 = map.get(baseVo);
            String sheetName = baseVo.getName();
            List<InspectRecordSheetDto> inspectionWorkOrderDtos = new ArrayList<>();
            if (Objects.nonNull(inspectRecordSheetVos1) && CollectionUtils.isNotEmpty(inspectRecordSheetVos1.getInspectionWorkOrderDtos())) {
                inspectionWorkOrderDtos = inspectRecordSheetVos1.getInspectionWorkOrderDtos();

            }
            //获取设备属性参数的数量和设备技术参数的数量，用于设置列宽
            int devicedatasize = CollectionUtils.isEmpty(deviceParamDataMap.get(baseVo)) ? 0 : deviceParamDataMap.get(baseVo).size();
            int techsize = CollectionUtils.isEmpty(paramValueMap.get(new BaseVo(node.getId(), node.getModelLabel()))) ? 0 : paramValueMap.get(new BaseVo(node.getId(), node.getModelLabel())).size();
            List<Integer> colWidth = createColWidth(title.size(), inspectionWorkOrderDtos.size(), param, devicedatasize, techsize);
            List<InspectRecordSheetDto> finalInspectionWorkOrderDtos = inspectionWorkOrderDtos;
            PoiExcelUtils.createSheet(workBook, sheetName, (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;
                //节点名称和时间信息--格子长度根据巡检记录表格长度决定
                writeMiddleData(rowNum++, sheet, baseCellStyle, baseVo, param.getStartTime(), param.getCycle(), colWidth.size(), workBook, param.isMerge());
                //设备数据--没有的不用加空行，有设备信息的要分开
                rowNum = writeDeviceData(rowNum, sheet, baseCellStyle, deviceParamDataMap.get(baseVo), paramValueMap.get(new BaseVo(node.getId(), node.getModelLabel())));
                List<Row> rowList = writeHeaderTransverse(sheet, baseCellStyle, rowNum++, title, param);
                //巡检记录信息
                if (Objects.equals(param.getType(), 0)) {
                    writeRecordSheet(rowNum, sheet, baseCellStyle, finalInspectionWorkOrderDtos, workBook, param.isMerge());
                } else {
                    writeRecordSheetVertical(rowList, baseCellStyle, finalInspectionWorkOrderDtos, workBook, param.isMerge());
                }
            }, colWidth);
        }
    }

    /**
     * 合并导出
     *
     * @param param
     * @param map
     * @param title
     * @param workBook
     */
    private void mergeExportNodesFile(RecordSheetQueryParam param, Map<BaseVo, InspectRecordSheetVo> map, List<String> title, Workbook workBook) {
        List<InspectRecordSheetDto> inspectRecordSheetDtosWithBase = MergeBaseToinspectRecordSheet(param, map);
        String sheetName = FILE_NAME;
        //设置列宽
        List<Integer> colWidth = createColWidth(title.size(), inspectRecordSheetDtosWithBase.size(), param, 0, 0);
        PoiExcelUtils.createSheet(workBook, sheetName, (sheet, baseCellStyle, rowIndex) -> {
            int rowNum = 0;
            //节点名称和时间信息--格子长度根据巡检记录表格长度决定
            writeMiddleData(rowNum++, sheet, baseCellStyle, new BaseVo(), param.getStartTime(), param.getCycle(), colWidth.size(), workBook, param.isMerge());
            List<Row> rowList = writeHeaderTransverse(sheet, baseCellStyle, rowNum++, title, param);
            //巡检记录信息
            if (Objects.equals(param.getType(), 0)) {
                writeRecordSheet(rowNum, sheet, baseCellStyle, inspectRecordSheetDtosWithBase, workBook, param.isMerge());
            } else {
                writeRecordSheetVertical(rowList, baseCellStyle, inspectRecordSheetDtosWithBase, workBook, param.isMerge());
            }
        }, colWidth);
    }

    /**
     * 把设备信息添加到巡检记录表中
     *
     * @param param
     * @param map
     * @return
     */
    private List<InspectRecordSheetDto> MergeBaseToinspectRecordSheet(RecordSheetQueryParam param, Map<BaseVo, InspectRecordSheetVo> map) {
        List<InspectRecordSheetDto> inspectRecordSheetDtosWithBase = new ArrayList<>();
        for (BaseVo baseVo : param.getNodes()) {
            InspectRecordSheetVo inspectRecordSheetVos1 = map.get(baseVo);
            List<InspectRecordSheetDto> inspectionWorkOrderDtos = new ArrayList<>();
            if (Objects.nonNull(inspectRecordSheetVos1) && CollectionUtils.isNotEmpty(inspectRecordSheetVos1.getInspectionWorkOrderDtos())) {
                inspectionWorkOrderDtos = inspectRecordSheetVos1.getInspectionWorkOrderDtos();
            }
            //巡检记录信息中加入巡检设备的信息
            for (InspectRecordSheetDto inspectRecordSheetDto : inspectionWorkOrderDtos) {
                inspectRecordSheetDto.setBaseVo(baseVo);
            }
            inspectRecordSheetDtosWithBase.addAll(inspectionWorkOrderDtos);
        }
        return inspectRecordSheetDtosWithBase.stream().sorted(Comparator.comparing(InspectRecordSheetDto::getFinishTime)).collect(Collectors.toList());
    }

    /**
     * 纵向导出时写入巡检记录表数据
     *
     * @param rowList
     * @param baseCellStyle
     * @param inspectionWorkOrderDtos
     * @param workbook
     * @param merge
     */
    private void writeRecordSheetVertical(List<Row> rowList, CellStyle baseCellStyle, List<InspectRecordSheetDto> inspectionWorkOrderDtos, Workbook workbook, boolean merge) {
        if (CollectionUtils.isEmpty(inspectionWorkOrderDtos)) {
            return;
        }
        int col = 1;
        //列遍历，每列写入一条巡检记录表数据
        for (InspectRecordSheetDto item : inspectionWorkOrderDtos) {
            int i = 0;
            if (Objects.nonNull(item.getFinishTime())) {
                PoiExcelUtils.createCell(rowList.get(i++), col, baseCellStyle, TimeUtil.format(item.getFinishTime(), TimeUtil.YYYY_MM_DD_HH_MM));
            } else {
                PoiExcelUtils.createCell(rowList.get(i++), col, baseCellStyle, CommonUtils.BLANK_STR);
            }
            //如果合并导出，则写入设备名称
            if (merge) {
                PoiExcelUtils.createCell(rowList.get(i++), col, baseCellStyle, item.getBaseVo().getName());
            }
            for (InspectionSchemeDetail detail : item.getInspectParams()) {
                CellStyle style = chooseCellStyle(baseCellStyle, detail, workbook);
                PoiExcelUtils.createCell(rowList.get(i++), col, style, assembleParamValueByType(detail));
            }
            PoiExcelUtils.createCell(rowList.get(i++), col, baseCellStyle, item.getReason());
            PoiExcelUtils.createCell(rowList.get(i++), col, baseCellStyle, item.getUserList());
            PoiExcelUtils.createCell(rowList.get(i), col++, baseCellStyle, item.getTeamName());

        }

    }

    /**
     * 待巡检的工单
     *
     * @param sheet
     * @param baseCellStyle
     * @param startRow
     */
    private List<Row> writeHeaderTransverse(Sheet sheet, CellStyle baseCellStyle, int startRow, List<String> titles, RecordSheetQueryParam param) {
        List<Row> rowList = new ArrayList<>();
        List<String> allTitle = new ArrayList<>();
        allTitle.add("时间");
        if (param.isMerge()) {
            allTitle.add("设备");
        }
        allTitle.addAll(titles);
        allTitle.add("异常情况说明");
        allTitle.add("巡检人");
        allTitle.add("巡检班组");
        if (Objects.equals(param.getType(), 0)) {
            PoiExcelUtils.createHeaderName(sheet, startRow, allTitle, baseCellStyle);
            return Collections.emptyList();
        } else {
            for (String title : allTitle) {
                Row row = PoiExcelUtils.createRow(sheet, startRow++);
                rowList.add(row);
                PoiExcelUtils.createCell(row, 0, baseCellStyle, title);
            }
        }
        return rowList;
    }

    private void writeMiddleData(int rowNum, Sheet sheet, CellStyle baseCellStyle, BaseVo baseVo, LocalDateTime st, Integer cycle, int size, Workbook workbook, boolean merge) {
        Row row = PoiExcelUtils.createRow(sheet, rowNum);
        row.setHeight((short) 800);
        int col = 0;
        String day = st.getYear() + "年";
        if (Objects.equals(cycle, AggregationCycle.ONE_MONTH)) {
            day = day + st.getMonthValue() + "月";
        } else if (Objects.equals(cycle, AggregationCycle.ONE_DAY)) {
            day = day + st.getMonthValue() + "月" + st.getDayOfMonth() + "日";
        }
        if (merge) {
            PoiExcelUtils.createCell(row, col, createMiddleStyle(workbook, baseCellStyle), FILE_NAME + "    " + day);
        } else {
            PoiExcelUtils.createCell(row, col, createMiddleStyle(workbook, baseCellStyle), baseVo.getName() + TITLE_NAME + "    " + day);
        }
        //合并单元格的数量必须大于等于2个
        if (size <= 1) {
            size = 2;
        }
        //合并单元格
        CellRangeAddress region = new CellRangeAddress(rowNum, rowNum, 0, size - 1);
        sheet.addMergedRegion(region);
        RegionUtil.setBorderBottom(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderTop(BorderStyle.THIN, region, sheet);
    }

    public static CellStyle createRequiredStyle(Workbook workbook, CellStyle baseCellStyle) {
        CellStyle requiredCellStyle = workbook.createCellStyle();
        requiredCellStyle.cloneStyleFrom(baseCellStyle);
        Font font = PoiExcelUtils.createFont(workbook, true, null, null, HSSFColor.HSSFColorPredefined.RED.getIndex());
        requiredCellStyle.setFont(font);
        return requiredCellStyle;
    }

    public static CellStyle createMiddleStyle(Workbook workbook, CellStyle baseCellStyle) {
        CellStyle requiredCellStyle = workbook.createCellStyle();
        requiredCellStyle.cloneStyleFrom(baseCellStyle);
        Font font = PoiExcelUtils.createFont(workbook, true, null, 15, HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        requiredCellStyle.setFont(font);
        return requiredCellStyle;
    }

    /**
     * 巡检记录数据横向导出
     *
     * @param rowNum
     * @param sheet
     * @param baseCellStyle
     * @param inspectionWorkOrderDtos
     * @param workbook
     * @param merge
     */
    private void writeRecordSheet(int rowNum, Sheet sheet, CellStyle baseCellStyle, List<InspectRecordSheetDto> inspectionWorkOrderDtos, Workbook workbook, boolean merge) {

        int col;
        if (CollectionUtils.isEmpty(inspectionWorkOrderDtos)) {
            return;
        }
        //行遍历，每行写入一条巡检记录表信息
        for (InspectRecordSheetDto item : inspectionWorkOrderDtos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            if (Objects.nonNull(item.getFinishTime())) {
                PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getFinishTime(), TimeUtil.YYYY_MM_DD_HH_MM));
            } else {
                PoiExcelUtils.createCell(row, col++, baseCellStyle, CommonUtils.BLANK_STR);
            }
            if (merge) {
                PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getBaseVo().getName());
            }
            for (InspectionSchemeDetail detail : item.getInspectParams()) {
                CellStyle style = chooseCellStyle(baseCellStyle, detail, workbook);
                PoiExcelUtils.createCell(row, col++, style, assembleParamValueByType(detail));
            }
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getReason());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getUserList());
            PoiExcelUtils.createCell(row, col, baseCellStyle, item.getTeamName());
            rowNum++;
        }
    }

    private CellStyle chooseCellStyle(CellStyle baseCellStyle, InspectionSchemeDetail detail, Workbook workbook) {
        if (Objects.equals(TYPE, detail.getType())) {
            if (Objects.nonNull(detail.getStatus()) && Boolean.FALSE.equals(detail.getStatus())) {
                return createRequiredStyle(workbook, baseCellStyle);
            }
        } else {
            if (Objects.nonNull(detail.getValue()) && (detail.getValue() < detail.getMin() || detail.getValue() > detail.getMax())) {
                return createRequiredStyle(workbook, baseCellStyle);
            }
        }
        return baseCellStyle;
    }

    private String assembleParamValueByType(InspectionSchemeDetail detail) {
        String value = CommonUtils.BLANK_STR;
        if (Objects.equals(ParameterTypeDef.STATUS_QUANTITY, detail.getType())) {
            if (Objects.nonNull(detail.getStatus()) && Boolean.TRUE.equals(detail.getStatus())) {
                value = TRUE;
            } else if (Objects.nonNull(detail.getStatus()) && Boolean.FALSE.equals(detail.getStatus())) {
                value = FALSE;
            }

        } else if (Objects.equals(ParameterTypeDef.ANALOG_QUANTITY, detail.getType())) {
            if (Objects.nonNull(detail.getValue())) {
                value = detail.getValue().toString();
            }
        } else if (Objects.equals(ParameterTypeDef.TEXT_QUANTITY, detail.getType())) {
            if (Objects.nonNull(detail.getTextValue())) {
                value = detail.getTextValue();
            }
        }
        return value;
    }

    /**
     * 拼接设备信息,按行合并--
     *
     * @param sheet
     * @param baseCellStyle
     * @param data
     * @return
     */
    private int writeDeviceData(int rowStart, Sheet sheet, CellStyle baseCellStyle, List<DeviceParamData> data, List<TechParamValue> techParamValues) {
        int col;
        if (CollectionUtils.isEmpty(data) && CollectionUtils.isEmpty(techParamValues)) {
            return rowStart;
        }

        int size = data.size();
        //3是每一行只写3个属性，和界面展示效果对应
        int rowNum = size / 3;
        if (!Objects.equals(3 * rowNum, size)) {
            rowNum = rowNum + 1;
        }
        //写入设备基础属性
        for (int i = 0; i < rowNum; i++) {
            Row row = PoiExcelUtils.createRow(sheet, i + rowStart);
            col = 0;
            for (int j = 0; j < 3; j++) {
                if (3 * i + 1 + j <= size) {
                    PoiExcelUtils.createCell(row, col++, baseCellStyle, data.get(3 * i + j).getParamName());
                    PoiExcelUtils.createCell(row, col++, baseCellStyle, data.get(3 * i + j).getValue());
                }
            }
        }
        if (CollectionUtils.isEmpty(techParamValues)) {
            return rowNum + rowStart + 1;
        }
        //判断是从几行几列开始写，设备属性可能没写完3个
        int startCol = 0;
        if (!Objects.equals(3 * rowNum, size)) {
            startCol = (size - 3 * (rowNum - 1)) * 2;
        }
        return writeTechParamData(rowNum, sheet, baseCellStyle, techParamValues, startCol);
    }

    /**
     * 填写设备技术参数
     *
     * @param rowStart
     * @param sheet
     * @param baseCellStyle
     * @param data
     * @param startCol
     * @return
     */
    private int writeTechParamData(int rowStart, Sheet sheet, CellStyle baseCellStyle, List<TechParamValue> data, int startCol) {
        if (CollectionUtils.isEmpty(data)) {
            return rowStart + 1;
        }
        int number = 0;
        int size = data.size();
        //如果传来的列数不是从0开始的情况，补充那一行
        if (!Objects.equals(startCol, 0)) {
            //计算缺几个
            number = 3 - startCol / 2;
            for (int m = 0; m < number; m++) {
                Row row = PoiExcelUtils.createRow(sheet, rowStart);
                if (m + 1 <= size) {
                    PoiExcelUtils.createCell(row, startCol++, baseCellStyle, data.get(m).getName());
                    PoiExcelUtils.createCell(row, startCol++, baseCellStyle, data.get(m).getValue());
                }
            }

        }
        //换行
        rowStart = rowStart + 1;
        int col;
        int rowNum = (size - number) / 3;
        if (!Objects.equals(3 * rowNum, (size - number))) {
            rowNum = rowNum + 1;
        }
        //其他逻辑和写设备技术参数差不多
        for (int i = 0; i < rowNum; i++) {
            Row row = PoiExcelUtils.createRow(sheet, i + rowStart);
            col = 0;
            for (int j = 0; j < 3; j++) {
                if (3 * i + 1 + j + number <= size) {
                    PoiExcelUtils.createCell(row, col++, baseCellStyle, data.get(3 * i + j + number).getName());
                    PoiExcelUtils.createCell(row, col++, baseCellStyle, data.get(3 * i + j + number).getValue());
                }
            }
        }
        return rowNum + rowStart + 1;
    }

    /**
     * 设置列宽
     *
     * @param titleSize
     * @param inspectionsize
     * @param param
     * @return
     */
    private List<Integer> createColWidth(int titleSize, int inspectionsize, RecordSheetQueryParam param, int devicedatasize, int techparamsize) {
        List<Integer> colWidth = new ArrayList<>();
        int devicesize = 0;
        //计算设备台账信息所占列数
        if ((devicedatasize + techparamsize) >= 3) {
            devicesize = 6;
        } else {
            devicesize = (devicedatasize + techparamsize) * 2;
        }
        if (Objects.equals(param.getType(), 0)) {
            //横向导出，参数有4列是固定的
            for (int i = 0; i < titleSize + 4; i++) {
                colWidth.add(20);
            }
            if (param.isMerge()) {
                colWidth.add(20);
            }
        } else {
            //纵向导出时，如果是合并导出或者没有设备台账信息，直接按照巡检记录数据量设置列宽数
            if (param.isMerge() || devicesize == 0) {
                for (int i = 0; i < inspectionsize + 1; i++) {
                    colWidth.add(20);
                }
            } else {
                //纵向导出时，如果有设备台账信息，则根据台账信息列数和巡检记录数据列数中多的来设置列宽数
                for (int i = 0; i < devicesize; i++) {
                    colWidth.add(20);
                }
                if (inspectionsize + 1 > devicesize) {
                    for (int i = 0; i < inspectionsize - devicesize - 1; i++) {
                        colWidth.add(20);
                    }
                }
            }
        }
        return colWidth;
    }

    /**
     * 多设备
     *
     * @param size
     * @param cycle
     * @param startTime
     * @param nodeName
     * @return
     */
    private String assembleFileName(Integer size, Integer cycle, LocalDateTime startTime, String nodeName) {
        String device = FILE_NAME;
        if (size <= 1) {
            device = nodeName;
        }
        String cycleName = "";
        String time = "";
        if (Objects.equals(cycle, AggregationCycle.ONE_YEAR)) {
            cycleName = YEAR;
            time = TimeUtil.format(startTime, TimeUtil.YEAR_TIME_FORMAT);
        } else if (Objects.equals(cycle, AggregationCycle.ONE_MONTH)) {
            cycleName = MONTH;
            time = TimeUtil.format(startTime, TimeUtil.YYYY_MM);
        } else {
            cycleName = DAY;
            time = TimeUtil.format(startTime, TimeUtil.YYYY_MM_DD);
        }
        return device + INTERVAL + cycleName + INTERVAL + time;

    }

    /**
     * 处理设备属性的数据
     *
     * @param maps
     * @param deviceParam
     * @param baseVos
     * @return
     */
    private Map<BaseVo, List<DeviceParamData>> assembleDeviceParamData(List<Map<String, Object>> maps, List<DeviceFieldData> deviceParam, List<BaseVo> baseVos) {
        Map<BaseVo, List<DeviceParamData>> result = new HashMap<>();
        for (BaseVo baseVo : baseVos) {
            //设备才需要属性
            if (LABELS.contains(baseVo.getModelLabel())) {
                result.put(baseVo, Collections.emptyList());
                continue;
            }
            for (Map<String, Object> map : maps) {
                boolean equals = Objects.equals(map.get(ColumnDef.ID).toString(), baseVo.getId().toString());
                boolean b = Objects.equals(map.get(ColumnDef.MODEL_LABEL), baseVo.getModelLabel());
                if (equals && b) {
                    List<DeviceParamData> deviceParamData = handleDeviceParamData(map, deviceParam);
                    baseVo.setName(map.get(ColumnDef.NAME).toString());
                    result.put(baseVo, deviceParamData);
                }
            }
        }
        return result;
    }

    /**
     * 处理设备属性数据
     *
     * @param map
     * @param deviceParam
     * @return
     */
    private List<DeviceParamData> handleDeviceParamData(Map<String, Object> map, List<DeviceFieldData> deviceParam) {
        List<DeviceParamData> result = new ArrayList<>();
        for (DeviceFieldData data : deviceParam) {
            Object obj = map.get(data.getName());
            DeviceParamData paramData = new DeviceParamData();
            BeanUtils.copyProperties(data, paramData);
            if (Objects.nonNull(obj)) {
                paramData.setValue(obj.toString());
                handleParamValue(obj, paramData, data.getName());
            } else {
                paramData.setValue(CommonUtils.BLANK_STR);
            }
            result.add(paramData);
        }
        return result;
    }

    private void handleParamValue(Object param, DeviceParamData paramData, String key) {

        if (param instanceof Integer) {
            //查枚举
            Integer value = (Integer) param;
            assembleEnumData(key, value, paramData);

        } else if (param instanceof Long) {
            //日期
            Long l = (Long) param;
            String format = TimeUtil.format(l, TimeUtil.YYYY_MM_DD);
            paramData.setValue(format);
        } else {
            paramData.setValue(param.toString());
        }
    }

    private void assembleEnumData(String key, Integer value, DeviceParamData paramData) {
        Set<String> strings = DEVICE_MAP.keySet();
        ApiResult<List<IdTextPair>> enumrationByModel;
        if (strings.contains(key)) {
            String s = DEVICE_MAP.get(key);
            enumrationByModel = eemModelDataService.getEnumrationByModel(s);
        } else {
            enumrationByModel = eemModelDataService.getEnumrationByModel(key);
        }
        List<IdTextPair> data = enumrationByModel.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            IdTextPair item = data.stream().filter(enumItem -> Objects.equals(enumItem.getId(), value)).findAny().orElse(new IdTextPair());
            paramData.setValue(item.getText());
        }
    }

    /**
     * 转换查询条件
     *
     * @param param
     * @return
     */
    private InspectionSearchDto createInspectionSearchDto(RecordSheetQueryParam param) {
        InspectionSearchDto dto = new InspectionSearchDto();
        dto.setWorkSheetStatus(WorkSheetStatusDef.ACCOMPLISHED);
        dto.setPage(param.getPage());

        dto.setStartTime(param.getStartTime());
        if (CollectionUtils.isNotEmpty(param.getTemplateIds())) {
            List<QueryTemplate> queryTemplates = queryTemplateDao.selectBatchIds(param.getTemplateIds());
            List<Long> ids = queryTemplates.stream().flatMap(queryTemplate -> queryTemplate.getDetail().getInspectionSchemeId().stream())
                    .distinct().collect(Collectors.toList());
            param.setTemplateIds(ids);
            List<BaseVo> node = queryTemplates.stream().flatMap(queryTemplate -> queryTemplate.getDetail().getNode().stream())
                    .map(nodeIdLabel -> new BaseVo(nodeIdLabel.getId(), nodeIdLabel.getModelLabel())).distinct().collect(Collectors.toList());
            param.setNodes(node);
            param.setCycle(queryTemplates.get(0).getDetail().getCycle());
        }
        dto.setEndTime(TimeUtil.addDateTimeByCycle(dto.getStartTime(), param.getCycle(), 1));
        return dto;
    }

    /**
     * 拼接数据
     *
     * @param data
     * @param groupMap
     * @return
     */
    private InspectRecordSheetVo handleRecordSheetData(List<InspectionWorkOrderDto> data, Map<Long, String> groupMap, List<InspectionParameter> parameters) {
        InspectRecordSheetVo inspectRecordSheetVo = new InspectRecordSheetVo();
        //按节点分组先
        List<InspectRecordSheetDto> dtoList = new ArrayList<>();
        for (InspectionWorkOrderDto dto : data) {
            InspectRecordSheetDto inspectRecordSheetDto = new InspectRecordSheetDto();
            inspectRecordSheetDto.setReason(dto.getHandleDescription());
            inspectRecordSheetDto.setFinishTime(dto.getFinishTime());
            inspectRecordSheetDto.setTeamId(dto.getTeamId());
            MaintenanceContent maintenanceContent = JsonTransferUtils.parseObject(dto.getMaintenanceContent(), MaintenanceContent.class);
            if (maintenanceContent == null) {
                maintenanceContent = new MaintenanceContent();
            }
            inspectRecordSheetDto.setInspectParams(maintenanceContent.getInspectParams());
            inspectRecordSheetDto.setUsers(maintenanceContent.getUsers());
            inspectRecordSheetDto.setTeamName(groupMap.get(dto.getTeamId()));
            dtoList.add(inspectRecordSheetDto);
        }
        handleRecordDetail(dtoList, inspectRecordSheetVo, parameters);
        return inspectRecordSheetVo;
    }

    @Override
    public List<InspectRecordSheetVo> filterByNodes(List<InspectionWorkOrderDto> data, Map<Long, String> groupMap, List<InspectionParameter> parameters) {
        Set<BaseVo> nodes = new HashSet<>();
        for (InspectionWorkOrderDto dto : data) {
            List<DevicePlanRelationship> devicePlanRelationshipList = dto.getDevicePlanRelationshipList();
            if (CollectionUtils.isNotEmpty(devicePlanRelationshipList)) {
                BaseVo node = new BaseVo(devicePlanRelationshipList.get(0).getDeviceId(), devicePlanRelationshipList.get(0).getDeviceLabel());
                dto.setNode(node);
                nodes.add(node);

            }
        }
        List<BaseVo> baseVos = nodeDao.queryNodeName(nodes);
        Map<BaseVo, List<InspectionWorkOrderDto>> map = data.stream().collect(Collectors.groupingBy(InspectionWorkOrderDto::getNode));
        List<InspectRecordSheetVo> result = new ArrayList<>();
        for (BaseVo baseVo : baseVos) {
            InspectRecordSheetVo inspectRecordSheetVo = handleRecordSheetData(map.get(baseVo), groupMap, parameters);
            inspectRecordSheetVo.setBaseVo(baseVo);
            result.add(inspectRecordSheetVo);
        }

        return result;
    }

    @Override
    public List<String> queryTitle(List<Long> schemeId) {
        List<InspectionParameter> inspectionParameters = getParameters(schemeId);
        if (CollectionUtils.isEmpty(inspectionParameters)) {
            return Collections.emptyList();
        }
        return inspectionParameters.stream().sorted(Comparator.comparing(InspectionParameter::getId)).map(InspectionParameter::getName).collect(Collectors.toList());
    }

    private List<InspectionParameter> getParameters(List<Long> schemeId) {
        List<InspectionSchemeWithSubLayer> query = inspectionSchemeDao.query(schemeId);
        if (CollectionUtils.isEmpty(query)) {
            return Collections.emptyList();
        }
        List<Long> paramIds = query.stream().filter(inspectionSchemeWithSubLayer -> CollectionUtils.isNotEmpty(inspectionSchemeWithSubLayer.getSchemeDetails()))
                .flatMap(inspectionSchemeWithSubLayer -> inspectionSchemeWithSubLayer.getSchemeDetails().stream()).map(InspectionSchemeDetail::getInspectionParameterId)
                .distinct().collect(Collectors.toList());
        List<InspectionParameter> inspectionParameters = inspectionParameterDao.selectBatchIds(paramIds);
        if (CollectionUtils.isEmpty(inspectionParameters)) {
            return Collections.emptyList();
        }
        return inspectionParameters;
    }

    @Override
    public List<InspectionScheme> queryInspectionSchemeByNodes(List<BaseVo> nodes) {
        //查询巡检计划
        List<PlanSheetWithSubLayer> planSheetWithSubLayers = planSheetDao.queryPlanSheet(GlobalInfoUtils.getTenantId());
        if (CollectionUtils.isEmpty(planSheetWithSubLayers)) {
            return Collections.emptyList();
        }
        Set<BaseVo> baseVos = nodes.stream().map(baseVo -> new BaseVo(baseVo.getId(), baseVo.getModelLabel())).collect(Collectors.toSet());
        //根据节点找到对应的巡检方案，多节点取并集
        Map<BaseVo, Set<Long>> nodeSchemeMap = new HashMap<>();
        for (PlanSheetWithSubLayer layer : planSheetWithSubLayers) {
            if (CollectionUtils.isNotEmpty(layer.getDevicePlanRelationshipList())) {
                Set<BaseVo> node = layer.getDevicePlanRelationshipList().stream().map(devicePlanRelationship -> new BaseVo(devicePlanRelationship.getDeviceId(), devicePlanRelationship.getDeviceLabel()))
                        .collect(Collectors.toSet());
                Set<BaseVo> baseVoSet = node.stream().filter(baseVos::contains).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(baseVoSet)) {
                    putNodesWithSchemeId(nodeSchemeMap, baseVoSet, layer.getInspectionSchemeId());
                }
            }
        }
        if (MapUtils.isEmpty(nodeSchemeMap)) {
            return Collections.emptyList();
        }
        Set<Long> ids = assembleIntersectionSchemeId(baseVos, nodeSchemeMap);
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return inspectionSchemeDao.selectBatchIds(ids);
    }

    /**
     * 构建一个map
     *
     * @param nodeSchemeMap
     * @param baseVoSet
     * @param id
     */
    private void putNodesWithSchemeId(Map<BaseVo, Set<Long>> nodeSchemeMap, Set<BaseVo> baseVoSet, Long id) {
        for (BaseVo baseVo : baseVoSet) {
            Set<Long> longs = nodeSchemeMap.get(baseVo);
            if (CollectionUtils.isEmpty(longs)) {
                longs = new HashSet<>();
                longs.add(id);
                nodeSchemeMap.put(baseVo, longs);
            } else {
                longs.add(id);
            }
        }
    }

    /**
     * 找巡检方案id交集
     *
     * @param baseVos
     * @param nodeSchemeMap
     * @return
     */
    private Set<Long> assembleIntersectionSchemeId(Set<BaseVo> baseVos, Map<BaseVo, Set<Long>> nodeSchemeMap) {
        HashSet<Long> ids = new HashSet<>();
        for (BaseVo baseVo : baseVos) {
            Set<Long> longs = nodeSchemeMap.get(baseVo);
            if (CollectionUtils.isEmpty(longs)) {
                return Collections.emptySet();
            }
            if (CollectionUtils.isEmpty(ids)) {
                ids.addAll(longs);
            } else {
                ids.retainAll(longs);
                //只要有一次取交集为空就返回
                if (CollectionUtils.isEmpty(ids)) {
                    return Collections.emptySet();
                }
            }
        }
        return ids;
    }

    @Override
    public List<TechParamValue> queryNodeParamInfo(List<BaseVo> baseVo) {
        if (CollectionUtils.isEmpty(baseVo)) {
            return Collections.emptyList();
        }
        //根据节点查询设备技术参数
        List<TechParamValue> techParamValues = techParamValueDao.queryTechParam(baseVo);
        if (CollectionUtils.isEmpty(techParamValues)) {
            return Collections.emptyList();
        }
        //过滤出模板id
        Set<Long> templateIds = techParamValues.stream().map(TechParamValue::getTechParamTemplateId).collect(Collectors.toSet());
        //查询模板详情
        List<TechParam> techParams = techParamDao.selectBatchIds(templateIds);
        for (TechParamValue paramValue : techParamValues) {
            TechParam techParam1 = techParams.stream().filter(techParam -> Objects.equals(techParam.getId(), paramValue.getTechParamTemplateId()))
                    .findAny().orElse(new TechParam());
            paramValue.setName(techParam1.getName());
            //拼接名称，兼容以前写入的内容number字段，赋值到value字段
            if (Objects.isNull(paramValue.getValue()) && Objects.nonNull(paramValue.getNumber())) {
                paramValue.setValue(String.valueOf(paramValue.getNumber().intValue()));
            }
        }
        return techParamValues;
    }

    private void handleRecordDetail(List<InspectRecordSheetDto> dtoList,
                                    InspectRecordSheetVo inspectRecordSheetVo, List<InspectionParameter> parameters) {
        //获得巡检详情id列表，然后获得paramName列表。顺便处理用户信息和原因信息
        Set<Long> paramIds = parameters.stream().map(InspectionParameter::getId).sorted(Comparator.comparing(Long::longValue)).collect(Collectors.toCollection(LinkedHashSet::new));
        List<String> strings = parameters.stream().sorted(Comparator.comparing(InspectionParameter::getId)).map(InspectionParameter::getName).collect(Collectors.toList());
        inspectRecordSheetVo.setTitle(strings);
        inspectRecordSheetVo.setInspectionWorkOrderDtos(dtoList);
        for (InspectRecordSheetDto dto : dtoList) {
            getAbnormalReason(dto);
            List<InspectionSchemeDetail> dtoInspectParams = dto.getInspectParams();
            dto.setInspectParams(assembleSchemeDetail(dtoInspectParams, paramIds, parameters));
        }
    }

    /**
     * 根据
     *
     * @param dtoInspectParams
     * @param paramIds
     * @return
     */
    private List<InspectionSchemeDetail> assembleSchemeDetail(List<InspectionSchemeDetail> dtoInspectParams, Set<Long> paramIds, List<InspectionParameter> parameters) {
        List<InspectionSchemeDetail> result = new ArrayList<>();
        for (Long id : paramIds) {
            InspectionSchemeDetail detail = dtoInspectParams.stream().filter(inspectionSchemeDetail -> Objects.equals(inspectionSchemeDetail.getInspectionParameterId(), id))
                    .findAny().orElse(new InspectionSchemeDetail());
            if (Objects.isNull(detail.getType())) {
                InspectionParameter inspectionParameter1 = parameters.stream().filter(inspectionParameter -> Objects.equals(inspectionParameter.getId(), id)).findAny().orElse(new InspectionParameter());
                detail.setType(inspectionParameter1.getType());
            }
            result.add(detail);
        }
        return result;
    }

    /**
     * 赋值异常原因和巡检人
     *
     * @param dto
     */
    private void getAbnormalReason(InspectRecordSheetDto dto) {

        List<OperationUser> users = dto.getUsers();
        if (Objects.isNull(dto.getReason()) || StringUtils.isEmpty(dto.getReason())) {
            dto.setReason(CommonUtils.BLANK_STR);
        }
        if (CollectionUtils.isEmpty(users)) {
            dto.setUserList(CommonUtils.BLANK_STR);
        } else {
            List<String> collect = users.stream().map(OperationUser::getUserName).collect(Collectors.toList());
            String join = StringUtils.join(collect, ",");
            dto.setUserList(join);
        }
    }

}



