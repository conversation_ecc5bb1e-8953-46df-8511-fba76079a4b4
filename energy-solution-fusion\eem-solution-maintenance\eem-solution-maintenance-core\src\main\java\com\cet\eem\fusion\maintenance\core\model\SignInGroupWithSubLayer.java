package com.cet.eem.fusion.maintenance.core.model;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInPoint;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInPointSequence;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName : SignInGroupWithSubLayer
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 15:31
 */
@Getter
@Setter
public class SignInGroupWithSubLayer extends SignInGroup {

    @JsonProperty(ModelLabelDef.REGISTRATION_POINT + "_model")
    private List<SignInPoint> signInPointList;

    @JsonProperty(ModelLabelDef.REGISTRATION_POINT_SEQUENCE + "_model")
    private List<SignInPointSequence> signInPointSequenceList;

    /**
     * 获取排序的order
     * 小心多次调用导致顺序不一致的问题
     *
     * @return
     */
    @JsonIgnore
    public int getNextOrder() {
        if (CollectionUtils.isEmpty(signInPointSequenceList)) {
            return 1;
        }
        return signInPointSequenceList.stream().map(SignInPointSequence::getSort).filter(Objects::nonNull).max(Comparator.naturalOrder()).orElse(0) + 1;
    }
}
