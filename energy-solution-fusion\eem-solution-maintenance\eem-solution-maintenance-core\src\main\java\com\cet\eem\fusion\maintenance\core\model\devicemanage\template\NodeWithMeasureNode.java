package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.MeasureNode;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.RunningParamNode;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-14
 */
@Data
public class NodeWithMeasureNode extends RunningParamNode {
    @JsonProperty(ModelLabelDef.MEASURE_NODE+"_model")
    private List<MeasureNode> params;
}

