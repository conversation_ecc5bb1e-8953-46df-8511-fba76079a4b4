package com.cet.eem.fusion.maintenance.core.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.cet.eem.fusion.maintenance.core.model.maintance.item.MaintenanceItemVo;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.model.BaseVo;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : MaintenanceItemListener
 * @Description : 导入维保项目时候的读取方式
 * <AUTHOR> jiangzixuan
 * @Date: 2021-08-03 11:16
 */
public class MaintenanceItemListener extends AnalysisEventListener<MaintenanceItemImport> {
    List<MaintenanceItemVo> maintenanceItemVos;

    public MaintenanceItemListener(List<MaintenanceItemVo> maintenanceItemVos) {
        this.maintenanceItemVos = maintenanceItemVos;
    }

    Map<Integer, String> errorMsgMap = new HashMap<>();


    @Override
    public void invoke(MaintenanceItemImport maintenanceItemImport, AnalysisContext analysisContext) {
        int index = 0;
        MaintenanceItemVo maintenanceItemVo = new MaintenanceItemVo();
        maintenanceItemVos.add(maintenanceItemVo);
        if (StringUtils.isEmpty(maintenanceItemImport.getGroupName())) {
            errorMsgMap.put(index++, "导入的数据维保分组信息不能为空！");
        } else {
            maintenanceItemVo.setGroupName(maintenanceItemImport.getGroupName());
        }
        if (StringUtils.isEmpty(maintenanceItemImport.getMaintenanceTypeName())) {
            errorMsgMap.put(index++, "导入的数据维保方式不能为空！");
        } else {
            maintenanceItemVo.setMaintenanceTypeName(maintenanceItemImport.getMaintenanceTypeName());
        }
        if (StringUtils.isEmpty(maintenanceItemImport.getCount())) {
            errorMsgMap.put(index, "导入的数据维保内容不能为空！");
        } else {
            maintenanceItemVo.setContent(maintenanceItemImport.getCount());
        }
        if (StringUtils.isNotEmpty(maintenanceItemImport.getSparePartName())) {
            BaseVo baseVo = CommonUtils.getDeviceNameAndIdAndModelLabel(maintenanceItemImport.getSparePartName());
            if (null != baseVo) {
                maintenanceItemVo.setSparePartId(baseVo.getId());
                maintenanceItemVo.setSparePartName(baseVo.getName());
            }

        }
        if (null != maintenanceItemImport.getNumber()) {
            maintenanceItemVo.setNumber(maintenanceItemImport.getNumber());
        }
        if (StringUtils.isNotEmpty(maintenanceItemImport.getUnit())) {
            maintenanceItemVo.setUnit(maintenanceItemImport.getUnit());
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (errorMsgMap.size() <= 0) {
            return;
        }
        throw new ValidationException(errorMsgMap.toString());
    }
}

