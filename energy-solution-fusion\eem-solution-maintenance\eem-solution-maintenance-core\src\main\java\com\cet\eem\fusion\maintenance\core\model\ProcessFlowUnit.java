package com.cet.eem.fusion.maintenance.core.model;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/10
 */
@Getter
@Setter
@ApiModel(description = "流程日志")
@ModelLabel(ModelLabelDef.PROCESS_FLOW_UNIT)
public class ProcessFlowUnit {
    private Long id;

    private String detail;

    private Long eventid;

    private String eventlabel;

    private String executionid;

    private Long logtime;

    private String nodedefinitionkey;

    private String nodename;

    private String operator;

    private Long operator_id;

    private String processdefinitionid;

    private Integer processflowstatus;

    private String processinstanceid;

    private String remark;

    private String suggestion;

    private String taskid;

    private String attachments;
}
