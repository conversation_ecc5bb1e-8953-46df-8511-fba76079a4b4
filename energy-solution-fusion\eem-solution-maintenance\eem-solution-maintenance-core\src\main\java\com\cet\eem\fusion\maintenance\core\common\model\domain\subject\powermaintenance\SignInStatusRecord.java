package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/6/17
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.SIGN_IN_STATUS_RECORD)
public class SignInStatusRecord extends EntityWithName {
    @JsonProperty(ColumnDef.SIGN_IN_GROUP_ID)
    private Long signInGroupId;

    @JsonProperty(ColumnDef.SIGN_IN_POINT_ID)
    private Long signInPointId;

    private Integer status;

    @JsonProperty(ColumnDef.UPDATE_TIME)
    private LocalDateTime updateTime;

    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;

    public SignInStatusRecord() {
        this.modelLabel = ModelLabelDef.SIGN_IN_STATUS_RECORD;
    }
}
