package com.cet.eem.fusion.maintenance.core.model.plan;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.MaintenanceExtend;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : AddInspectionPlanRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-21 09:32
 */
@Getter
@Setter
@ApiModel(value = "EditMaintenancePlanRequest", description = "编辑维保计划")
public class EditMaintenancePlanRequest {

    /**
     * 主键id
     */
    @NotNull(message = "维保计划id不能为空")
    private Long id;
    /**
     * 周期
     */
    @JsonProperty("aggregationcycle")
    private Integer aggregationCycle;

    /**
     * 工单提前生成时长
     */
    @JsonProperty("aheadduration")
    private Integer aheadDuration;

    /**
     * 时间表达式
     */
    private String cycle;


    /**
     * 首次执行时间
     */
    @JsonProperty("executetime")
    private Long executeTime;

    /**
     * 计划结束时间
     */
    @JsonProperty("finishtime")
    private Long finishTime;


    /**
     * 计划名称
     */
    private String name;

    /**
     * 巡检组id
     */
    @JsonProperty("teamid")
    private Long teamId;

    /**
     * 预计耗时
     */
    @JsonProperty("timeconsumeplan")
    private Long timeConsumePlan;

    /**
     * 设备列表
     */
    @JsonProperty("deviceplanrelationship_model")
    private List<DevicePlanRelationship> deviceList;

    /**
     * 计划人员数量
     */
    private Integer population;

    /**
     * 任务描述
     */
    @JsonProperty("taskdescription")
    private String taskDescription;

    /**
     * 任务等级
     */
    @JsonProperty("worksheettasklevel")
    private Integer worksheetTaskLevel;


    @JsonProperty("maintenanceextend")
    private MaintenanceExtend maintenanceExtend;

    @ApiModelProperty("维保计划策略")
    @JsonProperty(ColumnDef.EXECUTE_STRATEGY)
    private Integer executeStrategy;

    @ApiModelProperty("系统安全措施")
    @JsonProperty(ColumnDef.SAFETY_MEASURE)
    private String safetyMeasure;

    @ApiModelProperty("对象标识")
    @JsonProperty(ColumnDef.NODE_LABEL)
    private String nodeLabel;

    @ApiModelProperty("对象型号")
    @JsonProperty(ColumnDef.MODEL)
    private String model;
}


