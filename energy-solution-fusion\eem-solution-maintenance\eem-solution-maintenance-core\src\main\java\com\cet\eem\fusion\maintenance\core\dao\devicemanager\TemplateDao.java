package com.cet.eem.fusion.maintenance.core.dao.devicemanager;

import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.AttributeTemplate;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.AttributeTemplateDto;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.Collection;
import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-12
 */
public interface TemplateDao extends BaseModelDao<AttributeTemplateDto> {
    List<AttributeTemplate> getTemplates(List<Long> ids);

    List<AttributeTemplate> queryNodeTemplates(Collection<Long> ids);
}


