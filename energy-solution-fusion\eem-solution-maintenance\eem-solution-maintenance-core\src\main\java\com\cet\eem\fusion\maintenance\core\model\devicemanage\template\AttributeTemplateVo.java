package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-27
 */
@Data
@AllArgsConstructor
@ApiModel(description = "模板")
public class AttributeTemplateVo extends AttributeTemplate {

    private Integer runningParamAmount;
    private Integer techParamAmount;


    public AttributeTemplateVo(String templatename, Boolean isopenprotect, List<RunningParamGroup> runningParam, List<TechParam> techParams,  Integer runningParamAmount, Integer techParamAmount) {
        super(templatename, isopenprotect, runningParam, techParams);
        this.runningParamAmount = runningParamAmount;
        this.techParamAmount = techParamAmount;
    }

    public AttributeTemplateVo(String templatename, Boolean isopenprotect, List<RunningParamGroup> runningParam, List<TechParam> techParams) {
        super(templatename, isopenprotect, runningParam, techParams);
    }

    public AttributeTemplateVo() {

    }
}

