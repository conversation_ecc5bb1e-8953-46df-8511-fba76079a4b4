# Fix WorkOrderStatus encoding issue
Write-Host "Fixing WorkOrderStatus encoding issue..." -ForegroundColor Green

$filePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java\com\cet\eem\fusion\common\model\enumeration\subject\powermaintenance\WorkOrderStatus.java"
$utf8NoBom = New-Object System.Text.UTF8Encoding $false

# Remove the file if it exists
if (Test-Path $filePath) {
    Remove-Item $filePath -Force
    Write-Host "Removed existing file" -ForegroundColor Yellow
}

# Create new content without Chinese characters
$content = @"
package com.cet.eem.fusion.common.model.enumeration.subject.powermaintenance;

/**
 * Work order status enumeration
 */
public enum WorkOrderStatus {
    DRAFT("draft", "Draft"),
    PENDING("pending", "Pending"),
    IN_PROGRESS("in_progress", "In Progress"),
    COMPLETED("completed", "Completed"),
    CANCELLED("cancelled", "Cancelled");
    
    private final String code;
    private final String description;
    
    WorkOrderStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() { return code; }
    public String getDescription() { return description; }
}
"@

# Write the file with UTF-8 without BOM
[System.IO.File]::WriteAllText($filePath, $content, $utf8NoBom)

Write-Host "Fixed WorkOrderStatus.java successfully!" -ForegroundColor Green
