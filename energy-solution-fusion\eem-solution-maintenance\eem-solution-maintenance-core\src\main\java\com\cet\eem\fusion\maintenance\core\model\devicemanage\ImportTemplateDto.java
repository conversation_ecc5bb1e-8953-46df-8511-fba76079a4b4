package com.cet.eem.fusion.maintenance.core.model.devicemanage;

import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : ImportTemplateDto
 * @Description : 校验导入模板的冗余数据
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-18 13:45
 */
@Getter
@Setter
public class ImportTemplateDto extends TechParamValue {
    /**
     * 模板id
     */
    private Long nodeTemplateId;
    /**
     * 该模板id对应的技术参数id的集合
     */
    private List<Long> ids;
}

