package com.cet.eem.fusion.maintenance.core.controller.bff.inspect;

import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionSearchDto;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorParameterExportService;
import com.cet.electric.commons.ApiResult;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

/**

/**
 * @ClassName : InspectorParameterExportController
 * @Description : 导出巡检数据（定制）
 * <AUTHOR> jiangzixaun
 * @Date: 2021-08-25 16:20
 */
public class InspectorParameterExportController {
    @Autowired
    InspectorParameterExportService inspectorParameterExportService;
    @ApiOperation(value = "导出巡检数据")
    @PostMapping("/export")
    public ApiResult<Object> exportParameter(@RequestBody InspectionSearchDto dto, HttpServletResponse response) throws Exception {
        inspectorParameterExportService.exportParameter(dto,response);
        return null;
    }
}


