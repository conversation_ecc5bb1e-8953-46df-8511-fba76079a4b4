package com.cet.eem.fusion.maintenance.core.common.model.enumeration.subject.powermaintenance;

/**
 * Inspection status enumeration
 */
public enum InspectionStatus {
    PENDING("pending", "Pending"),
    IN_PROGRESS("in_progress", "In Progress"),
    COMPLETED("completed", "Completed"),
    FAILED("failed", "Failed");
    
    private final String code;
    private final String description;
    
    InspectionStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() { return code; }
    public String getDescription() { return description; }
}