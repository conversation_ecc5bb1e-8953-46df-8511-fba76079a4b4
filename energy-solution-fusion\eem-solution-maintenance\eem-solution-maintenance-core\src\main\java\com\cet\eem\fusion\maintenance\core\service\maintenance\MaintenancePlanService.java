package com.cet.eem.fusion.maintenance.core.service.maintenance;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.fusion.maintenance.core.model.plan.*;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.electric.commons.ApiResult;
import org.quartz.SchedulerException;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface MaintenancePlanService {

    /**
     * 添加维保计划
     *
     * @param addMaintenancePlanRequest
     * @return
     * @throws SchedulerException
     */
    PlanSheet addMaintenancePlan(AddMaintenancePlanRequest addMaintenancePlanRequest) throws SchedulerException;

    /**
     * 编辑巡检计划
     * @param editMaintenancePlanRequest
     * @return
     * @throws SchedulerException
     */
    PlanSheet editMaintenancePlan(EditMaintenancePlanRequest editMaintenancePlanRequest) throws SchedulerException;

    /**
     * 查询巡检计划
     *
     * @param queryInspectionPlanRequest
     * @return
     */
    ApiResult<List<MaintenancePlanSheetVo>> queryMaintenancePlan(QueryMaintenancePlanRequest queryInspectionPlanRequest);

    /**
     * 查询巡检计划工单信息
     *
     * @param workSheetId
     * @return
     */
    List<InspectionWorkOrderDto> queryPlanWorkOrder(Long workSheetId);
    /**
     * 禁用巡检计划
     *
     * @param ids
     */
    void disablePlanSheet(Collection<Long> ids) throws SchedulerException;

    /**
     * 启用巡检计划
     *
     * @param ids
     */
    void enablePlanSheet(Collection<Long> ids) throws SchedulerException;

    /**
     * 定时任务查询维保计划
     * @param queryInspectionPlanRequest
     * @return
     */
    public ApiResult<List<MaintenancePlanSheetVo>> queryMaintenancePlanBySchedule(QueryMaintenancePlanRequest queryInspectionPlanRequest);
}


