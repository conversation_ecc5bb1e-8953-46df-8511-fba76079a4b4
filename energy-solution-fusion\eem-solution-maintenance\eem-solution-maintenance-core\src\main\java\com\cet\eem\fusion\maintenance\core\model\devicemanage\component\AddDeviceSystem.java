package com.cet.eem.fusion.maintenance.core.model.devicemanage.component;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(value = "AddDeviceSystem", description = "新增设备系统")
public class AddDeviceSystem {
    @Length(max = 50,message = "系统名称长度不能超过50")
    @NotNull(message = "系统名称不能为空")
    private String name;


}

