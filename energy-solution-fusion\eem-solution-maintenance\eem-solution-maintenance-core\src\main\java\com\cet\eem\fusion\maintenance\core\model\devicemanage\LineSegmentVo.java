package com.cet.eem.fusion.maintenance.core.model.devicemanage;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import lombok.Data;

/**
 * @Author: fyl
 * @Description: 抽屉柜
 * @Data: Created in 2021-05-19
 */
@Data
@ModelLabel(ModelLabelDef.LINE_SEGMENT)
public class LineSegmentVo extends EntityWithName {
    private String model;

    public LineSegmentVo() {
        this.modelLabel = ModelLabelDef.LINE_SEGMENT;
    }
}


