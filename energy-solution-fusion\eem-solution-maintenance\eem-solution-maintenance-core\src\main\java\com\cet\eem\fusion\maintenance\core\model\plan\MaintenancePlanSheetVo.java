package com.cet.eem.fusion.maintenance.core.model.plan;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.PlanSheet;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

/**
 * @ClassName : MaintenancePlanSheetVo
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-22 09:58
 */
@Getter
@Setter
public class MaintenancePlanSheetVo extends PlanSheetBaseVo {

    /**
     * 等级名称
     */
    private String worksheetTaskLevelName;


    public MaintenancePlanSheetVo(PlanSheet planSheet) {
        BeanUtils.copyProperties(planSheet, this);
    }
}

