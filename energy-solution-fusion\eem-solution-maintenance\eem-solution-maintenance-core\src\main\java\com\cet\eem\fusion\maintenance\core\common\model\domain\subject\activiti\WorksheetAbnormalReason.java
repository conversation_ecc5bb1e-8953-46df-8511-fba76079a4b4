package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.activiti;

import com.cet.eem.fusion.maintenance.common.definition.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/10
 */
@Getter
@Setter
@ApiModel(description = "工单异常原因")
public class WorksheetAbnormalReason {
    private Long id;

    private String modelLabel;

    @ApiModelProperty("故障类型")
    private Integer type;

    @ApiModelProperty("工单id")
    @JsonProperty(ColumnDef.PM_WORK_SHEET_ID)
    private Long pmWorkSheetId;

    public WorksheetAbnormalReason() {
        this.modelLabel = ModelLabelDef.WORKSHEET_ABNORMAL_REASON;
    }

    public WorksheetAbnormalReason(Integer type, Long pmWorkSheetId) {
        this();
        this.type = type;
        this.pmWorkSheetId = pmWorkSheetId;
    }
}
