package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : MeasureNodeGroup
 * @Description : 测点分组
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-15 18:49
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.MEASURE_NODE_GROUP)
public class MeasureNodeGroup extends EntityWithName {
    @JsonProperty("leafnode")
    private boolean leafNode;
    @JsonProperty("parentid")
    private Long parentId;
    public MeasureNodeGroup() {
        this.modelLabel = ModelLabelDef.MEASURE_NODE_GROUP;
    }
}

