package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInGroup;
import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInGroupWithAllSubLayer;
import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInGroupWithEquipment;
import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInGroupWithSubLayer;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.Collection;
import java.util.List;

public interface SignInGroupDao extends BaseModelDao<SignInGroup> {
    /**
     * @param signInGroupId
     * @return
     */
    SignInGroupWithEquipment querySignInGroupWithEquipment(Long signInGroupId);

    /**
     * 查询所有的签到信息
     *
     * @param signInGroupIds
     * @return
     */
    List<SignInGroupWithAllSubLayer> querySignInGroupWithAllSubLayer(Collection<Long> signInGroupIds);

    /**
     * 根据签到组id查询信息
     *
     * @param signGroupId 签到组id
     * @return 签到组以及签到点信息
     */
    SignInGroupWithSubLayer querySignInGroupWithSubLayer(Long signGroupId);

    /**
     * 根据项目id查询签到点信息
     *
     * @param projectId
     * @return
     */
    List<SignInGroupWithSubLayer> querySignInGroupWithSubLayerByProjectId(Long projectId);
}



