package com.cet.eem.fusion.maintenance.core.service;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.WorkOrderCheckInfo;
import com.cet.eem.fusion.common.utils.datatype.NumberUtils;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderCheckInfoDao;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderDao;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.def.tips.WorkOrderTips;
import com.cet.eem.fusion.maintenance.core.model.workorder.*;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.WorkOrderBatchReviewVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.WorkOrderReviewVo;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorService;
import com.cet.eem.fusion.maintenance.core.utils.InspectorUserCheckUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ContentTypeDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.definition.exception.WorkOrderErrorCodeEnum;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.electric.commons.ApiResult;

import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;
import com.cet.electric.workflow.api.ProcessInstanceRestApi;
import com.cet.electric.workflow.api.UserTaskRestApi;
import com.cet.eem.fusion.maintenance.core.common.constants.ProcessVariableDefinition;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.node.config.UserTaskConfig;
import com.cet.eem.fusion.maintenance.core.common.model.params.ManyUserTaskParams;
import com.cet.eem.fusion.maintenance.core.common.model.params.ReviewManyTaskParams;
import com.cet.eem.fusion.maintenance.core.common.model.params.ReviewTaskParams;
import com.cet.electric.workflow.starter.rest.api.proxy.ProcessInstanceRestApiProxy;
import com.cet.futureblue.i18n.LanguageUtil;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/31
 */
@Service
@Slf4j
public class WorkOrderServiceImpl implements WorkOrderService {
    @Autowired
    UserRestApi userRestApi;

    @Autowired
    WorkOrderDao workOrderDao;

    @Autowired
    InspectorService inspectorService;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    ProcessInstanceRestApi processInstanceRestApi;

    @Autowired
    ProcessInstanceRestApiProxy processInstanceRestApiProxy;

    @Autowired
    UserTaskRestApi userTaskRestApi;

    @Autowired
    WorkOrderCheckInfoDao workOrderCheckInfoDao;

    @Autowired
    FileUtils fileUtils;

    @Autowired
    InspectorUserCheckUtils inspectorUserCheckUtils;

    @Override
    public ApiResult<List<InspectionWorkOrderDto>> queryWorkOrderList(WorkOrderSearchVo searchVo) {
        return workOrderDao.queryWorkOrder(searchVo, GlobalInfoUtils.getUserId());
    }

    @Override
    public List<WOCountByTaskTypeVo> queryWorkOrderCount(WorkOrderSearchVo dto) {
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();
        dto.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(dto.getTeamId(), user));
        dto.setInspectUser(inspectorService.isInspectUser(user));

        List<Map<String, Object>> maps = workOrderDao.queryWorkOrderCountByTaskType(dto);
        return assemblyRuntimeWorkOrderNumber(dto, maps);
    }

    @Override
    public List<WOCountByTaskTypeVo> queryRuntimeWorkOrderCount(WorkOrderSearchVo dto) {
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();
        dto.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(dto.getTeamId(), user));
        dto.setInspectUser(inspectorService.isInspectUser(user));

        List<Map<String, Object>> maps = workOrderDao.queryRuntimeWorkOrderCountByTaskType(dto, null);
        return assemblyRuntimeWorkOrderNumber(dto, maps);
    }

    private List<WOCountByTaskTypeVo> assemblyRuntimeWorkOrderNumber(WorkOrderSearchVo dto, List<Map<String, Object>> maps) {
        Map<Integer, String> statusMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORK_SHEET_TASK_TYPE);
        Map<Integer, WOCountByTaskTypeVo> statusCountMap = new HashMap<>();

        // 解析结果
        for (Map<String, Object> map : maps) {
            WOCountByTaskTypeVo workOrderCountDto = new WOCountByTaskTypeVo();
            Integer status = NumberUtils.parseInteger(map.get(WorkOrderDef.TASK_TYPE));
            Integer count = NumberUtils.parseInteger(map.get(ColumnDef.COUNT_ID));

            workOrderCountDto.setCount(count);
            workOrderCountDto.setTaskType(status);
            workOrderCountDto.setTaskTypeName(statusMap.get(status));
            statusCountMap.put(status, workOrderCountDto);
        }

        if (CollectionUtils.isEmpty(dto.getTaskTypes())) {
            return new ArrayList<>(statusCountMap.values());
        }

        List<WOCountByTaskTypeVo> result = new ArrayList<>();
        for (Integer workSheetStatus : dto.getTaskTypes()) {
            WOCountByTaskTypeVo workOrderCountDto = statusCountMap.get(workSheetStatus);
            if (workOrderCountDto == null) {
                workOrderCountDto = new WOCountByTaskTypeVo(workSheetStatus, statusMap.get(workSheetStatus));
            }
            result.add(workOrderCountDto);
        }
        return result;
    }

    @Override
    public void getProcessDiagram(String code, Boolean isLightStyle) throws Exception {
        if (isLightStyle == null) {
            isLightStyle = false;
        }
        InspectionWorkOrderDto workOrderDto = queryWorkOrder(code);

        Optional<Response> processDiagram = processInstanceRestApiProxy.getProcessDiagram(workOrderDto.getProcessInstanceId(), isLightStyle);
        processDiagram.ifPresent(it -> {
            try {
                downloadProcessDiagram(it);
            } catch (Exception e) {
                log.error(WorkOrderTips.download_wo_diagram_error, e);
                throw new ValidationException(WorkOrderTips.download_wo_diagram_error);
            }
        });
    }

    private void downloadProcessDiagram(Response processDiagram) throws Exception {
        Response.Body body = processDiagram.body();
        HttpServletResponse servletResponse = response;

        InputStream fileInputStream = body.asInputStream();
        ByteArrayOutputStream newFileInputStream = cloneInputStream(fileInputStream);
        if (newFileInputStream == null) {
            throw new ValidationException("读取流信息失败！");
        }
        ApiResult<Object> result = null;
        try {
            String content = FileUtils.readInputStream(new ByteArrayInputStream(newFileInputStream.toByteArray()));
            log.error("打印流信息:{}", content);
            result = JsonTransferUtils.parseObject(content, Result.class);
        } catch (Exception ex) {
            log.error("转换结果异常：", ex);
        }

        if (result != null) {
            ParamUtils.checkResultGeneric(result);
        }

        FileUtils.download(new ByteArrayInputStream(newFileInputStream.toByteArray()), ContentTypeDef.PNG, servletResponse);
    }

    private static ByteArrayOutputStream cloneInputStream(InputStream input) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = input.read(buffer)) > -1) {
                baos.write(buffer, 0, len);
            }
            baos.flush();
            return baos;
        } catch (IOException e) {
            log.error("拷贝流异常", e);
        }

        return null;
    }

    @Override
    public InspectionWorkOrderDto queryRuntimeWorkOrder(String code) {
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();
        Map<String, Object> workOrderMap = workOrderDao.queryRuntimeWorkOrder(code, GlobalInfoUtils.getUserId(), null);
        InspectionWorkOrderDto workOrder = JsonTransferUtils.parseObject(workOrderMap, InspectionWorkOrderDto.class);
        if (workOrder == null) {
            throw new ValidationException(LanguageUtil.getMessage(WorkOrderErrorCodeEnum.RUNTIME_WORK_ORDER_NOT_FOUNT.getMsg()));
        }

        ApiResult<UserTaskConfig> userTaskConfig = userTaskRestApi.getTaskConfig(GlobalInfoUtils.getUserId(), workOrder.getTaskId());
        ParamUtils.checkResultGeneric(userTaskConfig);

        if (inspectorUserCheckUtils.checkRepairWorkOrderAuth(user, workOrder, userTaskConfig.getData())) {
            return workOrder;
        }
        return null;
    }

    @Override
    public InspectionWorkOrderDto queryWorkOrder(String code) {
        InspectionWorkOrderDto dto = workOrderDao.queryWorkOrder(code, InspectionWorkOrderDto.class);
        if (dto == null) {
            throw new ValidationException(LanguageUtil.getMessage(WorkOrderErrorCodeEnum.RUNTIME_WORK_ORDER_NOT_FOUNT.getMsg()));
        }
        return dto;
    }

    @Override
    public void reviewForm(WorkOrderReviewVo workOrderReviewVo) {
        Long userId = GlobalInfoUtils.getUserId();
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(userId);
UserVo user = usrRes.getData();

        InspectionWorkOrderDto workOrder = queryRuntimeWorkOrder(workOrderReviewVo.getCode());
        ApiResult<Object> result = userTaskRestApi.reviewForm(user.getId(), workOrder.getTaskId(), workOrderReviewVo.getParams());
        ParamUtils.checkResultGeneric(result);
    }

    @Override
    public void reviewFormBatch(WorkOrderBatchReviewVo workOrderReviewVo) {
        Long userId = GlobalInfoUtils.getUserId();
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(userId);
UserVo user = usrRes.getData();

        List<InspectionWorkOrderDto> workOrders = workOrderDao.queryRuntimeWorkOrders(workOrderReviewVo.getCodes(), userId, null);
        ReviewManyTaskParams params = workOrderReviewVo.getParams();
        params.setTaskIds(workOrders.stream().map(InspectionWorkOrderDto::getTaskId).collect(Collectors.toList()));
        Map<String, Object> formData = params.getFormData();
        if (formData == null) {
            formData = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
            params.setFormData(formData);
        }

        formData.putIfAbsent(ColumnDef.END_TIME, ProcessVariableDefinition.NOW_TIME_SYMBOL);

        ApiResult<List<String>> result = userTaskRestApi.reviewManyForms(user.getId(), params);
        ParamUtils.checkResultGeneric(result);
    }

    @Override
    public void saveReviewForm(WorkOrderReviewVo workOrderReviewVo) {
        Long userId = GlobalInfoUtils.getUserId();
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(userId);
UserVo user = usrRes.getData();

        InspectionWorkOrderDto workOrder = queryRuntimeWorkOrder(workOrderReviewVo.getCode());
        WorkOrderCheckInfo info = new WorkOrderCheckInfo();
        ReviewTaskParams params = workOrderReviewVo.getParams();
        info.setAttachments(JsonTransferUtils.toJSONString(params.getAttachments()));
        info.setCreateTime(LocalDateTime.now());
        info.setRemark(params.getRemark());
        info.setPmWorkSheetId(workOrder.getId());
        info.setTaskId(workOrder.getTaskId());
        info.setCode(workOrder.getCode());
        info.setUserId(user.getId());
        info.setFormData(params.getFormData());
        modelServiceUtils.writeData(Collections.singletonList(info));
    }

    @Override
    public WorkOrderCheckInfoVo queryWorkOrderCheckInfo(String code) {
        InspectionWorkOrderDto workOrder = queryRuntimeWorkOrder(code);
        WorkOrderCheckInfoVo checkInfo = workOrderCheckInfoDao.queryLastCheckInfo(workOrder.getCode(), workOrder.getTaskId());
        if (checkInfo == null) {
            return null;
        }

        UserVo user = authUtils.queryUser(checkInfo.getUserId());
        if (user != null) {
            checkInfo.setUserName(user.getName());
        }

        return checkInfo;
    }

    @Override
    public void checkTaskNodes(String code, String nodeLabel) {
        UserTaskConfig userTaskConfig = queryTaskConfig(code);
        if (userTaskConfig == null) {
            throw new ValidationException(LanguageUtil.getMessage(WorkOrderErrorCodeEnum.TASK_CONFIG_NOT_FOUND.getMsg()));
        }

        if (!Objects.equals(userTaskConfig.getNodeModel(), nodeLabel)) {
            throw new ValidationException(LanguageUtil.getMessage(WorkOrderErrorCodeEnum.SUBMIT_NOT_MATCH_TASK_NODE.getMsg()));
        }
    }

    @Override
    public UserTaskConfig queryTaskConfig(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ValidationException("工单号不允许为空!");
        }

        InspectionWorkOrderDto workOrder = queryRuntimeWorkOrder(code);
        ApiResult<UserTaskConfig> userTaskConfig = userTaskRestApi.getTaskConfig(GlobalInfoUtils.getUserId(), workOrder.getTaskId());
        ParamUtils.checkResultGeneric(userTaskConfig);
        return userTaskConfig.getData();
    }

    @Override
    public void submitFormData(WorkOrderFormSubmitParam submitParam) {
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();
        InspectionWorkOrderDto workOrder = queryRuntimeWorkOrder(submitParam.getCode());

        ApiResult<Map<String, Object>> result = userTaskRestApi.submitForm(user.getId(), workOrder.getTaskId(), submitParam.getUserTaskParams());
        ParamUtils.checkResultGeneric(result);
    }

    @Override
    public void submitFormDataBatch(WorkOrderFormBatchSubmitParam submitParam) {
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();

        submitParam.setCodes(submitParam.getCodes().stream().distinct().collect(Collectors.toList()));
        List<InspectionWorkOrderDto> workOrders = workOrderDao.queryRuntimeWorkOrders(submitParam.getCodes(), GlobalInfoUtils.getUserId(), null);
        if (submitParam.getCodes().size() != workOrders.size()) {
            throw new ValidationException(LanguageUtil.getMessage(WorkOrderErrorCodeEnum.RUNTIME_WORK_ORDER_NOT_FOUNT.getMsg()));
        }
        ManyUserTaskParams params = submitParam.getParams();
        params.setTaskIds(workOrders.stream().map(InspectionWorkOrderDto::getTaskId).collect(Collectors.toList()));
        Map<String, UserTaskConfig> taskConfigMap = workOrderDao.queryTaskConfigList(workOrders.stream().map(EntityWithName::getId).collect(Collectors.toList()), null);
        for (InspectionWorkOrderDto workOrder : workOrders) {
            if (!inspectorUserCheckUtils.checkRepairWorkOrderAuth(user, workOrder, taskConfigMap.get(workOrder.getCode()))) {
                throw new ValidationException(String.format("当前用户无操作工单号为%s的权限！", workOrder.getCode()));
            }
        }
        ApiResult<List<String>> result = userTaskRestApi.submitManyForms(user.getId(), submitParam.getParams());
        ParamUtils.checkResultGeneric(result);
    }

    @Override
    public boolean checkAuth(String code) {
        ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(GlobalInfoUtils.getUserId());
        UserVo user = usrRes.getData();
        InspectionWorkOrderDto workOrder = workOrderDao.queryWorkOrder(code, InspectionWorkOrderDto.class);
        Map<String, UserTaskConfig> taskConfigMap = workOrderDao.queryTaskConfigList(Collections.singletonList(workOrder.getId()), null);
        UserTaskConfig userTaskConfig = taskConfigMap.get(code);
        if (userTaskConfig == null) {
            return false;
        }

        return inspectorUserCheckUtils.checkRepairWorkOrderAuth(user, workOrder, userTaskConfig);
    }

}



