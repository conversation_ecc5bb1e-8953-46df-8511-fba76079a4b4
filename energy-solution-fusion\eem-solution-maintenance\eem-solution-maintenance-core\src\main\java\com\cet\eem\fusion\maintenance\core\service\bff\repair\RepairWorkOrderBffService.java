package com.cet.eem.fusion.maintenance.core.service.bff.repair;

import com.cet.eem.fusion.maintenance.core.model.maintance.WorkOrderExportVo;
import com.cet.eem.fusion.maintenance.core.model.maintance.WorkOrderVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionCountSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.RepairByNodeSearchVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.RepairSearchVo;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/2
 */
public interface RepairWorkOrderBffService {
    /**
     * 查询工单列表
     *
     * @param dto
     * @return
     */
    ApiResult<List<WorkOrderVo>> queryWorkOrderList(RepairSearchVo dto);

    /**
     * 根据节点查询工单
     *
     * @param dto
     * @return
     */
    ApiResult<List<WorkOrderVo>> queryWorkOrderByNode(RepairByNodeSearchVo dto);

    /**
     * 查询工单数量
     *
     * @param dto
     * @return
     */
    List<WorkOrderCountDto> queryWorkOrderCount(InspectionCountSearchDto dto);

    /**
     * 组装数据
     *
     * @param workOrderList 工单数据
     * @param tenantId      租户id
     */
    void assemblyPartWorkOrderList(List<WorkOrderVo> workOrderList, Long tenantId, UserVo user);

    /**
     * 组装数据
     *
     * @param workOrderList
     * @param tenantId
     */
    void assemblyWholeWorkOrderList(List<WorkOrderVo> workOrderList, Long tenantId, UserVo user);

    /**
     * 查询工单
     *
     * @param code 工单号
     * @return 工单
     */
    WorkOrderExportVo queryWorkOrder(String code);

    /**
     * 导出工单
     *
     * @param code
     */
    void exportWorkOrder(String code);
}


