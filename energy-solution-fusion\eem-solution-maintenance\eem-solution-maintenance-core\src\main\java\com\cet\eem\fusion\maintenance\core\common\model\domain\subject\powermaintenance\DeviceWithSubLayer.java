package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import java.util.List;

/**
 * Device with sub layer
 */
public class DeviceWithSubLayer extends EntityWithName {
    
    private String deviceName;
    private String deviceType;
    private String deviceCode;
    private String deviceStatus;
    private String manufacturer;
    private String model;
    private String serialNumber;
    private String installDate;
    private String location;
    private String nodeId;
    private String nodeName;
    private List<DeviceWithSubLayer> subLayers;
    
    public String getDeviceName() {
        return deviceName;
    }
    
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    
    public String getDeviceType() {
        return deviceType;
    }
    
    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }
    
    public String getDeviceCode() {
        return deviceCode;
    }
    
    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }
    
    public String getDeviceStatus() {
        return deviceStatus;
    }
    
    public void setDeviceStatus(String deviceStatus) {
        this.deviceStatus = deviceStatus;
    }
    
    public String getManufacturer() {
        return manufacturer;
    }
    
    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public String getSerialNumber() {
        return serialNumber;
    }
    
    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }
    
    public String getInstallDate() {
        return installDate;
    }
    
    public void setInstallDate(String installDate) {
        this.installDate = installDate;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getNodeId() {
        return nodeId;
    }
    
    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }
    
    public String getNodeName() {
        return nodeName;
    }
    
    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }
    
    public List<DeviceWithSubLayer> getSubLayers() {
        return subLayers;
    }
    
    public void setSubLayers(List<DeviceWithSubLayer> subLayers) {
        this.subLayers = subLayers;
    }
}
