# PowerShell script to delete problematic newly created files
Write-Host "Deleting problematic newly created files..."

$baseDir = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# List of problematic files to delete
$filesToDelete = @(
    "$baseDir\com\cet\eem\fusion\common\conditions\query\QueryConditionBuilder.java",
    "$baseDir\com\cet\eem\fusion\common\model\objective\physicalquantity\PhysicalQuantity.java",
    "$baseDir\com\cet\eem\fusion\common\model\ext\subject\powermaintenance\PlanSheetWithSubLayer.java",
    "$baseDir\com\cet\eem\fusion\common\model\ext\subject\powermaintenance\DeviceWithSubLayer.java",
    "$baseDir\com\cet\eem\fusion\common\util\TimeUtil.java",
    "$baseDir\com\cet\eem\fusion\common\util\CommonUtilsService.java",
    "$baseDir\com\cet\eem\fusion\common\utils\ExcelValidationUtils.java",
    "$baseDir\com\cet\eem\fusion\common\utils\StringUtils.java",
    "$baseDir\com\cet\eem\fusion\common\model\tool\QueryConditionBuilder.java",
    "$baseDir\com\cet\eem\fusion\common\service\EemModelDataService.java",
    "$baseDir\com\cet\eem\fusion\common\model\auth\user\UserInfo.java",
    "$baseDir\com\cet\eem\fusion\common\model\auth\node\NodeInfo.java",
    "$baseDir\com\cet\eem\fusion\common\auth\utils\AuthUtils.java",
    "$baseDir\com\cet\eem\fusion\maintenance\constant\MaintenanceConstant.java",
    "$baseDir\com\cet\eem\fusion\common\definition\CommonDef.java",
    "$baseDir\com\cet\eem\fusion\common\definition\ContentTypeDef.java",
    "$baseDir\com\cet\electric\workflow\api\UserTaskRestApi.java",
    "$baseDir\com\cet\electric\workflow\api\TriggerRestApi.java",
    "$baseDir\com\cet\eem\fusion\common\exception\BusinessException.java",
    "$baseDir\com\cet\eem\fusion\common\model\event\ConvergenceEvent.java",
    "$baseDir\com\cet\eem\fusion\common\service\event\ConvergenceEventService.java",
    "$baseDir\com\cet\eem\fusion\common\service\event\expert\ExpertService.java",
    "$baseDir\com\cet\eem\fusion\common\service\event\PecCoreEventBffService.java",
    "$baseDir\com\cet\eem\fusion\common\dao\event\ConvergenceEventDao.java",
    "$baseDir\com\cet\eem\fusion\common\model\domain\perception\logicaldevice\LogicalDevice.java",
    "$baseDir\com\cet\eem\fusion\common\model\domain\object\powersystem\PowerSystemObject.java",
    "$baseDir\com\cet\eem\fusion\common\dao\device\DeviceCommonInfoDao.java",
    "$baseDir\com\cet\eem\fusion\common\dao\device\PecDeviceExtendDao.java",
    "$baseDir\com\cet\eem\fusion\common\model\device\DeviceCommonInfo.java",
    "$baseDir\com\cet\eem\fusion\common\model\device\PecDeviceExtendVo.java",
    "$baseDir\com\cet\eem\fusion\common\utils\performance\annotation\ExecuteIndexAnnotation.java",
    "$baseDir\com\cet\eem\fusion\common\constant\PecsNodeType.java",
    "$baseDir\com\cet\eem\fusion\common\constant\DemandConstant.java",
    "$baseDir\com\cet\eem\fusion\common\model\domain\subject\huaxingguangdian\EventPlan.java",
    "$baseDir\com\cet\eem\fusion\common\model\expert\ExpertRule.java",
    "$baseDir\com\cet\eem\fusion\common\model\expert\ExpertAnalysis.java",
    "$baseDir\com\cet\eem\fusion\common\log\service\LogService.java",
    "$baseDir\com\cet\eem\fusion\common\model\peccore\PecCoreNode.java"
)

$deletedCount = 0
foreach ($file in $filesToDelete) {
    if (Test-Path $file) {
        Remove-Item $file -Force
        Write-Host "Deleted: $(Split-Path $file -Leaf)"
        $deletedCount++
    }
}

Write-Host "Deleted $deletedCount problematic files."
