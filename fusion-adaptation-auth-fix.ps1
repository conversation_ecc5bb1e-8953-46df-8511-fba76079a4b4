# Fix authentication and authorization related imports
Write-Host "Fixing authentication and authorization imports..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Auth-related import replacements
$authImportReplacements = @{
    "import com.cet.eem.auth.aspect.EnumAndOr;" = "import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;"
    "import com.cet.eem.auth.aspect.OperationPermission;" = "import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;"
    "import com.cet.eem.bll.common.def.OperationAuthDef;" = "import com.cet.eem.fusion.common.def.OperationAuthDef;"
    "import com.cet.piem.common.constant.TableNameDef;" = "import com.cet.eem.solution.common.def.common.label.ModelLabelDef;"
    "import com.cet.piem.common.constant.TableColumnNameDef;" = "import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;"
    "import com.cet.eem.model.tool.QueryConditionBuilder;" = "import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;"
    "import com.cet.eem.model.tool.ParentQueryConditionBuilder;" = "import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;"
    "import com.cet.eem.model.tool.ModelServiceUtils;" = "import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;"
    "import com.cet.piem.common.utils.ProgressUpdaterUtils;" = "import com.cet.eem.solution.common.utils.ProgressUpdaterUtils;"
    "import com.cet.piem.common.def.AggregationCycle;" = "import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;"
    "import com.cet.eem.common.exception.BusinessBaseException;" = "import com.cet.eem.fusion.common.exception.BusinessBaseException;"
}

# Constant replacements
$constantReplacements = @{
    "TableNameDef\." = "ModelLabelDef."
    "TableColumnNameDef\." = "TableColumnNameDef."
}

function Apply-AuthFixes {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $changed = $false
    
    # Apply auth import replacements
    foreach ($oldImport in $authImportReplacements.Keys) {
        $newImport = $authImportReplacements[$oldImport]
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), $newImport
            $changed = $true
            Write-Host "  - Updated auth import: $oldImport -> $newImport" -ForegroundColor Yellow
        }
    }
    
    # Apply constant replacements
    foreach ($oldConstant in $constantReplacements.Keys) {
        $newConstant = $constantReplacements[$oldConstant]
        if ($content -match $oldConstant) {
            $content = $content -replace $oldConstant, $newConstant
            $changed = $true
            Write-Host "  - Updated constant: $oldConstant -> $newConstant" -ForegroundColor Yellow
        }
    }
    
    if ($changed) {
        Set-Content $filePath -Value $content -Encoding UTF8
        Write-Host "✓ Applied auth fixes to: $filePath" -ForegroundColor Green
        return $true
    }
    
    return $false
}

# Process all Java files
Write-Host "Processing Java files for auth fixes..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$filesUpdated = 0

foreach ($file in $javaFiles) {
    if (Apply-AuthFixes -filePath $file.FullName) {
        $filesUpdated++
    }
}

Write-Host "`nAuth fixes completed!" -ForegroundColor Green
Write-Host "Files updated: $filesUpdated" -ForegroundColor Cyan
