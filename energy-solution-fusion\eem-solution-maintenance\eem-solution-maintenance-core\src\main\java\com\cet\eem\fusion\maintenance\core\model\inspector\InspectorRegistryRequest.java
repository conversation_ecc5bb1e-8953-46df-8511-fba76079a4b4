package com.cet.eem.fusion.maintenance.core.model.inspector;

import com.cet.eem.fusion.maintenance.core.common.model.auth.user.RoleVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName : InspectorRegistryRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-02 09:33
 */
@Getter
@Setter
@ApiModel(value = "InspectorRegistryRequest", description = "巡检人员注册")
public class InspectorRegistryRequest {

    /**
     * 名称
     */
    @NotEmpty(message = "巡检人员名称不能为空")
    private String name;

    /**
     * 密码
     */
    @NotEmpty(message = "密码不能为空")
    private String password;

    /**
     * 审核用密码不能为空
     */
    @ApiModelProperty("审核用密码")
    private String checkPassword;

    /**
     * 巡检人员角色
     */
    @NotNull(message = "巡检人员角色不能为空")
    private RoleVo role;
}

