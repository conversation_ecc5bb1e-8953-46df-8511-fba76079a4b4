# Additional modifications script - based on 03其他修改任务.md
Write-Host "Starting additional modifications..." -ForegroundColor Green

$coreDir = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$javaFiles = Get-ChildItem -Path $coreDir -Filter "*.java" -Recurse

$modifiedCount = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $modified = $false
    
    # 1. ResultWithTotal to ApiResult
    if ($content -match "ResultWithTotal") {
        Write-Host "Processing file: $($file.Name) - ResultWithTotal modification" -ForegroundColor Yellow
        
        # 替换import语句
        $content = $content -replace "import com\.cet\.eem\.common\.model\.ResultWithTotal;", "import com.cet.electric.commons.ApiResult;"
        $content = $content -replace "import com\.cet\.eem\.bll\.common\.model\.ResultWithTotal;", "import com.cet.electric.commons.ApiResult;"
        
        # 替换类型声明
        $content = $content -replace "\bResultWithTotal\b", "ApiResult"
        
        $modified = $true
    }
    
    # 2. UnitService modification
    if ($content -match "UnitService") {
        Write-Host "Processing file: $($file.Name) - UnitService modification" -ForegroundColor Yellow
        
        # 替换import
        $content = $content -replace "import com\.cet\.piem\.service\.UnitService;", "import com.cet.eem.fusion.config.sdk.service.EnergyUnitService;"
        
        # 添加新的import
        if ($content -match "EnergyUnitService" -and $content -notmatch "import com\.cet\.eem\.fusion\.config\.sdk\.utils\.GlobalInfoUtils;") {
            $content = $content -replace "(import com\.cet\.eem\.fusion\.config\.sdk\.service\.EnergyUnitService;)", "`$1`nimport com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;`nimport com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitSearchDTO;"
        }
        
        # 替换注入
        $content = $content -replace "@Resource\s+private\s+UnitService\s+unitService;", "@Resource`nprivate EnergyUnitService energyUnitService;"
        
        # 替换UserDefineUnit为UserDefineUnitDTO
        $content = $content -replace "\bUserDefineUnit\b", "UserDefineUnitDTO"
        
        $modified = $true
    }
    
    # 3. EemCloudAuthService modification
    if ($content -match "EemCloudAuthService") {
        Write-Host "Processing file: $($file.Name) - EemCloudAuthService modification" -ForegroundColor Yellow
        
        # 替换import
        $content = $content -replace "import com\.cet\.eem\.service\.EemCloudAuthService;", "import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;"
        
        # 添加ApiResultI18n import
        if ($content -match "UserRestApi" -and $content -notmatch "import com\.cet\.electric\.matterhorn\.cloud\.authservice\.common\.util\.i18n\.ApiResultI18n;") {
            $content = $content -replace "(import com\.cet\.electric\.matterhorn\.cloud\.authservice\.api\.UserRestApi;)", "`$1`nimport com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;"
        }
        
        # 替换注入
        $content = $content -replace "@Resource\s+private\s+EemCloudAuthService\s+cloudAuthService;", "@Resource`nUserRestApi userRestApi;"
        
        # 替换方法调用
        $content = $content -replace "cloudAuthService\.queryUserBatch\(([^)]+)\)", "userRestApi.getUsers(`$1)"
        $content = $content -replace "Result<List<UserVo>>", "ApiResultI18n<List<UserVo>>"
        
        $modified = $true
    }
    
    # 4. AuthUtils modification
    if ($content -match "com\.cet\.eem\.auth\.service\.AuthUtils") {
        Write-Host "Processing file: $($file.Name) - AuthUtils modification" -ForegroundColor Yellow
        
        # 替换import
        $content = $content -replace "import com\.cet\.eem\.auth\.service\.AuthUtils;", "import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;`nimport com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;"
        
        # 替换注入
        $content = $content -replace "@Autowired\s+AuthUtils\s+authUtils;", "@Resource`nUserRestApi userRestApi;"
        
        # 替换方法调用
        $content = $content -replace "authUtils\.queryAndCheckUser\(([^)]+)\)", "userRestApi.getUserByUserId(`$1).getData()"
        
        $modified = $true
    }
    
    # 5. GlobalInfoUtils.getHttpResponse() modification
    if ($content -match "GlobalInfoUtils\.getHttpResponse\(\)") {
        Write-Host "Processing file: $($file.Name) - GlobalInfoUtils.getHttpResponse() modification" -ForegroundColor Yellow

        # This needs manual modification as it involves method signature changes
        # Just mark for manual processing
        Write-Host "Warning: File $($file.Name) contains GlobalInfoUtils.getHttpResponse() call, needs manual method signature modification" -ForegroundColor Red
        
        $modified = $true
    }
    
    # 6. JsonUtil modification
    if ($content -match "com\.cet\.eem\.common\.util\.JsonUtil") {
        Write-Host "Processing file: $($file.Name) - JsonUtil modification" -ForegroundColor Yellow
        
        # 替换import
        $content = $content -replace "import com\.cet\.eem\.common\.util\.JsonUtil;", "import com.cet.eem.fusion.common.utils.JsonTransferUtils;"
        
        # 替换方法调用
        $content = $content -replace "JsonUtil\.mapList2BeanList\(([^,]+),\s*([^)]+)\)", "JsonTransferUtils.parseList(`$1, `$2)"
        
        $modified = $true
    }
    
    # 如果内容有修改，写回文件
    if ($modified -and $content -ne $originalContent) {
        try {
            # 确保使用UTF-8无BOM编码
            $utf8NoBom = New-Object System.Text.UTF8Encoding $false
            [System.IO.File]::WriteAllText($file.FullName, $content, $utf8NoBom)
            $modifiedCount++
            Write-Host "Modified file: $($file.Name)" -ForegroundColor Green
        }
        catch {
            Write-Host "Failed to modify file: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "Additional modifications completed! Total files modified: $modifiedCount" -ForegroundColor Green
