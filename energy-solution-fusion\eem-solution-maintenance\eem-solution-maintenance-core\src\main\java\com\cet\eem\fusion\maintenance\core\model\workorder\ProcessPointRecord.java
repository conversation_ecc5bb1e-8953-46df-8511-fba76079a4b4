package com.cet.eem.fusion.maintenance.core.model.workorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 4/13/2021
 */
@Getter
@Setter
@ApiModel(description = "流程状态模型")
public class ProcessPointRecord {
    @ApiModelProperty("流程节点名称")
    private String name;
    @ApiModelProperty("操作人")
    private Long operatorUser;
    @ApiModelProperty("操作人名称")
    private String operatorUserName;
    @ApiModelProperty("意见")
    private String opinion;
    @ApiModelProperty("操作时间")
    private String operationTime;
    @ApiModelProperty("操作结果")
    private String content;
}

