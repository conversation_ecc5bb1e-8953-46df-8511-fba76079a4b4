package com.cet.eem.fusion.maintenance.core.model.sign;

import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/5/12
 */
@Getter
@Setter
public class SignInStatisticsTableWriteVo extends EntityWithName {
    @ApiModelProperty("是否跳过签到点")
    @JsonProperty("skipsign")
    private Boolean skipSign;

    @ApiModelProperty("签到人")
    @JsonProperty("staff_id")
    private Long staffId;

    @ApiModelProperty("签到时间")
    @JsonProperty("logtime")
    private Long logTime;

    @ApiModelProperty("签到点id")
    @JsonProperty("signpoint_id")
    private Long signPointId;

    @ApiModelProperty("签到点组id")
    private Long signGroupId;

    @ApiModelProperty("项目id")
    @JsonProperty("project_id")
    private Long projectId;

    @ApiModelProperty("跳过原因")
    private String description;

    @ApiModelProperty("工单号")
    @JsonProperty("ordercode")
    private String orderCode;

    @ApiModelProperty("工单id")
    @JsonProperty("pmworksheet_id")
    private Long pmWorkSheetId;

    public SignInStatisticsTableWriteVo() {
        this.modelLabel = ModelLabelDef.SIGN_IN_STATISTICS_TABLE;
    }
}



