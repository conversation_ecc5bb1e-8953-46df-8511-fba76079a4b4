package com.cet.eem.fusion.maintenance.core.dao.devicecomponent.impl;


import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DeviceSystem;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SpareParts;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SparePartsDevice;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.DeviceSystemWithSubLayer;
import com.cet.eem.fusion.maintenance.core.bll.common.model.ext.subject.powermaintenance.DeviceWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.DeviceDao;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.DeviceSystemDao;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.DeviceImportList;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.model.model.AbstractModelEntity;
import com.cet.eem.fusion.maintenance.common.toolkit.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Repository
public class DeviceDaoimpl extends ModelDaoImpl<SparePartsDevice> implements DeviceDao {
    @Autowired
    DeviceSystemDao deviceSystemDao;

    @Override
    public List<DeviceSystemWithSubLayer> queryDeviceBySystem() {
        LambdaQueryWrapper<DeviceSystem> queryWrapper = LambdaQueryWrapper.of(DeviceSystem.class);
        queryWrapper.eq(DeviceSystem::getProjectId, GlobalInfoUtils.getTenantId());
        return deviceSystemDao.selectRelatedList(DeviceSystemWithSubLayer.class, queryWrapper);
    }

    @Override
    public List<SpareParts> querySparepartsByDeviceId(Long id) {
        LambdaQueryWrapper<SparePartsDevice> queryWrapper = LambdaQueryWrapper.of(SparePartsDevice.class)
                .eq(SparePartsDevice::getId, id);
        List<DeviceWithSubLayer> deviceWithSubLayers = this.selectRelatedList(DeviceWithSubLayer.class, queryWrapper);
        List<SpareParts> list = new ArrayList<>();
        for (DeviceWithSubLayer deviceWithSubLayer : deviceWithSubLayers) {
            if (CollectionUtils.isEmpty(deviceWithSubLayer.getSparePartsList())) {
                continue;
            }
            list.addAll(deviceWithSubLayer.getSparePartsList());
        }
        return list;
    }

    @Override
    public DeviceSystemWithSubLayer queryByNameAndId(String name, Long id, Long systemId) {
        LambdaQueryWrapper<SparePartsDevice> queryWrapper = LambdaQueryWrapper.of(SparePartsDevice.class)
                .eq(SparePartsDevice::getName, name)
                .ne(SparePartsDevice::getId, id);
        return deviceSystemDao.selectRelatedById(DeviceSystemWithSubLayer.class, systemId, Arrays.asList(queryWrapper));

    }

    @Override
    public DeviceSystemWithSubLayer queryByModelAndObjectLabel(String model, String objectLabel, Long id, Long systemId) {
        LambdaQueryWrapper<SparePartsDevice> queryWrapper = LambdaQueryWrapper.of(SparePartsDevice.class)
                .eq(SparePartsDevice::getModel, model)
                .eq(SparePartsDevice::getObjectLabel, objectLabel)
                .ne(SparePartsDevice::getId, id);
        return deviceSystemDao.selectRelatedById(DeviceSystemWithSubLayer.class, systemId, Arrays.asList(queryWrapper));
    }

    @Override
    public List<SpareParts> querySparepartsStorageByDevice(String model, String objectLabel) {
        LambdaQueryWrapper<SparePartsDevice> queryWrapper = LambdaQueryWrapper.of(SparePartsDevice.class)
                .eq(SparePartsDevice::getModel, model)
                .eq(SparePartsDevice::getObjectLabel, objectLabel);
        List<DeviceWithSubLayer> deviceWithSubLayers = this.selectRelatedList(DeviceWithSubLayer.class, queryWrapper);
        if (CollectionUtils.isEmpty(deviceWithSubLayers)) {
            return Collections.emptyList();
        }
        List<SpareParts> sparePartsList = new ArrayList<>();
        for (DeviceWithSubLayer deviceWithSubLayer : deviceWithSubLayers) {
            if (CollectionUtils.isNotEmpty(deviceWithSubLayer.getSparePartsList())) {
                sparePartsList.addAll(deviceWithSubLayer.getSparePartsList());
            }
        }
        return sparePartsList;
    }

    @Override
    public List<SparePartsDevice> queryByModelInfo(Set<DeviceImportList> modelNodes) {
        if (CollectionUtils.isEmpty(modelNodes)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SparePartsDevice> queryWrapper = LambdaQueryWrapper.of(SparePartsDevice.class);
        Map<String, List<DeviceImportList>> nodeMap = modelNodes.stream().collect(Collectors.groupingBy(DeviceImportList::getModelLabel));
        nodeMap.forEach((key, val) -> {
            Set<String> models = val.stream().map(DeviceImportList::getModel).collect(Collectors.toSet());
            queryWrapper.or(it -> it.eq(SparePartsDevice::getObjectLabel, key)
                    .in(SparePartsDevice::getModel, models));
        });

        return selectList(queryWrapper);
    }

    public List<DeviceWithSubLayer> queryByModelInfo(List<Long> sparePartsDeviceIdList, Collection<String> modelList) {
        LambdaQueryWrapper<SparePartsDevice> wrapper = LambdaQueryWrapper.of(SparePartsDevice.class).in(AbstractModelEntity::getId, sparePartsDeviceIdList);

        LambdaQueryWrapper<SpareParts> wrapper1 = LambdaQueryWrapper.of(SpareParts.class)
                .in(SpareParts::getModel, modelList);

        return selectRelatedList(DeviceWithSubLayer.class, wrapper, Collections.singletonList(wrapper1));
    }
}


