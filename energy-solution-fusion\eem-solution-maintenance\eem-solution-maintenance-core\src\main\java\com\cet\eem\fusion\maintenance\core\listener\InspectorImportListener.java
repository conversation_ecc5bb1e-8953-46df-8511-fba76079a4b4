package com.cet.eem.fusion.maintenance.core.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.cet.eem.fusion.maintenance.core.model.InspectorImportDto;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/11 14:45
 */
public class InspectorImportListener extends AnalysisEventListener<InspectorImportDto> {


    private List<InspectorImportDto> dataList = new ArrayList<>();

    /**
     * 读取数据时触发invoke()方法
     */
    @Override
    public void invoke(InspectorImportDto data, AnalysisContext context) {
        dataList.add(data);
    }

    /**
     * 所有数据解析完成后触发doAfterAllAnalysed()方法
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    public List<InspectorImportDto> getDataList() {
        return dataList;
    }
}
