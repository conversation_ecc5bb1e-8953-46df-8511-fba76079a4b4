package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-06
 */
@Data
@AllArgsConstructor
@ApiModel(description = "模板运行参数分组")
@ModelLabel(ModelLabelDef.RUNNING_PARAM_TEMPLATE_GROUP)
public class RunningParamGroup extends TemplateGroupDto {

    @JsonProperty("runningparam_model")
    private List<RunningParam> runningParams;

    public RunningParamGroup() {
        this.modelLabel = ModelLabelDef.RUNNING_PARAM_TEMPLATE_GROUP;
    }
}


