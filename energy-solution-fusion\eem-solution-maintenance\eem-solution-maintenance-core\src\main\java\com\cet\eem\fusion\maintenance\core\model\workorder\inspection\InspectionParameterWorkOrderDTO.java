﻿package com.cet.eem.fusion.maintenance.core.model.workorder.inspection;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.model.workorder.MaintenanceContent;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 4/13/2021
 */

@Data
public class InspectionParameterWorkOrderDTO {

    @ApiModelProperty("实际完成时间")
    @JsonProperty(WorkOrderDef.FINISH_TIME)
    private LocalDateTime finishTime;


    @ApiModelProperty("关联的设备")
    @JsonProperty(WorkOrderDef.DEVICE_PLAN_RELATIONSHIP_MODEL)
    protected List<DevicePlanRelationship> devicePlanRelationshipList;

    /**
     * 巡检
     */
    @JsonProperty(WorkOrderDef.MAINTENANCE_CONTENT)
    protected String maintenanceContent;

    @ApiModelProperty("巡检参数详情")
    private List<InspectionSchemeDetail> inspectionSchemeDetails;

    public List<InspectionSchemeDetail> getInspectionSchemeDetails() {
        MaintenanceContent maintenanceContentObj = JsonTransferUtils.parseObject(this.maintenanceContent, MaintenanceContent.class);
        return maintenanceContentObj == null ? null : maintenanceContentObj.getInspectParams();
    }

}


