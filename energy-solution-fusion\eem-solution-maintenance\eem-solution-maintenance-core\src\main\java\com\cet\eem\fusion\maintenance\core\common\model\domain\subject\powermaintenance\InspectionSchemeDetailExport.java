package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : InspectionSchemeDetailExport
 * @Description : 导出的巡检方案详情
 * <AUTHOR> jiang<PERSON><PERSON>uan
 * @Date: 2021-08-25 19:28
 */
@Getter
@Setter
public class InspectionSchemeDetailExport extends InspectionSchemeDetailVo{
    /**
     * 设备id
     */
    private Long deviceId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 配电柜id
     */
    private Long powerDisCabinetId;
    /**
     * 配电柜名称
     */
    private String powerDisCabinetName;
    /**
     * 房间id
     */
    private Long roomId;
    /**
     * 房间名称
     */
    private String roomName;
    /**
     * 执行人名称
     */
    private String staffName;
}