# Fix remaining SignIn related classes import statements
Write-Host "Fixing remaining SignIn related classes import statements..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Define additional import replacements for remaining SignIn related classes
$remainingSignInImportReplacements = @{
    # SignInStatusRecord class
    "import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInStatusRecord;" = "import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInStatusRecord;"
    
    # Any other SignIn related classes that might be missed
    "import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInRecord;" = "import com.cet.eem.fusion.common.model.domain.subject.powermaintenance.SignInRecord;"
    "import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInRecord;" = "import com.cet.eem.fusion.common.model.ext.subject.powermaintenance.SignInRecord;"
}

function Update-RemainingSignInImports {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $updated = $false
    
    foreach ($oldImport in $remainingSignInImportReplacements.Keys) {
        $newImport = $remainingSignInImportReplacements[$oldImport]
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), $newImport
            $updated = $true
            Write-Host "  - Updated: $oldImport" -ForegroundColor Yellow
            Write-Host "    -> $newImport" -ForegroundColor Green
        }
    }
    
    if ($updated) {
        Set-Content -Path $filePath -Value $content -Encoding UTF8
        return $true
    }
    
    return $false
}

# List of specific files that still have issues
$problematicFiles = @(
    "SignInStatusRecordDao.java",
    "SignInStatusRecordImpl.java", 
    "ResetSignStatusRecord.java",
    "UpdateSignPointStatus.java",
    "SignInStatisticsTableMobileServiceImpl.java",
    "SignInStatusRecordServiceImpl.java"
)

Write-Host "Processing specific problematic files..." -ForegroundColor Cyan
$updatedFiles = 0

foreach ($fileName in $problematicFiles) {
    Write-Host "Looking for file: $fileName" -ForegroundColor White
    
    # Find the file in the directory structure
    $foundFiles = Get-ChildItem -Path $coreSourcePath -Filter $fileName -Recurse
    
    foreach ($file in $foundFiles) {
        Write-Host "Processing: $($file.FullName)" -ForegroundColor White
        
        if (Update-RemainingSignInImports -filePath $file.FullName) {
            $updatedFiles++
            Write-Host "Updated: $($file.Name)" -ForegroundColor Green
        } else {
            # Check if file still has old SignIn imports
            $content = Get-Content $file.FullName -Raw -Encoding UTF8
            if ($content -match "import com\.cet\.eem\.bll\.common\.model\..*SignIn") {
                Write-Host "File still has old SignIn imports: $($file.Name)" -ForegroundColor Red
                
                # Show the problematic imports
                $lines = Get-Content $file.FullName
                for ($i = 0; $i -lt $lines.Length; $i++) {
                    if ($lines[$i] -match "import com\.cet\.eem\.bll\.common\.model\..*SignIn") {
                        Write-Host "  Line $($i+1): $($lines[$i])" -ForegroundColor Red
                    }
                }
            }
        }
    }
}

Write-Host "`nRemaining fix completed!" -ForegroundColor Green
Write-Host "Total updated files: $updatedFiles" -ForegroundColor Cyan

# Final check for any remaining old SignIn imports
Write-Host "`nFinal check for remaining SignIn related imports..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$remainingIssues = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    if ($content -match "import com\.cet\.eem\.bll\.common\.model\..*SignIn") {
        Write-Host "Still has old SignIn import: $($file.Name)" -ForegroundColor Red
        $remainingIssues++
        
        # Show the specific imports
        $lines = Get-Content $file.FullName
        for ($i = 0; $i -lt $lines.Length; $i++) {
            if ($lines[$i] -match "import com\.cet\.eem\.bll\.common\.model\..*SignIn") {
                Write-Host "  Line $($i+1): $($lines[$i])" -ForegroundColor Yellow
            }
        }
    }
}

if ($remainingIssues -eq 0) {
    Write-Host "All SignIn related imports have been updated correctly!" -ForegroundColor Green
} else {
    Write-Host "Still have $remainingIssues files with old SignIn imports" -ForegroundColor Red
}

Write-Host "`nScript execution completed!" -ForegroundColor Green
