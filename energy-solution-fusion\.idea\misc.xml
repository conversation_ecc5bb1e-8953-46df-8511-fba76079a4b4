<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/eem-solution-common/pom.xml" />
        <option value="$PROJECT_DIR$/eem-solution-demo/pom.xml" />
        <option value="$PROJECT_DIR$/eem-solution-production-expansion/pom.xml" />
        <option value="$PROJECT_DIR$/eem-solution-maintenance/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/eem-solution-maintenance/eem-solution-maintenance-core/pom.xml" />
        <option value="$PROJECT_DIR$/eem-solution-maintenance/eem-solution-maintenance-service/pom.xml" />
        <option value="$PROJECT_DIR$/eem-solution-maintenance/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State />
        </expanded-state>
        <selected-state>
          <State>
            <id>Android</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>