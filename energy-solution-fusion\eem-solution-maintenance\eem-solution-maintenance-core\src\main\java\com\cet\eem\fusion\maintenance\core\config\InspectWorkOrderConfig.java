package com.cet.eem.fusion.maintenance.core.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2023/2/15
 */
@Getter
@Setter
//@Component
//@ConfigurationProperties(prefix = "cet.eem.work-order.inspect")
//@Slf4j
//@ToString
public class InspectWorkOrderConfig {
    private InspectCheckOverTimeConfig checkOverTime;
}

