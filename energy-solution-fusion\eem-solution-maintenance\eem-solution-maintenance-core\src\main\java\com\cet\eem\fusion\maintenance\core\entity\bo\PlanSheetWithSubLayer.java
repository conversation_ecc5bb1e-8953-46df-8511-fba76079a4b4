package com.cet.eem.fusion.maintenance.core.entity.bo;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.MaintenanceItem;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.PlanSheet;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * @ClassName : PlanSheetWithSubLayer
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-22 09:14
 */
@Getter
@Setter
public class PlanSheetWithSubLayer extends PlanSheet {

    /**
     * 设备列表
     */
    @JsonProperty(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP + "_model")
    private List<DevicePlanRelationship> devicePlanRelationshipList;

    /**
     * 设备列表
     */
    @JsonProperty(ModelLabelDef.MAINTENANCE_ITEM + "_model")
    private List<MaintenanceItem> maintenanceItemList;
}