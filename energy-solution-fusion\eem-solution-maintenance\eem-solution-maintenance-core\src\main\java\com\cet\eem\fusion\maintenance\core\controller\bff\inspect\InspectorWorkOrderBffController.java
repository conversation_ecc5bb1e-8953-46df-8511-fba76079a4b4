package com.cet.eem.fusion.maintenance.core.controller.bff.inspect;

import com.cet.eem.fusion.common.entity.Result;
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;
import com.cet.eem.fusion.common.def.OperationAuthDef;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCheckInfoVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderPo;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.*;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionWorkOrderService;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.ProcessInstanceResponse;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.node.config.UserTaskConfig;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-03-12 16:38
 */
public class InspectorWorkOrderBffController {

    @Autowired
    private InspectionWorkOrderService inspectionWorkOrderService;

    @Autowired
    private WorkOrderService workOrderService;

    /**
     * 消息即将超时推送预警提前时间
     */
    @Value("${cet.eem.work-order.inspect.check-over-time-notice.pre-time}")
    private int noticeMinute;

    @ApiOperation(value = "查询巡检工单")
    @PostMapping("/workOrder")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<List<InspectionWorkOrderDto>> queryWorkOrderList(
            @RequestBody InspectionSearchDto dto) {
        return inspectionWorkOrderService.queryWorkOrderList(dto);
    }

    @ApiOperation(value = "审核工单")
    @PostMapping("/workOrder/check")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_MONITOR_CHECK, OperationAuthDef.INSPECTOR_WORK_ORDER_TECHNICIAN_CHECK}, andOr = EnumAndOr.OR)
    public ApiResult<List<WorkOrderPo>> reviewForm(
            @RequestBody WorkOrderReviewVo workOrderReviewVo) {
        workOrderService.reviewForm(workOrderReviewVo);
        return Result.ok();
    }

    @ApiOperation(value = "批量审核工单")
    @PostMapping("/workOrder/check/batch")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_MONITOR_CHECK, OperationAuthDef.INSPECTOR_WORK_ORDER_TECHNICIAN_CHECK}, andOr = EnumAndOr.OR)
    public ApiResult<List<WorkOrderPo>> reviewForm(
            @RequestBody WorkOrderBatchReviewVo workOrderReviewVo) {
        workOrderService.reviewFormBatch(workOrderReviewVo);
        return Result.ok();
    }

    @ApiOperation(value = "暂存审核信息")
    @PostMapping("/workOrder/check/stash")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_MONITOR_CHECK, OperationAuthDef.INSPECTOR_WORK_ORDER_TECHNICIAN_CHECK}, andOr = EnumAndOr.OR)
    public ApiResult<List<WorkOrderPo>> saveReviewForm(
            @RequestBody WorkOrderReviewVo workOrderReviewVo) {
        workOrderService.saveReviewForm(workOrderReviewVo);
        return Result.ok();
    }

    @ApiOperation(value = "查询暂存的审核信息")
    @GetMapping("/workOrder/check/stash/{code}")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_MONITOR_CHECK, OperationAuthDef.INSPECTOR_WORK_ORDER_TECHNICIAN_CHECK}, andOr = EnumAndOr.OR)
    public ApiResult<WorkOrderCheckInfoVo> checkOrder(
            @PathVariable String code) {
        WorkOrderCheckInfoVo workOrderCheckInfoVo = workOrderService.queryWorkOrderCheckInfo(code);
        return Result.ok(workOrderCheckInfoVo);
    }

    @ApiOperation(value = "统计工单数量")
    @PostMapping("/workOrder/count")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<List<WorkOrderCountDto>> queryWorkOrderCount(
            @RequestBody InspectionCountSearchDto dto) {
        List<WorkOrderCountDto> result = inspectionWorkOrderService.queryWorkOrderCount(dto);
        return Result.ok(result);
    }

    @ApiOperation(value = "查询指定巡检工单")
    @GetMapping("/workOrder/{code}")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<InspectionWorkOrderDto> queryWorkOrder(
            @PathVariable @ApiParam(name = "code", value = "工单编号", required = true) String code) {
        InspectionWorkOrderDto result = inspectionWorkOrderService.queryWorkOrder(code);
        return Result.ok(result);
    }

    @ApiOperation(value = "根据工单id查询指定巡检工单")
    @GetMapping("/{workOrderId}")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<InspectionWorkOrderDto> queryWorkOrder(
            @PathVariable @ApiParam(name = "workOrderId", value = "工单id", required = true) Long workOrderId) {
        InspectionWorkOrderDto result = inspectionWorkOrderService.queryWorkOrder(workOrderId);
        return Result.ok(result);
    }

    @ApiOperation(value = "查询指定任务当前所处的节点")
    @GetMapping("/task/config/{code}")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<UserTaskConfig> queryTaskConfig(
            @PathVariable @ApiParam(name = "code", value = "工单编号", required = true) String code) {
        UserTaskConfig result = inspectionWorkOrderService.queryTaskConfig(code);
        return Result.ok(result);
    }

    @ApiOperation(value = "手动创建巡检工单")
    @PutMapping("/workOrder")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_CREATE})
    public ApiResult<ProcessInstanceResponse> queryCurrentProjectSignInGroup(
            @RequestBody InspectionAddDto dto) {
        ProcessInstanceResponse workOrder = inspectionWorkOrderService.createWorkOrder(dto);
        return Result.ok(workOrder);
    }

    @ApiOperation(value = "提交巡检数据，推动流程进入下一步")
    @PutMapping("/workOrder/param/submit")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_INSPECT})
    public ApiResult<ProcessInstanceResponse> submitInspectParams(
            @RequestBody InspectParamsWriteVo dto) {
        inspectionWorkOrderService.submitInspectParams(dto);
        return Result.ok();
    }

    @ApiOperation(value = "保存巡检数据，不会推动流程进入下一步")
    @PutMapping("/workOrder/param/update")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_INSPECT})
    public ApiResult<ProcessInstanceResponse> updateWorkOrder(
            @RequestBody InspectParamsWriteVo dto) {
        inspectionWorkOrderService.updateWorkOrder(dto);
        return Result.ok();
    }

    @ApiOperation(value = "获取流程状态图")
    @GetMapping("/workOrder/processDiagram/{code}")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<Object> updateWorkOrder(@PathVariable String code, @RequestParam(required = false) Boolean isLightStyle) throws Exception {
        workOrderService.getProcessDiagram(code, isLightStyle);
        return null;
    }

    @ApiOperation(value = "工单导出")
    @PostMapping("/export")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<Object> exportWorkOrder(@RequestBody InspectionSearchDto dto, HttpServletResponse response) throws Exception {
        inspectionWorkOrderService.exportWorkOrder(dto, response);
        return null;
    }

    @ApiOperation(value = "校验权限")
    @PostMapping("/checkAuth")
    public ApiResult<Boolean> exportWorkOrder(@RequestParam String code) {
        boolean result = workOrderService.checkAuth(code);
        return Result.ok(result);
    }

    @ApiOperation(value = "查询服务器配置工单即将超时预警允许提前的时间，单位为分钟，返回值小于0表示不需要进行超时预警")
    @GetMapping("/overTimeNoticePreMinute")
    public ApiResult<Integer> getOverTimeNoticePreMinute() {
        return Result.ok(noticeMinute);
    }

    @ApiOperation(value = "更新超时工单状态")
    @GetMapping("/updateOverTimeStatus")
    public ApiResult<Integer> updateOverTimeStatus() {
        inspectionWorkOrderService.updateOverTimeStatus();
        return Result.ok();
    }
}



