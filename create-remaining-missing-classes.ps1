# 创建剩余缺失的类
Write-Host "Creating remaining missing classes..." -ForegroundColor Green

$coreDir = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# 创建目录结构
$directories = @(
    "$coreDir\com\cet\eem\conditions\query",
    "$coreDir\com\cet\eem\common\model\objective\physicalquantity",
    "$coreDir\com\cet\eem\bll\common\model\ext\subject\powermaintenance",
    "$coreDir\com\cet\eem\common\util",
    "$coreDir\com\cet\eem\bll\common\model",
    "$coreDir\com\cet\eem\bll\common\model\domain\perception\logicaldevice",
    "$coreDir\com\cet\eem\bll\common\service\event\convergence",
    "$coreDir\com\cet\eem\event\service",
    "$coreDir\com\cet\eem\event\service\expert",
    "$coreDir\com\cet\eem\bll\common\model\domain\subject\huaxingguangdian",
    "$coreDir\com\cet\eem\common\utils",
    "$coreDir\com\cet\eem\model\tool",
    "$coreDir\com\cet\eem\service",
    "$coreDir\com\cet\eem\bll\common\log\service",
    "$coreDir\com\cet\eem\common\model\peccore",
    "$coreDir\com\cet\eem\auth\model\user",
    "$coreDir\com\cet\eem\auth\utils",
    "$coreDir\com\cet\eem\common",
    "$coreDir\com\cet\eem\common\model\auth\node",
    "$coreDir\com\cet\eem\utils",
    "$coreDir\com\cet\eem\bll\maintenance\constant",
    "$coreDir\com\cet\eem\common\definition",
    "$coreDir\com\cet\eem\bll\common\exception",
    "$coreDir\com\cet\eem\event\model\expert",
    "$coreDir\com\cet\eem\fusion\common\service\auth\impl",
    "$coreDir\com\cet\eem\bll\common\dao\pecdeviceevent",
    "$coreDir\com\cet\eem\bll\common\dao\powersystem",
    "$coreDir\com\cet\eem\bll\common\model\domain\object\powersystem",
    "$coreDir\com\cet\eem\bll\demand\constant",
    "$coreDir\com\cet\eem\common\utils\performance\annotation"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Yellow
    }
}

Write-Host "Directory creation completed!" -ForegroundColor Green
