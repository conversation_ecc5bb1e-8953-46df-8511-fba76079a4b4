package com.cet.eem.fusion.maintenance.core.constant;

import javax.print.DocFlavor;

/**
 * @ClassName : TextConstant
 * @Description :
 * <AUTHOR> xian<PERSON><PERSON><PERSON>
 * @Date: 2023-05-05 15:51
 */
public interface TextConstant {
    public static final String IMPORT_SCHEME_FILE_NAME_PREFIX = "巡检方案收集表";
    public static final String IMPORT_INSPECTOR_FILE_NAME_PREFIX = "巡检人员收集表";
    public static final String IMPORT_SCHEME_CAUTION = "模拟量填写样式: 参数名称 上限值(数值)~下限值(数值)。例: 线圈温度 80~20(温度上限值80，下限值20);\n状态量填写样式:参数名称\n" +
            "文本量填写样式:参数名称 T ( T为固定样式，方便识别是文本量);";
    public static final String IMPORT_INSPECTOR_CAUTION = "1、所有内容均为必填项\n" +
            "班组名称:自定义文本，唯一不能重复\n班组类型:枚举值(巡检班组或维修班组)班组负责人:自定义文本，姓名不能重复，两个姓名间用中文(、)顿号隔开\n" +
            "班组成员:自定义文本，姓名不能重复;两个姓名间用中文(、)顿号隔开。\n2、其他说明:\n1）所有用户默认登录密码为:qweQWE123@巡检确认密码为:123。\n" +
            "2）用户组=班组名称，角色=班组类型+负责人/成员对应的角色";
    public static final String IMPORT_SCHEME_SHEET_NAME = "巡检方案";
    public static final String SCHEME_NAME_TEXT = "巡检方案名称";
    public static final String SCHEME_NAME_DEMO = "巡检方案示例(导入前清除)";
    public static final String PARAM_TYPE_1_DEMO = "状态量";
    public static final String PARAM_TYPE_2_DEMO = "模拟量℃_60~20";
    public static final String PARAM_TYPE_3_DEMO = "文本量_T";
    public static final String INSPECT_INDEX = "序号";
    public static final String INSPECT_TEAM_NAME = "班组名称";
    public static final String INSPECT_TEAM_NAME_DEMO = "北变电站";
    public static final String INSPECT_TEAM_TYPE = "班组类型";
    public static final String INSPECT_TEAM_TYPE_DEMO = "巡检班组";
    public static final String DUTY_OFFICER = "班组负责人(班长)";
    public static final String DUTY_OFFICER_DEMO = "徐宝梅、任婷";
    public static final String DUTY_STAFF = "班组成员";
    public static final String DUTY_STAFF_DEMO = "王林娟、郭婷、樊勇、岳瑞红";
    public static final String FONT_CALIBRI = "等线";
    public static final String FONT_MICROSOFT_YAHEI = "微软雅黑";
    public static final float IMPORT_SCHEME_COLUMN_WIDTH = 24.5f;
    public static final float IMPORT_SCHEME_FIRST_COLUMN_WIDTH = 15.38f;
    public static final float IMPORT_SCHEME_ROW_HEIGHT = 23.1f;
    String DUTY_TYPE_TEXT = "dutyType";
    String ROLE_NAME_INSPECT_OFFICER="巡检班组负责人";
    String ROLE_NAME_INSPECT_STAFF="巡检班组用户";
    String ROLE_NAME_REPAIR_OFFICER="维修班组负责人";
    String ROLE_NAME_REPAIR_STAFF="维修班组用户";

    String DEFAULT_PASSWORD = "qweQWE123@";
    String DEFAULT_CHECK_PASSWORD="123";

}

