package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : PlanSheet
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-16 09:09
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.PLAN_SHEET)
public class PlanSheet extends EntityWithName {

    @JsonProperty("aggregationcycle")
    private Integer aggregationCycle;

    /**
     * 单位分钟
     */
    @JsonProperty("aheadduration")
    private Integer aheadDuration;

    /**
     * 创建时间
     */
    @JsonProperty("createtime")
    private Long createTime;

    /**
     * 创建者id
     */
    private Long creator;

    /**
     * 创建者名
     */
    @JsonProperty("creatorname")
    private String creatorName;

    /**
     * 当为自定义时有值
     * example:P0M0DT1H
     */
    private String cycle;

    /**
     * 删除标志
     */
    private Boolean deleted;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 特殊日期
     */
    @JsonProperty("enableddays")
    private EnabledDays enabledDays;

    /**
     * 首次执行时间
     */
    @JsonProperty("executetime")
    private Long executeTime;

    /**
     * 结束时间
     * null代表永远
     *
     * @JsonInclude 注意更新为空值
     */
    @JsonProperty("finishtime")
    @JsonInclude
    private Long finishTime;

    /**
     * 巡检方案id
     */
    @JsonProperty("inspectionschemeid")
    private Long inspectionSchemeId;

    @JsonProperty("plancode")
    private String planCode;

    @JsonProperty("processdefinitionid")
    private String processDefinitionId;

    @JsonProperty("processdefinitionkey")
    private String processDefinitionKey;

    @JsonProperty("processinstanceid")
    private String processInstanceId;

    /**
     * 项目id
     */
    @JsonProperty("project_id")
    private Long tenantId;

    /**
     * 项目名称
     */
    @JsonProperty("projectname")
    private String projectName;

    @JsonProperty("roomid")
    private Long roomId;

    @JsonProperty("roomlocation")
    private String roomLocation;

    @JsonProperty("roomname")
    private String roomName;

    @JsonProperty("sectionareaid")
    private Long sectionAreaId;

    @JsonProperty("sectionareaname")
    private String sectionAreaName;

    /**
     * 巡检路线id
     */
    @JsonProperty("signgroup_id")
    private Long signGroupId;

    /**
     * 过期状态
     */
    private Boolean status;

    @JsonProperty("taskcontent")
    private String taskContent;

    @JsonProperty("taskdescription")
    private String taskDescription;

    /**
     * 巡检组id
     */
    @JsonProperty("teamid")
    private Long teamId;

    /**
     * 预计耗时
     */
    @JsonProperty("timeconsumeplan")
    private Long timeConsumePlan;

    /**
     * 任务等级
     */
    @JsonProperty("worksheettasklevel")
    private Integer worksheetTaskLevel;

    /**
     * 任务类型
     * {@link WorkSheetTaskType}
     */
    @JsonProperty("worksheettype")
    private Integer workSheetType;

    /**
     * 计划人员数量
     */
    private Integer population;

    /**
     * 维保项目扩展字段
     */
    @JsonProperty("maintenanceextend")
    private MaintenanceExtend maintenanceExtend;

    /**
     * 安全措施
     */
    @JsonProperty(ColumnDef.SAFETY_MEASURE)
    private String safetyMeasure;

    /**
     * 安全措施附件
     */
    @JsonProperty(ColumnDef.SAFETY_MEASURE_ATTACHMENT)
    private List<Attachment> safetyMeasureAttachment;

    @JsonProperty(ColumnDef.EXECUTE_STRATEGY)
    private Integer executeStrategy;

    @ApiModelProperty("对象标识")
    @JsonProperty(ColumnDef.NODE_LABEL)
    private String nodeLabel;

    @ApiModelProperty("对象型号")
    @JsonProperty(ColumnDef.MODEL)
    private String model;

    /**
     * 巡检计划是否被引用
     */
    private Boolean used;

    public PlanSheet() {
        this.modelLabel = ModelLabelDef.PLAN_SHEET;
    }
}
