package com.cet.eem.fusion.maintenance.core.model.workorder.maintenance;

import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.Attachment;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : InputMaintenanceWorkOrderRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-21 11:35
 */
@Getter
@Setter
@ApiModel(description = "录入维保工单")
public class InputMaintenanceWorkOrderRequest {

    /**
     * id
     */
    private Long id;

    /**
     * 工单号
     */
    private String code;

    /**
     * 完成时间
     */
    @JsonProperty(WorkOrderDef.FINISH_TIME)
    @ApiModelProperty("完成时间")
    private Long finishTime;
    /**
     * 实际开始时间
     */
    @NotNull(message = "实际开始时间不能为空")
    @JsonProperty(WorkOrderDef.EXECUTE_TIME)
    private Long executeTime;
    /**
     * 备件信息
     */
    @ApiModelProperty("备件信息")
    private List<SparePartNumber> sparePartNumbers;

    /**
     * 维保项目
     */
    private List<MaintenanceItemExtend> itemExtends;

    /**
     * 工单附件
     */
    @ApiModelProperty("工单附件")
    private List<Attachment> attachment;

    /**
     * 处理描述
     */
    @ApiModelProperty("处理描述")
    private String description;
}
