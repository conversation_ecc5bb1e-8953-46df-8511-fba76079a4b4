package com.cet.eem.fusion.maintenance.core.service.inspection;

import com.cet.eem.fusion.maintenance.core.entity.po.InspectionParameter;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetailVo;
import com.cet.eem.fusion.maintenance.core.model.param.AddInspectionParameterRequest;
import com.cet.eem.fusion.maintenance.core.model.param.QueryInspectionParameterByDevice;
import com.cet.eem.fusion.maintenance.core.model.param.QueryInspectionParameterRequest;
import com.cet.eem.fusion.maintenance.core.model.param.UpdateInspectionParameterRequest;
import com.cet.electric.commons.ApiResult;

import java.util.List;

public interface InspectionParameterService {

    /**
     * 新增当前项目的巡检参数
     *
     * @param addInspectionParameterRequest
     * @return
     */
    InspectionParameter addInspectionParameter(AddInspectionParameterRequest addInspectionParameterRequest);

    /**
     * 查询当前项目的巡检参数
     *
     * @return
     */
    ApiResult<List<InspectionParameter>> queryAllInspectionParameter(QueryInspectionParameterRequest queryInspectionParameterRequest);

    /**
     * 删除巡检参数
     *
     * @param ids
     */
    void deleteInspectionParameter(List<Long> ids);

    /**
     * 更新巡检参数
     *
     * @param updateInspectionParameterRequest
     * @return
     */
    InspectionParameter updateInspectionParameter(UpdateInspectionParameterRequest updateInspectionParameterRequest);

    /**
     * 根据设备查询巡检参数
     * @param queryInspectionParameterByDevice
     * @return
     */
    List<InspectionSchemeDetailVo> queryInspectionParameterByDevice(QueryInspectionParameterByDevice queryInspectionParameterByDevice);

    /**
     * 根据设备查询巡检方案
     * @param queryInspectionParameterByDevice
     * @return
     */
    List<InspectionScheme> queryInspectionSchemeByDevice(QueryInspectionParameterByDevice queryInspectionParameterByDevice);

    /**
     * 跟据巡检方案查询巡检参数
     * @param inspectionSchemeId
     * @param inspectionParameterType
     * @return
     */
    List<InspectionSchemeDetail> queryDetailByScheme(Long inspectionSchemeId,Integer inspectionParameterType);
}


