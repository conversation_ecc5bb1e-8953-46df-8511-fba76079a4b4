﻿package com.cet.eem.fusion.maintenance.core.dao.maintenance.impl;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.MaintenanceGroup;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.MaintenanceGroupWithSubLayer;
import com.cet.eem.fusion.maintenance.core.dao.maintenance.MaintenanceGroupDao;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.maintenance.core.common.model.base.SingleModelConditionDTO;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : PlanSheetDaoImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 13:46
 */
@Repository
public class MaintenanceGroupImpl extends ModelDaoImpl<MaintenanceGroup> implements MaintenanceGroupDao {

    @Override
    public List<MaintenanceGroup> queryItemGroup(Long itemId) {
        ParentQueryConditionBuilder<EntityWithName> ParentQueryConditionBuilder = new ParentQueryConditionBuilder<>(ModelLabelDef.MAINTENANCE_ITEM, itemId);
        ParentQueryConditionBuilder.selectChild(new SingleModelConditionDTO(ModelLabelDef.MAINTENANCE_GROUP));
        ApiResult<List<Map<String, Object>>> query = eemModelDataService.query(ParentQueryConditionBuilder.build());
        Map<String, Object> map = query.getData().stream().findAny().orElse(null);
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> maintenanceGroupMap = (List<Map<String, Object>>) map.get(ModelLabelDef.MAINTENANCE_GROUP + "_model");
        List<MaintenanceGroup> maintenanceGroups = JsonTransferUtils.parseList(maintenanceGroupMap, MaintenanceGroup.class);
        if (CollectionUtils.isEmpty(maintenanceGroups)) {
            return Collections.emptyList();
        }
        return maintenanceGroups;
    }

    @Override
    public List<MaintenanceGroupWithSubLayer> queryMaintenanceGroupWithItem(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaintenanceGroup> queryWrapper = LambdaQueryWrapper.of(MaintenanceGroup.class);
        queryWrapper.in(MaintenanceGroup::getId, ids);
        //查询维保分组下的维保项
        List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers = this.selectRelatedList(MaintenanceGroupWithSubLayer.class, queryWrapper);
        return maintenanceGroupWithSubLayers;
    }

    @Override
    public List<MaintenanceGroupWithSubLayer> queryMaintenanceGroupWithName(List<String> names) {
        if(CollectionUtils.isEmpty(names)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaintenanceGroup> queryWrapper = LambdaQueryWrapper.of(MaintenanceGroup.class);
        queryWrapper.in(MaintenanceGroup::getName,names);
        //查询维保分组下的维保项
        List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers = this.selectRelatedList(MaintenanceGroupWithSubLayer.class, queryWrapper);
        return maintenanceGroupWithSubLayers;
    }

    @Override
    public List<MaintenanceGroupWithSubLayer> queryMaintenanceGroupWithName(List<String> names,Long projectId) {
        if(CollectionUtils.isEmpty(names)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaintenanceGroup> queryWrapper = LambdaQueryWrapper.of(MaintenanceGroup.class);
        queryWrapper.eq(MaintenanceGroup::getProjectId,projectId).in(MaintenanceGroup::getName,names);
        //查询维保分组下的维保项
        List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers = this.selectRelatedList(MaintenanceGroupWithSubLayer.class, queryWrapper);
        return maintenanceGroupWithSubLayers;
    }
}


