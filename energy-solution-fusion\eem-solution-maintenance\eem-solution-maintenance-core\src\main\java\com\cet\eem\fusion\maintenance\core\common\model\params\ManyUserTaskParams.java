package com.cet.eem.fusion.maintenance.core.common.model.params;

import java.util.List;
import java.util.Map;

/**
 * Many user task parameters
 */
public class ManyUserTaskParams {
    private List<String> userIds;
    private Map<String, Object> variables;
    private String taskName;
    private String description;
    
    // Getters and setters
    public List<String> getUserIds() { return userIds; }
    public void setUserIds(List<String> userIds) { this.userIds = userIds; }
    
    public Map<String, Object> getVariables() { return variables; }
    public void setVariables(Map<String, Object> variables) { this.variables = variables; }
    
    public String getTaskName() { return taskName; }
    public void setTaskName(String taskName) { this.taskName = taskName; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
}