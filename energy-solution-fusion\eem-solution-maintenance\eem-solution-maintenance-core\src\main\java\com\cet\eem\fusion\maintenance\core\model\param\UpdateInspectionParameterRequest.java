package com.cet.eem.fusion.maintenance.core.model.param;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName : UpdateInspectionParameterRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 09:08
 */
@Getter
@Setter
@ApiModel(value = "UpdateInspectionParameterRequest", description = "更新巡检参数")
public class UpdateInspectionParameterRequest {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 名称
     */
    @NotEmpty(message = "巡检参数名称不能为空")
    private String name;

    /**
     * 参数类型
     * 1 状态量
     * 2 模拟量
     */
    @NotNull(message = "巡检参数类型不能为空")
    private Integer type;
}

