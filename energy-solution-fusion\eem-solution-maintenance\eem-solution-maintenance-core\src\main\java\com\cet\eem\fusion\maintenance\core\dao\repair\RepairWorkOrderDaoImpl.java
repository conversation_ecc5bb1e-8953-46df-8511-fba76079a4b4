package com.cet.eem.fusion.maintenance.core.dao.repair;

import com.cet.eem.fusion.maintenance.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderDao;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.def.WorkSheetStatusDef;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionCountSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.RepairByNodeSearchVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.RepairSearchVo;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.maintenance.core.common.model.base.SingleModelConditionDTO;
import com.cet.eem.model.tool.SubConditionBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/5/21
 */
@Service
public class RepairWorkOrderDaoImpl implements RepairWorkOrderDao {

    @Autowired
    WorkOrderDao workOrderDao;

    @Override
    public Integer queryAbnormalWorkOrderCount(InspectionCountSearchDto dto) {
        ParentQueryConditionBuilder<EntityWithName> builder = new ParentQueryConditionBuilder<>(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, dto.getTaskType())
                .where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(dto.getStartTime()))
                .where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(dto.getEndTime()))
                .where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId())
                .count(WorkOrderDef.BUSSINESS_STATUS);

        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId());
        }

        ApiResult<List<Map<String, Object>>> modelEntityList = workOrderDao.getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        List<Map<String, Object>> data = modelEntityList.getData();
        for (Map<String, Object> map : data) {
            Integer status = NumberUtils.parseInteger(map.get(WorkOrderDef.WORKSHEET_STATUS));
            Integer count = NumberUtils.parseInteger(map.get(ColumnDef.COUNT_ID));

            if (WorkSheetStatusDef.ABNORMAL.equals(status)) {
                return count;
            }
        }

        return null;
    }

    @Override
    public <T extends InspectionWorkOrderDto> ApiResult<List<T>> queryWorkOrder(RepairSearchVo dto, Long userId, Class<T> clazz) {
        ParentQueryConditionBuilder<EntityWithName> builder = new ParentQueryConditionBuilder<>(ModelLabelDef.PM_WORK_SHEET)
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP))
                .orderByDescending(ColumnDef.CREATE_TIME)
                .limit(dto.getPage());

        assemblyCondition(dto, builder);

        ApiResult<List<Map<String, Object>>> modelEntityList = workOrderDao.getModelEntityList(builder.build(), userId);
        List<T> workOrderList = JsonTransferUtils.parseList(modelEntityList.getData(), clazz);
        return Result.ok(workOrderList, modelEntityList.getTotal());
    }

    @Override
    public <T extends InspectionWorkOrderDto> ApiResult<List<T>> queryWorkOrderByNode(RepairByNodeSearchVo repairByNodeSearchVo, Class<T> clazz) {
        BaseVo node = repairByNodeSearchVo.getNode();
        SingleModelConditionDTO subCondition = new SubConditionBuilder(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP)
                .where(WorkOrderDef.DEVICE_LABEL, ConditionBlock.OPERATOR_EQ, node.getModelLabel())
                .where(WorkOrderDef.DEVICE_ID, ConditionBlock.OPERATOR_EQ, node.getId())
                .build();

        ParentQueryConditionBuilder<EntityWithName> builder = new ParentQueryConditionBuilder<>(ModelLabelDef.PM_WORK_SHEET)
                .eq(WorkOrderDef.TASK_TYPE, WorkSheetTaskType.REPAIR)
                .leftJoin(subCondition)
                .limit(repairByNodeSearchVo.getPage())
                .orderByDescending(ColumnDef.CREATE_TIME);

        ApiResult<List<Map<String, Object>>> modelEntityList = workOrderDao.getModelEntityList(builder.build(), null);
        List<T> workOrderList = JsonTransferUtils.parseList(modelEntityList.getData(), clazz);
        return Result.ok(workOrderList, modelEntityList.getTotal());
    }

    /**
     * 组装查询参数
     *
     * @param dto     查询条件
     * @param builder 模型服务查询条件构造对象
     */
    private void assemblyCondition(RepairSearchVo dto, ParentQueryConditionBuilder<EntityWithName> builder) {
        // 如果关键字为空，那么直接拼接条件
        if (StringUtils.isBlank(dto.getKeyword())) {
            assemblyCondition(dto, builder, null);
            return;
        }

        // 如果关键字不为空，那么分别根据工单号和故障描述进行过滤
        builder.composeMethod(true);
        Integer group = 1;
        builder.where(ColumnDef.CODE, ConditionBlock.OPERATOR_LIKE, dto.getKeyword(), group);
        assemblyCondition(dto, builder, group++);
        builder.where(WorkOrderDef.FAULT_DESCRIPTION, ConditionBlock.OPERATOR_LIKE, dto.getKeyword(), group);
        assemblyCondition(dto, builder, group);
    }

    /**
     * 组装查询参数
     *
     * @param dto     查询条件
     * @param builder 模型服务查询条件构造对象
     * @param group   查询条件分组
     */
    private void assemblyCondition(RepairSearchVo dto, ParentQueryConditionBuilder<EntityWithName> builder, Integer group) {
        builder.where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.REPAIR, group);
        builder.where(WorkOrderDef.CREATE_TIME, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(dto.getStartTime()), group);
        builder.where(WorkOrderDef.CREATE_TIME, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(dto.getEndTime()), group);
        builder.where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId(), group);

        if (Objects.nonNull(dto.getOverTimeOnly())) {
            builder.where(WorkOrderDef.OVER_TIME, ConditionBlock.OPERATOR_EQ, dto.getOverTimeOnly(), group);
        }

        if (Objects.nonNull(dto.getTaskLevel())) {
            builder.where(WorkOrderDef.TASK_LEVEL, ConditionBlock.OPERATOR_EQ, dto.getTaskLevel(), group);
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            if (dto.isInspectUser()) {
                builder.eq(WorkOrderDef.INSPECT_TEAM_ID, dto.getTeamId(), group);
            } else {
                builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId(), group);
            }
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getWorkSheetStatus())) {
            builder.where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_EQ, dto.getWorkSheetStatus(), group);
        }
    }

    @Override
    public List<Map<String, Object>> queryWorkOrderCountByStatus(InspectionCountSearchDto dto) {
        ParentQueryConditionBuilder<EntityWithName> builder = new ParentQueryConditionBuilder<>(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, dto.getTaskType())
                .where(ColumnDef.CREATE_TIME, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(dto.getStartTime()))
                .where(ColumnDef.CREATE_TIME, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(dto.getEndTime()))
                .where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId())
                .leftJoin(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP)
                .count(WorkOrderDef.WORKSHEET_STATUS);

        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            if (dto.isInspectUser()) {
                builder.where(WorkOrderDef.INSPECT_TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId());
            } else {
                builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId());
            }
        }

        ApiResult<List<Map<String, Object>>> modelEntityList = workOrderDao.getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        return modelEntityList.getData();
    }

    @Override
    public ApiResult<List<Map<String, Object>>> queryWorkOrderByWorkStatus(LocalDateTime st, LocalDateTime et,
                                                                                 Integer taskType, List<Integer> workOrderStatus, Long teamId, Long userId, boolean isInspectUser) {
        ParentQueryConditionBuilder<EntityWithName> builder = new ParentQueryConditionBuilder<>(ModelLabelDef.PM_WORK_SHEET)
                .selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));

        builder.where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, taskType);
        builder.where(ColumnDef.CREATE_TIME, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(st));
        builder.where(ColumnDef.CREATE_TIME, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(et));
        builder.where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_IN, workOrderStatus);
        if (ParamUtils.checkPrimaryKeyValid(teamId)) {
            if (isInspectUser) {
                builder.where(WorkOrderDef.INSPECT_TEAM_ID, ConditionBlock.OPERATOR_EQ, teamId);
            } else {
                builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, teamId);
            }
        }

        return workOrderDao.getModelEntityList(builder.build(), userId);
    }

    @Override
    public <T extends InspectionWorkOrderDto> List<T> queryWorkOrderByWorkStatus(List<Integer> workOrderStatus, Integer taskType,
                                                                                 Long teamId, LocalDateTime et, Long userId, boolean isInspectUser, Class<T> clazz) {
        ParentQueryConditionBuilder<EntityWithName> builder = new ParentQueryConditionBuilder<>(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, taskType)
                .where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_IN, workOrderStatus)
                .selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));

        if (et != null) {
            builder.where(ColumnDef.CREATE_TIME, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(et));
        }

        if (ParamUtils.checkPrimaryKeyValid(teamId)) {
            if (isInspectUser) {
                builder.where(WorkOrderDef.INSPECT_TEAM_ID, ConditionBlock.OPERATOR_EQ, teamId);
            } else {
                builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, teamId);
            }
        }

        ApiResult<List<Map<String, Object>>> modelEntityList = workOrderDao.getModelEntityList(builder.build(), userId);
        return JsonTransferUtils.transferList(modelEntityList.getData(), clazz);
    }

    @Override
    public <T extends InspectionWorkOrderDto> List<T> queryRuntimeWorkOrderByWorkStatus(List<Integer> workOrderStatus, Integer taskType,
                                                                                 Long teamId, LocalDateTime et, Long userId, boolean isInspectUser, Class<T> clazz) {
        ParentQueryConditionBuilder<EntityWithName> builder = new ParentQueryConditionBuilder<>(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, taskType)
                .where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_IN, workOrderStatus)
                .selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));

        if (et != null) {
            builder.where(ColumnDef.CREATE_TIME, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(et));
        }

        if (ParamUtils.checkPrimaryKeyValid(teamId)) {
            if (isInspectUser) {
                builder.where(WorkOrderDef.INSPECT_TEAM_ID, ConditionBlock.OPERATOR_EQ, teamId);
            } else {
                builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, teamId);
            }
        }

        ApiResult<List<Map<String, Object>>> modelEntityList = workOrderDao.getRuntimeModelEntityList(builder.build(), userId, null);
        return JsonTransferUtils.transferList(modelEntityList.getData(), clazz);
    }
}




