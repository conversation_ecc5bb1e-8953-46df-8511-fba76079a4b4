package com.cet.eem.fusion.maintenance.core.model.devicemanage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/7/17
 */
@Getter
@Setter
@ApiModel(description = "设备管理节点二维码信息模型")
public class QrInfo {
    @ApiModelProperty("节点标识")
    private String objectLabel;

    @ApiModelProperty("节点id")
    private Long objectId;

    public QrInfo() {
    }

    public QrInfo(Long objectId, String objectLabel) {
        this.objectLabel = objectLabel;
        this.objectId = objectId;
    }
}

