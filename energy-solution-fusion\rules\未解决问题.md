执行完一轮之后，剩余18个问题

2、租户的适配：
解决定时任务时的查询:查询所有的租户列表：tenantService.getAllHubTenant();

3、UnitService是根据不同的项目数据类型获取自定义单位并进行适配，当前由于融合插件拆分，理论上不允许这部分代码出现，故需要重构

4、针对节点权限的代码重构，当前需要适配多租户，多项目的节点，此部分涉及代码重构

5、如果涉及到能耗单位，则需要调用能耗插件的sdk的方法，这部分感觉也不是很好的处理，和业务强相关了，可能ai没办法处理

6、解决的了的，直接解决，解决不了的标记出来，聚焦问题

7、AuthUtils是针对老的权限相关的工具类，现在取消了这个类的提供，需要根据具体的功能分析具体的方法了

严重：迁移时出现了代码修改的问题，这个不太好把控
![img.png](img.png)
![img_1.png](img_1.png)