package com.cet.eem.fusion.maintenance.core.controller.bff.device;

import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;
import com.cet.eem.fusion.common.def.OperationAuthDef;
import com.cet.eem.fusion.maintenance.common.log.annotation.OperationLog;
import com.cet.eem.fusion.maintenance.common.log.constant.EnumOperationSubType;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DeviceSystem;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SpareParts;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SparePartsReplaceRecordVo;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.*;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.dto.QueryReplaceRecordDto;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.dto.QuerySparePartsDto;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.vo.DeviceSystemVoWithSubLayer;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.vo.SparePartsCountVo;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.vo.SparePartsDeviceVo;
import com.cet.eem.fusion.maintenance.core.service.device.SparePartsService;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName : SparePartsBffController
 * @Description : 备件管理的接口
 * <AUTHOR> Administrator
 * @Date: 2021-06-10 09:33
 */
public class SparePartsBffController {
    @Autowired
    SparePartsService sparePartsService;

    @ApiOperation(value = "根据项目查看系统和系统下的设备")
    @PostMapping(value = "/deviceAndSystem", produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<List<DeviceSystemVoWithSubLayer>> queryDeviceAndSystem() {
        List<DeviceSystemVoWithSubLayer> deviceSystemVoWithSubLayers = sparePartsService.querySpareDevice();
        return new ApiResult<>(deviceSystemVoWithSubLayers);
    }

    @ApiOperation(value = "根据设备备件查看备件库")
    @PostMapping(value = "/sparePartsList", produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<List<SpareParts>> querySpareParts(@Valid @RequestBody QuerySparePartsDto querySparePartsDto) {
        List<SpareParts> spareParts = sparePartsService.querySparePartsByDevice(querySparePartsDto);
        return new ApiResult<>(spareParts);

    }

    @ApiOperation(value = "新增备件")
    @PostMapping(value = "/spareParts")
    @OperationLog(operationType = OperationLogType.SPAREPARTS_MANAGE, subType = EnumOperationSubType.ADD, description = "新增备件")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_CREATE})
    public ApiResult<Map<String, Object>> addSpareParts(@Valid @RequestBody AddSpareParts addSpareParts) {
        Map<String, Object> spareparts = sparePartsService.createSpareparts(addSpareParts);

        return Result.ok(spareparts);
    }

    @ApiOperation(value = "新增备件系统")
    @PostMapping(value = "/deviceSystem")
    @OperationLog(operationType = OperationLogType.SPAREPARTS_MANAGE, subType = EnumOperationSubType.ADD, description = "新增备件系统")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_CREATE})
    public ApiResult<DeviceSystem> addDeviceSystem(@Valid @RequestBody AddDeviceSystem addDeviceSystem) {
        DeviceSystem deviceSystem = sparePartsService.addDeviceSystem(addDeviceSystem);
        return Result.ok(deviceSystem);
    }

    @ApiOperation(value = "新增备件分类")
    @PostMapping(value = "/sparePartsDevice")
    @OperationLog(operationType = OperationLogType.SPAREPARTS_MANAGE, subType = EnumOperationSubType.ADD, description = "新增备件分类")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_CREATE})
    public ApiResult<SparePartsDeviceVo> addSparePartsDevice(@Valid @RequestBody AddSparePartsDevice addSparePartsDevice) {
        SparePartsDeviceVo device = sparePartsService.createDevice(addSparePartsDevice);
        return Result.ok(device);
    }

    @ApiOperation(value = "编辑备件")
    @PutMapping(value = "/spareParts")
    @OperationLog(operationType = OperationLogType.SPAREPARTS_MANAGE, subType = EnumOperationSubType.UPDATE, description = "编辑备件")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_UPDATE})
    public ApiResult<SpareParts> editSpareParts(@Valid @RequestBody EditSpareParts editSpareParts) {
        SpareParts spareParts = sparePartsService.editSpareParts(editSpareParts);
        return Result.ok(spareParts);
    }

    @ApiOperation(value = "编辑备件系统")
    @PutMapping(value = "/deviceSystem")
    @OperationLog(operationType = OperationLogType.SPAREPARTS_MANAGE, subType = EnumOperationSubType.UPDATE, description = "编辑备件系统")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_UPDATE})
    public ApiResult<DeviceSystem> editDeviceSystem(@Valid @RequestBody EditDeviceSystem editDeviceSystem) {
        DeviceSystem deviceSystem = sparePartsService.editDeviceSystem(editDeviceSystem);
        return Result.ok(deviceSystem);
    }

    @ApiOperation(value = "编辑备件分类")
    @PutMapping(value = "/sparePartsDevice")
    @OperationLog(operationType = OperationLogType.SPAREPARTS_MANAGE, subType = EnumOperationSubType.UPDATE, description = "编辑备件分类")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_UPDATE})
    public ApiResult<SparePartsDeviceVo> editSparePartsDevice(@Valid @RequestBody EditDevice editDevice) {
        SparePartsDeviceVo sparePartsDevice = sparePartsService.editDevice(editDevice);
        return Result.ok(sparePartsDevice);
    }

    @ApiOperation(value = "删除备件分类")
    @RequestMapping(value = "/eem/solution/maintenance/sparePartsDevice", method = RequestMethod.DELETE, produces = "application/json")
    @OperationLog(operationType = OperationLogType.SPAREPARTS_MANAGE, subType = EnumOperationSubType.DELETE, description = "删除备件分类")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_DELETE})
    public ApiResult<Object> deleteSparePartsDevice(@RequestBody @ApiParam(value = "ids", name = "ids", required = true) List<Long> ids, @RequestParam @ApiParam(value = "systemid", name = "systemid", required = true) Long systemid) {
        //查询层级的树节点结构是否有子层级
        sparePartsService.deleteDeviceList(ids, systemid);
        return Result.ok();
    }

    @ApiOperation(value = "删除备件")
    @RequestMapping(value = "/eem/solution/maintenance/spareParts", method = RequestMethod.DELETE, produces = "application/json")
    @OperationLog(operationType = OperationLogType.SPAREPARTS_MANAGE, subType = EnumOperationSubType.DELETE, description = "删除备件")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_DELETE})
    public ApiResult<Object> deleteSpareParts(@RequestBody @ApiParam(value = "ids", name = "ids", required = true) List<Long> ids, @RequestParam @ApiParam(value = "deviceid", name = "deviceid", required = true) Long deviceid) {
        String s = sparePartsService.deleteSparePartsList(ids, deviceid);
        if (Objects.nonNull(s)) {
            return Result.ok("名称为" + s + "的备件已经被使用，无法删除", null);
        }
        return Result.ok(s, null);
    }

    @ApiOperation(value = "删除备件系统")
    @RequestMapping(value = "/eem/solution/maintenance/deviceSystem", method = RequestMethod.DELETE, produces = "application/json")
    @OperationLog(operationType = OperationLogType.SPAREPARTS_MANAGE, subType = EnumOperationSubType.DELETE, description = "删除备件系统")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_DELETE})
    public ApiResult<Object> deleteDeviceSystem(@RequestBody @ApiParam(value = "ids", name = "ids", required = true) List<Long> ids) {
        //查询层级的树节点结构是否有子层级
        sparePartsService.deleteSystemList(ids);
        return Result.ok();
    }

    @ApiOperation(value = "备件统计-按备件统计")
    @PostMapping(value = "/sparePartsByOther", produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<List<SparePartsReplaceRecordVo>> querySparePartsRecordBYOther(@Valid @RequestBody QueryReplaceRecordDto queryReplaceRecordDto) {
        ApiResult<List<SparePartsReplaceRecordVo>> listResultWithTotal = sparePartsService.queryReplaceRecordBySparePart(queryReplaceRecordDto);
        return listResultWithTotal;
    }

    @ApiOperation(value = "备件统计-按设备统计")
    @PostMapping(value = "/sparePartsByDevice", produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<List<SparePartsCountVo>> querySparePartsRecordBYDevice(@Valid @RequestBody QueryReplaceRecordDto queryReplaceRecordDto) {
        return sparePartsService.queryReplaceByDevice(queryReplaceRecordDto);

    }

    @ApiOperation(value = "将零件同步到备件库")
    @RequestMapping(value = "/eem/solution/maintenance/importSpapreByComponent", method = RequestMethod.POST, produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_IMPORT})
    public ApiResult<Object> importComponents(@Valid @RequestBody List<ModelList> deviceImportLists) {
        sparePartsService.importDeviceComponentToSpareParts(deviceImportLists);
        return Result.ok();
    }

    @ApiOperation(value = "根据具体设备查询备件记录")
    @RequestMapping(value = "/eem/solution/maintenance/sparePartsReplaceByDevice", method = RequestMethod.POST, produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<List<SparePartsReplaceRecordVo>> querySparePartsReplaceByDevice(@NotNull @RequestBody BaseVo baseVo) {
        List<SparePartsReplaceRecordVo> replaceRecordVos = sparePartsService.querySparePartsByDevice(baseVo);
        return Result.ok(replaceRecordVos);
    }

    @ApiOperation(value = "根据具体设备查询备件库")
    @RequestMapping(value = "/eem/solution/maintenance/sparePartsStorageByDevice", method = RequestMethod.POST, produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<List<SpareParts>> querySparePartsStorageByDevice(@NotNull @RequestBody BaseVo baseVo) {
        List<SpareParts> sparePartsList = sparePartsService.querySparePartsStorageByDevice(baseVo);
        return Result.ok(sparePartsList);
    }

    @ApiOperation(value = "备件统计-按备件统计导出")
    @PostMapping(value = "/exportSparePartsRepalceRecord", produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<Object> querySparePartsRecordByOtherList(@Valid @RequestBody QueryReplaceRecordDto queryReplaceRecordDto, HttpServletResponse response) throws Exception {
        sparePartsService.exportSparePartsReplaceRecord(response, queryReplaceRecordDto);
        return null;
    }

    @ApiOperation(value = "备件统计-按设备统计导出")
    @PostMapping(value = "/exportSparePartsByDevice", produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<Object> querySparePartsRecordByDeviceList(@Valid @RequestBody QueryReplaceRecordDto queryReplaceRecordDto, HttpServletResponse response) throws Exception {
        sparePartsService.exportSparePartsReplaceRecordByDevice(response, queryReplaceRecordDto);
        return null;
    }

    @ApiOperation(value = "根据设备查询备件更换记录")
    @PostMapping("/sparePartsReplaceRecord")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<List<SparePartsReplaceRecordVo>> querySparePartsReplaceRecord(@RequestParam List<Long> workOrderIds) {
        return Result.ok(sparePartsService.querySparePartsReplaceRecord(workOrderIds));
    }
}




