package com.cet.eem.fusion.maintenance.core.common.model.enumeration.subject.powermaintenance;

/**
 * Work order status enumeration
 */
public enum WorkOrderStatus {
    DRAFT("draft", "Draft"),
    PENDING("pending", "Pending"),
    IN_PROGRESS("in_progress", "In Progress"),
    COMPLETED("completed", "Completed"),
    CANCELLED("cancelled", "Cancelled");
    
    private final String code;
    private final String description;
    
    WorkOrderStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() { return code; }
    public String getDescription() { return description; }
}