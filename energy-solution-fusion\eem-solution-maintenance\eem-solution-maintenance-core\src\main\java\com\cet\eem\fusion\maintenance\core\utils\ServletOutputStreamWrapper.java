package com.cet.eem.fusion.maintenance.core.utils;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2023/1/5
 */
public class ServletOutputStreamWrapper extends ServletOutputStream {

    private ByteArrayOutputStream outputStream;
    private HttpServletResponse response;

    public ServletOutputStreamWrapper(ByteArrayOutputStream outputStream, HttpServletResponse response) {
        this.outputStream = outputStream;
        this.response = response;
    }

    @Override
    public boolean isReady() {
        return true;
    }

    @Override
    public void setWriteListener(WriteListener listener) {

    }

    @Override
    public void write(int b) throws IOException {
        this.outputStream.write(b);
    }

    @Override
    public void flush() throws IOException {
        if (!this.response.isCommitted()) {
            byte[] body = this.outputStream.toByteArray();
            ServletOutputStream outputStream = this.response.getOutputStream();
            outputStream.write(body);
            outputStream.flush();
        }
    }
}
