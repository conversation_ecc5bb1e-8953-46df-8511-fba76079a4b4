package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.fusion.maintenance.core.model.InspectionSchemeWithSubLayer;

import java.util.List;


public interface InspectionSchemeDao extends BaseModelDao<InspectionScheme> {
    List<InspectionSchemeWithSubLayer> query(List<Long> ids);
}


