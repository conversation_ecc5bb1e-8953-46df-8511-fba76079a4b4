package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.fusion.common.annotation.ModelDao;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.InspectionSchemeWithSubLayer;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.List;


public interface InspectionSchemeDao extends BaseModelDao<InspectionScheme> {
    List<InspectionSchemeWithSubLayer> query(List<Long> ids);
}


