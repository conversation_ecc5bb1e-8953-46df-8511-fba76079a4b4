package com.cet.eem.fusion.maintenance.core.dao.devicemanager;

import com.cet.electric.commons.ApiResult;

import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;

import java.util.List;
import java.util.Map;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-14
 */
public interface DeviceManagerServiceDao {
    List<Map<String,Object>> queryDevice(QueryCondition queryCondition);

    ApiResult<List<Map<String,Object>>> query(QueryCondition queryCondition);

}


