package com.cet.eem.fusion.maintenance.core.bll.common.model.ext.subject.powermaintenance;

import java.util.Date;
import java.util.List;

/**
 * 设备与子层
 */
public class DeviceWithSubLayer {
    
    private Long id;
    private String name;
    private String code;
    private String type;
    private String model;
    private String manufacturer;
    private Date createTime;
    private Date updateTime;
    private Long projectId;
    private Long tenantId;
    private Integer status;
    private List<Object> subLayers;
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public String getManufacturer() {
        return manufacturer;
    }
    
    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public Long getProjectId() {
        return projectId;
    }
    
    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }
    
    public Long getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public List<Object> getSubLayers() {
        return subLayers;
    }
    
    public void setSubLayers(List<Object> subLayers) {
        this.subLayers = subLayers;
    }
}
