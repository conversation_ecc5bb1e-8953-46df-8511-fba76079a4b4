package com.cet.eem.fusion.maintenance.core.controller.inspect;

import com.cet.eem.fusion.common.service.auth.NodeManageWithAuthService;
import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.TechParamValue;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet.*;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectRecordSheetService;
import com.cet.eem.fusion.maintenance.common.definition.LoginDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : InspectionRecordSheetController
 * @Description : 巡检记录表
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-10 13:39
 */
@Api(value = "InspectionRecordSheetController", tags = {"巡检记录表接口"})
@RestController
@RequestMapping(value = "/eem/solution/maintenance/eem/v1/inspect/recordSheet")
public class InspectionRecordSheetController {
    @Autowired
    InspectRecordSheetService inspectRecordSheetService;
    @Autowired
    NodeManageWithAuthService nodeManageBffService;

    @ApiOperation(value = "查询快速查询模板")
    @PostMapping(value = "/template", produces = "application/json")
    public ApiResult<List<QueryTemplateWithNodeName>> queryTemplate() {
        return Result.ok(inspectRecordSheetService.queryTemplate(GlobalInfoUtils.getTenantId()));
    }

    @ApiOperation(value = "查询巡检对象节点树")
    @PostMapping(value = "/nodeTree", produces = "application/json")
    public ApiResult<List<BaseVo>> queryInspectionNodeTree(@RequestBody @ApiParam(name = "condition", value = "节点树参数", required = true) EemQueryCondition condition) {
        List<Map<String, Object>> nodeTree = nodeManageBffService.querySimpleNodeTree(condition, LoginDef.USER_ROOT);
        return Result.ok(inspectRecordSheetService.queryInspectionNodeTree(JsonTransferUtils.transferList(nodeTree, BaseVo.class)));
    }

    @ApiOperation(value = "新建查询文件夹、模板")
    @PostMapping(value = "/folder", produces = "application/json")
    public ApiResult<QueryTemplate> createPatrolFavorites(@RequestBody AddQuickQueryTemplate addQuickQueryTemplate) {
        return Result.ok(inspectRecordSheetService.addQueryTemplate(addQuickQueryTemplate, GlobalInfoUtils.getTenantId()));
    }

    @ApiOperation(value = "重命名模板、文件夹")
    @PutMapping(value = "/template", produces = "application/json")
    public ApiResult<QueryTemplate> updateQuickTemplate(@RequestBody UpdateQuickQueryTemplate queryTemplate) {
        return Result.ok(inspectRecordSheetService.updateQueryTemplate(queryTemplate));
    }

    @ApiOperation(value = "删除文件夹和模板")
    @DeleteMapping(value = "/template", produces = "application/json")
    public ApiResult<Object> deleteTemplateAndFolder(@RequestBody List<Long> ids) {
        inspectRecordSheetService.deleteTemplateAndFolder(ids, GlobalInfoUtils.getTenantId());
        return Result.ok();
    }

    @ApiOperation(value = "查询巡检记录表")
    @PostMapping(value = "/query", produces = "application/json")
    public ApiResult<List<InspectRecordSheetVo>> queryRecordSheet(@RequestBody RecordSheetQueryParam param) {
        return (inspectRecordSheetService.queryRecordSheet(param, GlobalInfoUtils.getTenantId()));
    }

    @ApiOperation(value = "导出巡检记录表")
    @PostMapping(value = "/export", produces = "application/json")
    public void exportRecordSheet(@RequestBody RecordSheetQueryParam param, HttpServletResponse response) {
        inspectRecordSheetService.exportRecordSheet(param, response, GlobalInfoUtils.getTenantId());
        //return Result.ok();
    }

    @ApiOperation(value = "巡检参数标题列表")
    @PostMapping(value = "/title", produces = "application/json")
    public ApiResult<List<String>> queryTitle(@RequestBody List<Long> schemeId) {
        return Result.ok(inspectRecordSheetService.queryTitle(schemeId));
    }

    @ApiOperation(value = "巡检方案列表")
    @PostMapping(value = "/inspectScheme", produces = "application/json")
    public ApiResult<List<InspectionScheme>> queryInspectionSchemeByNodes(@RequestBody List<BaseVo> nodes) {
        return Result.ok(inspectRecordSheetService.queryInspectionSchemeByNodes(nodes));
    }

    @ApiOperation(value = "设备信息")
    @PostMapping(value = "/nodeInfo", produces = "application/json")
    public ApiResult<List<TechParamValue>> queryNodeParamInfo(@RequestBody List<BaseVo> node) {
        return Result.ok(inspectRecordSheetService.queryNodeParamInfo(node));
    }
}


