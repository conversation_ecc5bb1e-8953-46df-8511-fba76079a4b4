package com.cet.eem.fusion.maintenance.core.def;

/**
 * Operation log type definitions for fusion framework
 */
public enum OperationLogType {
    
    // Maintenance operations
    MAINTENANCE_ADD("MAINTENANCE_ADD", "新增维护"),
    MAINTENANCE_UPDATE("MAINTENANC<PERSON>_UPDATE", "更新维护"),
    MAINTENANCE_DELETE("MAINTENANCE_DELETE", "删除维护"),
    MAINTENANCE_EXPORT("MAINTENANCE_EXPORT", "导出维护"),
    MAINTENANCE_IMPORT("MAINTENANCE_IMPORT", "导入维护"),
    
    // Inspection operations
    INSPECTION_ADD("INSPECTION_ADD", "新增巡检"),
    INSPECTION_UPDATE("INSPECTION_UPDATE", "更新巡检"),
    INSPECTION_DELETE("INSPECTION_DELETE", "删除巡检"),
    INSPECTION_EXPORT("INSPECTION_EXPORT", "导出巡检"),
    INSPECTION_IMPORT("INSPECTION_IMPORT", "导入巡检"),
    
    // Device operations
    DEVICE_ADD("DEVICE_ADD", "新增设备"),
    DEVICE_UPDATE("DEVICE_UPDATE", "更新设备"),
    DEVICE_DELETE("DEVICE_DELETE", "删除设备"),
    DEVICE_EXPORT("DEVICE_EXPORT", "导出设备"),
    DEVICE_IMPORT("DEVICE_IMPORT", "导入设备"),
    
    // Work order operations
    WORKORDER_ADD("WORKORDER_ADD", "新增工单"),
    WORKORDER_UPDATE("WORKORDER_UPDATE", "更新工单"),
    WORKORDER_DELETE("WORKORDER_DELETE", "删除工单"),
    WORKORDER_EXPORT("WORKORDER_EXPORT", "导出工单"),
    WORKORDER_APPROVE("WORKORDER_APPROVE", "审批工单"),
    WORKORDER_REJECT("WORKORDER_REJECT", "拒绝工单"),
    
    // Repair operations
    REPAIR_ADD("REPAIR_ADD", "新增报修"),
    REPAIR_UPDATE("REPAIR_UPDATE", "更新报修"),
    REPAIR_DELETE("REPAIR_DELETE", "删除报修"),
    REPAIR_EXPORT("REPAIR_EXPORT", "导出报修"),
    
    // Sign in operations
    SIGNIN_ADD("SIGNIN_ADD", "新增签到"),
    SIGNIN_UPDATE("SIGNIN_UPDATE", "更新签到"),
    SIGNIN_DELETE("SIGNIN_DELETE", "删除签到"),
    SIGNIN_EXPORT("SIGNIN_EXPORT", "导出签到"),
    
    // Handover operations
    HANDOVER_ADD("HANDOVER_ADD", "新增交接"),
    HANDOVER_UPDATE("HANDOVER_UPDATE", "更新交接"),
    HANDOVER_DELETE("HANDOVER_DELETE", "删除交接"),
    HANDOVER_EXPORT("HANDOVER_EXPORT", "导出交接");
    
    private final String code;
    private final String description;
    
    OperationLogType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static OperationLogType fromCode(String code) {
        for (OperationLogType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}
