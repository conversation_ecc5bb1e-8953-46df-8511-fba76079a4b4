package com.cet.eem.fusion.maintenance.core.dao.impl;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.entity.Result;
import com.cet.eem.fusion.common.modelutils.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.utils.SortUtils;
import com.cet.eem.fusion.common.utils.datatype.NumberUtils;
import com.cet.eem.fusion.maintenance.core.common.utils.performance.annotation.ExecuteIndexAnnotation;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.InspectionWorkOrderDao;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderDao;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.def.WorkSheetStatusDef;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionCountSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionParameterWorkOrderDTO;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.SubConditionBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/21
 */
@Service
@ExecuteIndexAnnotation
public class InspectionWorkOrderDaoImpl implements InspectionWorkOrderDao {

    @Autowired
    WorkOrderDao workOrderDao;

    @Override
    public Integer queryAbnormalWorkOrderCount(InspectionCountSearchDto dto) {
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, dto.getTaskType())
                .where(ColumnDef.CREATE_TIME, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(dto.getStartTime()))
                .where(ColumnDef.CREATE_TIME, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(dto.getEndTime()))
                .where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId())
                .count(WorkOrderDef.BUSSINESS_STATUS);

        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId());
        }

        ApiResult<List<Map<String, Object>>> modelEntityList = workOrderDao.getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        List<Map<String, Object>> data = modelEntityList.getData();
        for (Map<String, Object> map : data) {
            Integer status = NumberUtils.parseInteger(map.get(WorkOrderDef.WORKSHEET_STATUS));
            Integer count = NumberUtils.parseInteger(map.get(ColumnDef.COUNT_ID));

            if (WorkSheetStatusDef.ABNORMAL.equals(status)) {
                return count;
            }
        }

        return null;
    }

    @Override
    public ApiResult<List<InspectionParameterWorkOrderDTO>> queryInspectionParameterWorkOrder(InspectionSearchDto dto, Long userId) {
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP))
                .select(WorkOrderDef.FINISH_TIME, WorkOrderDef.MAINTENANCE_CONTENT, ColumnDef.ID)
                .orderByDescending(ColumnDef.EXECUTE_TIME_PLAN)
                .limit(dto.getPage());

        assemblyCondition(dto, builder);

        ApiResult<List<Map<String, Object>>> modelEntityList = workOrderDao.getModelEntityList(builder.build(), userId);
        List<InspectionParameterWorkOrderDTO> workOrderList = JsonTransferUtils.transferList(modelEntityList.getData(), InspectionParameterWorkOrderDTO.class);

        return Result.ok(workOrderList, modelEntityList.getTotal());
    }

    @Override
    public ApiResult<List<InspectionWorkOrderDto>> queryWorkOrder(InspectionSearchDto dto, Long userId) {
        // 由于目前工单服务仅支持两个表的关联查询，因此在查询异常原因的情况下需要拆为两步进行查询
        if (CollectionUtils.isEmpty(dto.getAbnormalTypes())) {
            return workOrderDao.queryWorkOrder(dto, userId);
        }

        SubConditionBuilder subConditionBuilder = new SubConditionBuilder(ModelLabelDef.WORKSHEET_ABNORMAL_REASON)
                .where(ColumnDef.TYPE, ConditionBlock.OPERATOR_IN, dto.getAbnormalTypes());
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .select(Collections.singletonList(ColumnDef.ID))
                .distinct()
                .leftJoin(subConditionBuilder.build())
                .limit(dto.getPage());
        assemblyCondition(dto, builder);

        ApiResult<List<Map<String, Object>>> workOrderWithAbnormalReasonsResult = workOrderDao.getModelEntityList(builder.build(), userId);
        List<InspectionWorkOrderDto> tmpDataList = JsonTransferUtils.transferList(workOrderWithAbnormalReasonsResult.getData(), InspectionWorkOrderDto.class);

        Set<Long> workOrderIds = tmpDataList.stream().map(EntityWithName::getId).collect(Collectors.toSet());
        List<InspectionWorkOrderDto> workOrderWithObject = workOrderDao.queryWorkOrderByIds(workOrderIds);
        Map<Long, InspectionWorkOrderDto> inspectObjectMap = workOrderWithObject.stream().collect(Collectors.toMap(EntityWithName::getId, it -> it));
        List<InspectionWorkOrderDto> result = new ArrayList<>();
        for (InspectionWorkOrderDto data : tmpDataList) {
            InspectionWorkOrderDto obj = inspectObjectMap.get(data.getId());
            if (obj != null) {
                result.add(obj);
                obj.setAbnormalReasonList(data.getAbnormalReasonList());
            }
        }
        result.sort((v1, v2) -> SortUtils.sort(v1.getCreateTime(), v2.getCreateTime(), false));
        return Result.ok(result, workOrderWithAbnormalReasonsResult.getTotal());
    }

    private void assemblyCondition(InspectionSearchDto dto, ParentQueryConditionBuilder builder) {
        builder.where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, dto.getTaskType());
        builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(dto.getStartTime()));
        builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(dto.getEndTime()));

        if (StringUtils.isNotBlank(dto.getCode())) {
            builder.where(ColumnDef.CODE, ConditionBlock.OPERATOR_LIKE, dto.getCode());
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId());
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getSignPointId())) {
            builder.where(WorkOrderDef.SIGN_POINT_ID, ConditionBlock.OPERATOR_EQ, dto.getSignPointId());
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getSignGroupId())) {
            builder.where(WorkOrderDef.SIGN_GROUP_ID, ConditionBlock.OPERATOR_EQ, dto.getSignGroupId());
        }
        if (ParamUtils.checkPrimaryKeyValid(dto.getWorkSheetStatus())) {
            if (WorkSheetStatusDef.ABNORMAL.equals(dto.getWorkSheetStatus())) {
                builder.where(WorkOrderDef.BUSSINESS_STATUS, ConditionBlock.OPERATOR_EQ, dto.getWorkSheetStatus());
            } else {
                builder.where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_EQ, dto.getWorkSheetStatus());
            }
        }
    }

}




