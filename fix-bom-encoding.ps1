# Fix BOM encoding issues in Java files
Write-Host "Fixing BOM encoding issues in Java files..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

function Remove-BOM {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    try {
        # Read the file as bytes
        $bytes = [System.IO.File]::ReadAllBytes($filePath)
        
        # Check if file starts with UTF-8 BOM (EF BB BF)
        if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
            Write-Host "  - Removing BOM from: $filePath" -ForegroundColor Yellow
            
            # Remove the first 3 bytes (BOM)
            $newBytes = $bytes[3..($bytes.Length - 1)]
            
            # Write back without BOM
            [System.IO.File]::WriteAllBytes($filePath, $newBytes)
            
            return $true
        }
    }
    catch {
        Write-Host "  - Error processing file: $filePath - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    return $false
}

# Process all Java files
Write-Host "Scanning Java files for BOM issues..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$fixedFiles = 0

foreach ($file in $javaFiles) {
    if (Remove-BOM -filePath $file.FullName) {
        $fixedFiles++
        Write-Host "Fixed BOM in: $($file.Name)" -ForegroundColor Green
    }
}

Write-Host "`nBOM fix completed!" -ForegroundColor Green
Write-Host "Total files fixed: $fixedFiles" -ForegroundColor Cyan

# Verify no more BOM issues
Write-Host "`nVerifying BOM fix..." -ForegroundColor Cyan
$remainingBomFiles = 0

foreach ($file in $javaFiles) {
    try {
        $bytes = [System.IO.File]::ReadAllBytes($file.FullName)
        if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
            Write-Host "Still has BOM: $($file.Name)" -ForegroundColor Red
            $remainingBomFiles++
        }
    }
    catch {
        Write-Host "Error checking file: $($file.Name)" -ForegroundColor Red
    }
}

if ($remainingBomFiles -eq 0) {
    Write-Host "SUCCESS: All BOM issues have been fixed!" -ForegroundColor Green
} else {
    Write-Host "WARNING: Still have $remainingBomFiles files with BOM issues" -ForegroundColor Red
}

Write-Host "`nScript execution completed!" -ForegroundColor Green
