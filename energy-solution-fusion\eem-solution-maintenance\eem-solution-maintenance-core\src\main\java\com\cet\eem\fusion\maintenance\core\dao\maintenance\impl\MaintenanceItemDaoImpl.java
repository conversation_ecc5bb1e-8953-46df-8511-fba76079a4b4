﻿package com.cet.eem.fusion.maintenance.core.dao.maintenance.impl;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.MaintenanceItem;
import com.cet.eem.fusion.maintenance.core.dao.maintenance.MaintenanceItemDao;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryResultContentTaker;
import com.cet.eem.fusion.maintenance.common.toolkit.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/13
 */
@Service
public class MaintenanceItemDaoImpl extends ModelDaoImpl<MaintenanceItem> implements MaintenanceItemDao {
    private final ModelServiceUtils modelServiceUtils;

    public MaintenanceItemDaoImpl(ModelServiceUtils modelServiceUtils) {
        this.modelServiceUtils = modelServiceUtils;
    }

    @Override
    public List<MaintenanceItem> queryMaintenanceByPlanSheet(Long planSheetId) {
        QueryCondition condition = new ParentQueryConditionBuilder<>(ModelLabelDef.PLAN_SHEET)
                .where(ColumnDef.ID, ConditionBlock.OPERATOR_EQ, planSheetId)
                .leftJoin(Collections.singletonList(ModelLabelDef.MAINTENANCE_ITEM))
                .queryAsTree()
                .build();

        List<Map<String, Object>> result = modelServiceUtils.query(condition);
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }

        return JsonTransferUtils.transferList(QueryResultContentTaker.getChildren(result.get(0)), MaintenanceItem.class);
    }

    @Override
    public List<Map<String, Object>> queryMaintenanceWithPlanSheet(Collection<Long> itemIds) {
        Assert.notEmpty(itemIds, "入参不允许为空！");

        QueryCondition condition = new ParentQueryConditionBuilder<>(ModelLabelDef.MAINTENANCE_ITEM)
                .where(ColumnDef.ID, ConditionBlock.OPERATOR_IN, itemIds)
                .leftJoin(Collections.singletonList(ModelLabelDef.PLAN_SHEET))
                .queryAsTree()
                .build();

        return modelServiceUtils.query(condition);
    }

    @Override
    public ApiResult<List<MaintenanceItem>> queryMaintenanceByTypes(List<Long> types, Page page) {
        Assert.notEmpty(types, "入参不允许为空！");

        LambdaQueryWrapper<MaintenanceItem> wrapper = LambdaQueryWrapper.of(MaintenanceItem.class)
                .in(MaintenanceItem::getMaintenanceType, types);

        return this.selectPage(wrapper, page);
    }
}


