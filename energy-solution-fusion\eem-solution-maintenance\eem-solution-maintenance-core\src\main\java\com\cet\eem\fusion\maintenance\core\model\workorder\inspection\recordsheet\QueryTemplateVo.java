package com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet;

import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : QueryTemplateVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-19 09:15
 */
@Getter
@Setter
public class QueryTemplateVo extends EntityWithName {
    private TemplateDetailVo detail;
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
}

