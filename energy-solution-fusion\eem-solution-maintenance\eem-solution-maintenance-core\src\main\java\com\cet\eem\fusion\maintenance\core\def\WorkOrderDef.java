package com.cet.eem.fusion.maintenance.core.def;

/**
 * 工单定义
 *
 * <AUTHOR>
 * @date 2020/4/28 15:41
 */
public class WorkOrderDef {

    public static final String MODEL = "pmworksheet";

    public static final String CODE = "code";

    /**
     * 任务描述
     */
    public static final String TASK_DESCRIPTION = "taskdescription";

    public static final String HAZARD_ANALYSIS = "hazardanalysis";

    public static final String ATTACHMENT = "attachment";

    public static final String TRAINEE = "trainee";

    public static final String HAZARD_ATTACHMENT = "hazardattachment";

    public static final String SAFETY_MEASURE = "safetymeasure";

    public static final String SAFETY_MEASURE_ATTACHMENT = "safetymeasureattachment";

    public static final String TASK_TYPE = "tasktype";

    public static final String TASK_CONTENT = "taskcontent";

    public static final String CREATE_TIME = "createtime";

    public static final String CREATOR = "creator";

    public static final String CREATOR_NAME = "creatorname";

    public static final String PLANSHEET_ID = "plansheetid";

    public static final String PROJECT_ID = "project_id";

    public static final String PROCESS_DEFINITION_KEY = "processdefinitionkey";

    public static final String PROCESS_DEFINITION_ID = "processdefinitionid";

    public static final String PROCESS_INSTANCE_ID = "processinstanceid";

    public static final String WORKSHEET_STATUS = "worksheetstatus";

    public static final String TIME_CONSUME_PLAN = "timeconsumeplan";

    public static final String TEAM_ID = "teamid";

    public static final String TIME_CONSUME = "timeconsume";

    public static final String STAFF = "staff";

    public static final String STAFFNAME = "givenstaffname";

    public static final String TASK_LEVEL = "tasklevel";

    public static final String ID = "id";

    public static final String EXECUTE_TIME = "executetime";

    /**
     * 计划开始时间
     */
    public static final String EXECUTE_TIME_PLAN = "executetimeplan";

    public static final String MAINTENANCE_CONTENT = "maintenancecontent";

    public static final String OPINION = "opinion";

    public static final String FINISH_TIME = "finishtime";

    public static final String FINISH_TIME_PLAN = "finishtimeplan";

    public static final String TASK_ID = "taskid";

    public static final String REJECT_ENABLED = "rejectenabled";

    public static final String NO_NUMBER = "0";

    public static final String UNASSIGN_OR_REPAIR = "1";

    public static final String ASSIGN_OR_CHECK = "2";

    public static final String HANDLED_OR_TEST = "3";

    public static final String MODEL_LABEL = "modelLabel";

    public static final String PROCESS_DESCRIPITON = "processdescription";

    public static final String PROJECT_NAME = "projectname";

    public static final String GIVEN_STAFF_NAME = "givenstaffname";

    public static final String CANCEL_TIME_OUT = "cancelTimeOut";

    public static final String ASSIGN_TIME = "assignTime";

    public static final String SIANINSTATISTICSTABLE_ID = "signinstatisticstable_id";

    public static final String UNDEFINED = "undefined";

    Long PARSE_SECOND = 60000L;

    public static final String SECTIONAREA_NAME = "sectionarea_name";

    public static final String SECTIONAREA_ID = "sectionarea_id";

    public static final String ROOM_LOCATION = "roomlocation";

    public static final String ROOM_ID = "roomid";

    public static final String ROOM_NAME = "roomname";

    public static final String ASSIGN_SITUATION = "assignSituation";

    public static final String TIME_OUT_COUNT = "timeoutcount";

    public static final String UNCOMPLETED_COUNT = "uncompletedcount";

    public static final String COMPLETED_COUNT = "completedcount";

    public static final String OPERATION_ORDER_ID = "operationorder_id";

    public static final String DEVICE_RELATION_LIST = "deviceRelationList";

    public static final String EVENT_LIST = "eventList";
    public static final String RELATED_WORK_ORDER = "relatedworkorder";

    public static final String FAULT_DESCRIPTION = "faultdescription";

    public static final String INSPECTION_SCHEME_ID = "inspectionschemeid";

    public static final String DEVICE_PLAN_RELATIONSHIP_MODEL = "deviceplanrelationship_model";

    public static final String BUSSINESS_STATUS = "bussinessstatus";

    public static final String WORKSHEET_ABNORMAL_REASON_MODEL = "worksheetabnormalreason_model";

    public static final String SIGN_GROUP_ID = "signgroupid";

    public static final String HANDLE_DESCRIPTION = "handledescription";

    public static final String SOURCE_TYPE = "sourcetype";

    public static final String SOURCE_MODEL = "sourcemodel";

    public static final String SOURCE_ID = "sourceid";

    public static final String SOURCE_TIME = "sourcetime";

    public static final String REPAIR_TYPE = "repairtype";

    public static final String OVER_TIME_REASON = "overtimereason";

    public static final String OVER_TIME = "overtime";

    public static final String HANDLE_ATTACHMENTS = "handleattachments";

    public static final String EVENT_PLAN_ID = "eventplanid";

    public static final String SIGN_POINT_ID = "signpointid";

    public static final String PERSON_NUMBER = "personnumber";

    public static final String DEVICE_ID = "device_id";

    public static final String DEVICE_LABEL = "device_label";

    public static final String FILL_FORM_TYPE = "fillformtype";

    public static final String INSPECT_CONFIRM_TIME = "inspectconfirmtime";

    public static final String REPAIR_CONFIRM_TIME = "repairconfirmtime";

    public static final String INSPECT_TEAM_ID = "inspectteamid";

    public static final String RELATED_CODE = "relatedcode";

    public static final String RELATED_CODE_ID = "relatedcodeid";

    public static final String SOURCE_INDEX = "sourceindex";
    /**
     * 维修类型
     */
    public static final String REPAIR_CATEGORY = "repaircategory";
    /**
     * 工单类别--维修专业类别
     */
    public static final String PROFESSIONAL_CATEGORY= "professionalcategory";
}

