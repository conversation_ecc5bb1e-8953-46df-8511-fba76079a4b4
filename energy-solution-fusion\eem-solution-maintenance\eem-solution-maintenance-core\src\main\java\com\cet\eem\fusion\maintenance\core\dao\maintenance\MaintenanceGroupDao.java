package com.cet.eem.fusion.maintenance.core.dao.maintenance;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.MaintenanceGroup;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.MaintenanceGroupWithSubLayer;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : MaintenanceGroupDao
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 13:46
 */
public interface MaintenanceGroupDao extends BaseModelDao<MaintenanceGroup> {

    /**
     * 查询巡检项的分组信息
     *
     * @param itemId
     * @return
     */
    List<MaintenanceGroup> queryItemGroup(Long itemId);

    /**
     * 根据维保分组id查询其下的维保项目
     * @param ids
     * @return
     */
    List<MaintenanceGroupWithSubLayer> queryMaintenanceGroupWithItem(List<Long> ids);

    /**
     * 根据维保分组的名称查询其下的维保项目
     * @param names
     * @return
     */
    List<MaintenanceGroupWithSubLayer> queryMaintenanceGroupWithName(List<String> names);

    /**
     * 根据维保分组的名称查询其下的维保项目
     * @param names
     * @return
     */
    List<MaintenanceGroupWithSubLayer> queryMaintenanceGroupWithName(List<String> names,Long projectId);
}


