package com.cet.eem.solution.common.def.common.label;

/**
 * <AUTHOR>  (2025/7/24 9:46)
 */
public class ModelLabelDef {
    public static final String DEMO = "demo";
    /**
     * 能源计划
     */
    public static final String ENERGY_CONSUME_PLAN = "energyconsumeplan";

    /**
     * 区域
     */
    public static final String SECTIONAREA = "sectionarea";
    /**
     * 楼层
     */
    public static final String FLOOR = "floor";
    /**
     * 楼栋
     */
    public static final String BUILDING = "building";
    /**
     * 节点关联关系
     */
    public static final String NODERELATIONSHIP = "noderelationship";

    /**
     * 产品计划
     */
    public static final String PRODUCTION_PLAN = "productionplan";

    /**
     * 产量
     */
    public static final String PRODUCTION_DATA = "productiondata";

    /**
     * 产量修正数据
     */
    public static final String PIPELINE_SUPPLY_TO = "pipelinesupplyto";
    /**
     * 生产设备关联表
     */
    public static final String PRODUCTION_EQUIPMENT_ASSOCIATION = "productionequipmentassociation";
    /**
     * 生产补充数据表
     */
    public static final String PRODUCT_SUPPLEMENT_DATA = "productsupplementdata";

    /**
     * 分时时段名设置
     */
    public static final String TIME_SHARE_PERIOD = "timeshareperiod";

    /**
     * 分时电费价格
     */
    public static final String ELECTRIC_PRICE = "electricprice";

    /**
     * 计量器具
     */
    public static final String MEASURING_INSTRUMENT = "measuringinstrument";

    /**
     * 物理量定义模板
     */
    public static final String QUANTITYDEFINITIONTEMPLATE = "quantitydefinitiontemplate";

    /**
     * 计量器具日志
     */
    public static final String MEASURING_INSTRUMENT_LOG = "measuringinstrumentlog";

    /**
     * 物理量聚合数据
     */
    public static final String QUANTITYAGGREGATIONDATA = "quantityaggregationdata";
    /**
     * 项目能源类型
     */
    public static final String PROJECT_ENERGY_TYPE = "projectenergytype";

    /**
     * 报警方案
     */
    public static final String ALARM_SCHEME = "alarmscheme";

    /**
     * 报警等级设置
     */
    public static final String ALARM_LEVEL_CONFIG = "alarmlevelconfig";

    /**
     * 对标方案
     */
    public static final String BENCH_MARK_SET = "benchmarkset";

    /**
     * 管道
     */
    public static final String PIPE_LINE = "pipeline";

    /**
     * 开关柜
     */
    public static final String LINE_SEGMENT_WITH_SWITCH = "linesegmentwithswitch";

    /**
     * 折标煤系数
     */
    public static final String CONVERTED_STANDARD_COAL_COEF = "convertedstandardcoalcoef";

    /**
     * 生产设备模型模板
     */
    public static final String MANUEQUIPMENT = "manuequipment";

    /**
     * 变压器
     */
    public static final String POWERTRANSFORMER = "powertransformer";

    /**
     * 产品
     */
    public static final String PRODUCT = "product";

    /**
     * 能耗数据
     */
    public static final String ENERGY_CONSUMPTION = "energyconsumption";

    /**
     * 总能耗预测数据
     */
    public static final String TOTAL_ENERGY_CONSUMPTION_PREDICT = "totalenergyconsumptionpredict";

    /**
     * 聚合数据
     */
    public static final String QUANTITY_AGGREGATION_DATA = "quantityaggregationdata";

    /**
     * 物理量对象
     */
    public static final String QUANTITY_OBJECT = "quantityobject";
    /**
     * 物理量对象映射模型
     */
    public static final String QUANTITYOBJECTMAP = "quantityobjectmap";

    /**
     * 供能关系
     */
    public static final String ENERGY_SUPPLY_TO = "energysupplyto";

    /**
     * 系统事件
     */
    public static final String SYSTEM_EVENT = "systemevent";

    /**
     * 运行工况诊断事件
     */
    public static final String WORKING_CONDITION_EVENT = "workingconditionevent";

    /**
     * 能效维度表
     */
    public static final String EFFICIENCY_DIMENSION = "efficiencydimension";

    /**
     * 能效维度系统表
     */
    public static final String EFFICIENCY_DIM_SYSTEM = "efficiencydimsystem";

    /**
     * 能效维度节点表
     */
    public static final String EFFICIENCY_DIM_NODES = "efficiencydimnodes";

    /**
     * 节能评价聚合数据
     */
    public static final String ENERGY_SAVING_EVALUATION_AGGR_DATA = "energysavingevaluationaggrdata";

    /******************************************油气田******************************************/
    /**
     * 机采设备
     */
    public static final String MECHANICAL_MINING_MACHINE = "mechanicalminingmachine";

    /**
     * 油气田录入参数code
     */
    public static final String OILPARAMENTRYCODE = "oilparamentrycode";

    /**
     * 油田公司
     */
    public static final String PROJECT = "project";

    /**
     * 油田公司
     */
    public static final String OIL_COMPANY = "oilcompany";

    /**
     * 采油厂
     */
    public static final String OIL_PRODUCTION_PLANT = "oilproductionplant";

    /**
     * 气矿/储气管理处
     */
    public static final String GAS_MINE = "gasmine";

    /**
     * 净化总公司
     */
    public static final String PURIFICATION_COMPANY = "purificationcompany";

    /**
     * 作业区
     */
    public static final String OPERATION_AREA = "operationarea";

    /**
     * 气田作业区，储气库
     */
    public static final String GAS_OPERATION_AREA = "gasoperationarea";

    /**
     * 油气田通用设备
     */
    public static final String OIL_COMMON_EQUIPMENT = "oilcommonequipment";

    /**
     * 采油队
     */
    public static final String OIL_PRODUCTION_CREW = "oilproductioncrew";

    /**
     * 转油站
     */
    public static final String OIL_TRANSFER_STATION = "oiltransferstation";

    /**
     * 注水站
     */
    public static final String WATER_INJECTION_STATION = "waterinjectionstation";

    /**
     * 联合站
     */
    public static final String COMBINED_STATION = "combinedstation";

    /**
     * 平台
     */
    public static final String PLATFORM = "platform";

    /**
     * 采气平台
     */
    public static final String GAS_PLATFORM = "gasplatform";

    /**
     * 脱水站
     */
    public static final String DEHYDRATING_STATION = "dehydratingstation";

    /**
     * 集气站
     */
    public static final String GAS_GATHERING_STATION = "gasgatheringstation";

    /**
     * 注水平台
     */
    public static final String WATER_INJECTION_PLATFORM = "waterinjectionplatform";

    /**
     * 注水井
     */
    public static final String WATER_INJECTION_WELL = "waterinjectionwell";

    /**
     * 天然气压缩机瞬时数据
     */
    public static final String GASCOMPRESSOREFFECTDATA = "gascompressoreffectdata";

    /**
     * 压缩机聚合数据
     */
    public static final String GASCOMPRESSORAGGRDATE = "gascompressoraggrdata";

    /**
     * 天然气压缩机参数
     */
    public static final String GASCOMPRESSORPARAM = "gascompressorparam";

    /**
     * 功图分析
     */
    public static final String DYNAMOMETERCARDANALYSIS = "dynamometercardanalysis";

    /**
     * 功图
     */
    public static final String DYNAMOMETERCARD = "dynamometercard";
    /**
     * 井筒温度
     */
    public static final String WELLBORETEMPERATURE = "wellboretemperature";

    /**
     * 加热炉参数
     */
    public static final String HEATINGFURNACEPARAM = "heatingfurnaceparam";

    /**
     * 净化厂
     */
    public static final String PURIFICATION_PLANT = "purificationplant";

    /**
     * 水井
     */
    public static final String WATER_WELL = "waterwell";

    /**
     * 油井
     */
    public static final String OIL_WELL = "oilwell";

    /**
     * 配电室
     */
    public static final String ROOM = "room";

    /**
     * 加热炉
     */
    public static final String HEATING_FURNACE = "heatingfurnace";

    /**
     * 典型功图
     */
    public static final String TYPICALDYNAMOMETERDATA = "typicaldynamometerdata";
    /**
     * 泵
     */
    public static final String PUMP = "pump";

    /**
     * 天然气压缩机
     */
    public static final String GAS_COMPRESSOR = "gascompressor";

    /**
     * 天然气压缩机实时能效表
     */
    public static final String GAS_COMPRESSOREFFECT_DATA = "gascompressoreffectdata";

    /**
     * 作业区或采油队能效指标
     */
    public static final String OIL_FIELD_KPI = "oilfieldkpi";

    /**
     * 注水站能效指标
     */
    public static final String WATER_INJECTION_STATION_KPI = "waterinjectionstationkpi";

    /**
     * 能源效率通用配置
     */
    public static final String ENERGY_EFFICIENCY_SET = "energyefficiencyset";

    /**
     * 油气田能效指标通用配置
     */
    public static final String OIL_EFFICIENCY_SET = "oilefficiencyset";

    /**
     * 油气田能效表
     */
    public static final String OIL_EFFICIENCY = "oilefficiency";

    /**
     * 加热炉
     */
    public static final String HEATINGFURNACE = "heatingfurnace";

    /**
     * 油田含水率编辑
     */
    public static final String OIL_FIELD_MOISTURE_CONTENT_EDIT = "oilfieldmoisturecontentedit";

    /**
     * 油田自然递减率编辑
     */
    public static final String OIL_FIELD_NATURAL_DECLINE_RATE_EDIT = "oilfieldnaturaldeclinerateedit";

    /**
     * 数据录入映射表
     */
    public static final String DATA_INPUT_MAPPING_TABLE = "datainputmappingtable";

    /**
     * 管道
     */
    public static final String PIPELINE = "pipeline";

    /**
     * 油井数据
     */
    public static final String OIL_WELL_DATA = "oilwelldata";

    /**
     * 气井数据
     */
    public static final String GAS_WELL_DATA = "gaswelldata";


    /**
     * 加热炉参数
     */
    public static final String HEATINGFURNACE_PARAM = "heatingfurnaceparam";

    /**
     * 物料成分表
     */
    public static final String CONSTITUENT = "constituent";

    /**
     * 油气田数据录入表
     */
    public static final String OIL_PARAM_ENTRY = "oilparamentry";

    /**
     * 指标和节点关联关系
     */
    public static final String EFFICIENCY_TO_OBJECT = "efficiencytoobject";

    /**
     * 机采设备
     */
    public static final String MECHANICALMININGMACHINE = "mechanicalminingmachine";


    /*********************************************批次能效************************************************/

    public static final String BATCH_BASE_CLASS = "batchbaseclass";

    public static final String BATCH_TEMPLATE = "batchtemplate";

    public static final String TEMPLATE_ATTRIBUTE = "templateattribute";
    public static final String BATCH_ATTRIBUTE_DATA = "batchattributedata";

    public static final String PROCESS = "process";

    public static final String BATCH_PRODUCT_DATA = "batchproductdata";
    public static final String PROCEDURE_THRESHOLD_SCHEME = "procedurethresholdscheme";
    public static final String PROCEDURE_THRESHOLD_CONFIG = "procedurethresholdconfig";
    public static final String PROCESS_ENERGY_CONSUMPTION = "processenergyconsumption";
    public static final String PRODUCT_CATEGORY = "productcategory";
    public static final String BATCH_EFFECT = "batcheffect";
    public static final String PROCEDURE = "procedure";

    public static final String FINALPRODUCT = "finalproduct";
    public static final String PROCEDURE_TYPE = "proceduretype";
    public static final String TRIPARTITE_ENERGY = "tripartiteenergy";
    public static final String TRIPARTITE_PRODUCT = "tripartiteproduct";

    public static final String CLEAN_SCHEME = "cleanscheme";
    public static final String CLEAN_CONDITIONS = "cleanconditions";
    /***********************************其他************************************/
    public static final String FILLING_TIME = "fillingtime";
    public static final String GASFURNACE_KPI = "gasfurnacekpi";
    public static final String WELL_KPI = "wellkpi";
    public static final String PUMP_KPI = "pumpkpi";
    public static final String GATHERING_UNIT_KPI = "gatheringunitkpi";
    public static final String WATER_UNIT_KPI = "waterunitkpi";
    public static final String GASFURNACE = "gasfurnace";
    public static final String WELL = "well";
    public static final String GATHERING_UNIT = "gatheringunit";
    public static final String WATER_UNIT = "waterunit";
    public static final String KPI = "kpi";
    public static final String KPI_STANDARD = "kpistandard";
    public static final String WELL_DATA_LOG = "welldatalog";
    public static final String HEATING_SYSTEM_VALUATION = "heatingsystemevaluation";
    public static final String HEATING_SYSTEM = "heatingsystem";
    public static final String MAIN_TENANCE_INFO = "maintenanceinfo";

    /**
     * 油气田录入参数表
     */
    public static final String OILPARAMENTRY = "oilparamentry";

    public static final String WECHAT_GROUP = "wechatgroup";
    public static final String WECHAT_MESSAGE_CONFIG = "wechatmessageconfig";
    public static final String WECHAT_MESSAGE = "wechatmessage";
    public static final String WELL_WASHING_CONDITION = "wellwashingcondition";
    public static final String WELL_WASHING_PLAN = "wellwashingplan";
    public static final String WELL_WASHING_CYCLE = "wellwashingcycle";
    public static final String WELL_WASHING_INFORM_CONFIG = "wellwashinginformconfig";
    public static final String WELL_LIQ_PRODUCTION_DATA = "wellliqproductiondata";

    /**
     * 泵日度数据恒达抄表记录
     */
    public static final String PDI_JD_HENGDA_POI = "pdi_jdhengda_poi";

    /**
     * 运行趋势诊断方案
     */
    public static final String RUNNING_TENDENCY_DIAGNOSIS_SCHEME = "runningtendencydiagnosisscheme";

    /**
     * 运行趋势阈值计算方案
     */
    public static final String RUNNING_TENDENCY_THRESHOLD_CONFIG = "runningtendencythresholdconfig";

    /**
     * 运行趋势诊断预警等级配置
     */
    public static final String RUNNING_TREND_ALARM_LEVEL = "runningtrendalarmlevel";

    /**
     * 运行趋势诊断关联节点
     */
    public static final String TENDENCY_DIAGNOSIS_SCHEME_TO_NODE = "tendencydiagnosisschemetonode";

    /**
     * 工况诊断方案表
     */
    public static final String WORKCONDITION_DIAGNOSIS_SCHEME = "workconditiondiagnosisscheme";

    /**
     * 工况诊断方案关联节点表
     */
    public static final String WORKCONDITION_DIAGNOSIS_SCHEME_TONODE = "workconditiondiagnosisschemetonode";

    /**
     * 脱水站能效指标
     */
    public static final String DEHY_DRATING_STATIONKPI = "dehydratingstationkpi";

    /**
     * 集气站能效指标
     */
    public static final String GAS_GATHERING_STATION_KPI = "gasgatheringstationkpi";

    /**
     * 气田平台能效指标
     */
    public static final String GAS_PLATFORM_KPI = "gasplatformkpi";

    /**
     * 气田作业区能效指标
     */
    public static final String GAS_OPERATION_AREA_KPI = "gasoperationareakpi";

    /**
     * 机采设备聚合数据
     */
    public static final String MECHANICALMININGMACHINEAGGRDATA = "mechanicalminingmachineaggrdata";

    /**
     * 净化厂能效指标
     */
    public static final String PURIFICATION_PLANT_KPI = "purificationplantkpi";

    /**
     * 采油队
     */
    public static final String OILPRODUCTIONCREW = "oilproductioncrew";

    /**
     * 采油厂
     */
    public static final String OILPRODUCTIONPLANT = "oilproductionplant";

    /**
     * 转油站
     */
    public static final String OILTRANSFERSTATION = "oiltransferstation";

    /**
     * 站库对接数据对应tag表
     */
    public static final String PDI_STATION_TAG_POI = "pdi_station_tag_poi";

    /**
     * A11数据对接记录
     */
    public static final String A11_POI = "a11_poi";

    public static final String MODEL_TRAINING_SCHEME = "modeltrainingscheme";

    public static final String MODEL_TRAINING_SCHEME_TO_NODE = "modeltrainingschemetonode";

    public static final String STRATEGY_ANALYSIS_EVENT = "strategyanalysisevent";

    public static final String DYNAMOMETERCARD_POINT_PARAM = "dynamometercardpointparam";

    /**
     * 设备检修
     */
    public static final String EQUIPMENT_MAINTENANCE_PLAN = "equipmentmaintenanceplan";
    public static final String EQUIPMENT_MAINTENANCE_PLAN_TO_NODE = "equipmentmaintenanceplantonode";
    public static final String EQUIPMENT_MAINTENANCE_RECORD = "equipmentmaintenancerecord";
    public static final String EQUIPMENT_MAINTENANCE_MESSAGE = "equipmentmaintenancemessage";
    /**
     * 下拉框选项
     */
    public static final String DROP_DOWN_OPTIONS = "dropdownoptions";

    /**
     * 公式
     */
    public static final String FORMULA = "formula";

    /**
     * 产量对接
     */
    public static final String PRODUCT_DOCKING = "productdocking";
    /***** 班组能耗部分 *******/
    /**
     * 排班方案
     */
    public static final String SCHEDULING_SCHEME = "schedulingscheme";
    /**
     * 班次方案表
     */
    public static final String CLASSES_SCHEME = "classesscheme";
    /**
     * 班次配置表
     */
    public static final String CLASSES_CONFIG = "classesconfig";
    /**
     * 班组信息表
     */
    public static final String TEAM_GROUP_INFO = "teamgroupinfo";
    /**
     * 班组节假日配置表
     */
    public static final String HOLIDAY_CONFIG = "holidayconfig";
    /**
     * 排班方案关联节点
     */
    public static final String SCHEDULING_SCHEME_TO_NODE = "schedulingschemetonode";
    /**
     * 排班班次
     */
    public static final String SCHEDULING_CLASSES = "schedulingclasses";
    /**
     * 班组能耗表
     */
    public static final String TEAM_GROUP_ENERGY = "teamgroupenergy";
}
