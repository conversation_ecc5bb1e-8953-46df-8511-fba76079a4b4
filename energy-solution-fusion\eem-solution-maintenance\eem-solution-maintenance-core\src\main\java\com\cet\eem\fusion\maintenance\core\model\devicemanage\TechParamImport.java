package com.cet.eem.fusion.maintenance.core.model.devicemanage;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-06-01
 */
@Data
public class TechParamImport {

    @ExcelProperty(value = "设备名称", index = 0)
    private String device;
    @ExcelProperty(value = "设备编号", index = 1)
    private String model;
    @ExcelProperty(value = "参数名称", index = 2)
    private String name;
    @ExcelProperty(value = "参数单位", index = 3)
    private String unit;
    @ExcelProperty(value = "参数值", index = 4)
    private String number;

}

