# Fix remaining compilation issues
Write-Host "Fixing remaining compilation issues..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$utf8NoBom = New-Object System.Text.UTF8Encoding $false

# Create missing constant classes
Write-Host "Creating missing constant classes..." -ForegroundColor Cyan

$fusionCommonPath = "$coreSourcePath\com\cet\eem\fusion\common"
New-Item -ItemType Directory -Path "$fusionCommonPath\constant" -Force | Out-Null

# Create Constants class
$constantsContent = @"
package com.cet.eem.fusion.common.constant;

/**
 * Common constants for fusion framework
 */
public class Constants {
    public static final String SUCCESS = "SUCCESS";
    public static final String ERROR = "ERROR";
    public static final String ACTIVE = "ACTIVE";
    public static final String INACTIVE = "INACTIVE";
    
    public static final String DEFAULT_TENANT_ID = "default";
    public static final String DEFAULT_PROJECT_ID = "default";
    
    public static final int DEFAULT_PAGE_SIZE = 20;
    public static final int MAX_PAGE_SIZE = 1000;
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\constant\Constants.java", $constantsContent, $utf8NoBom)

# Fix import issues in specific files
Write-Host "Fixing import issues in specific files..." -ForegroundColor Cyan

# Fix UserGroupVo and RoleVo files that have wrong directory structure
$wrongPathFiles = @(
    "$coreSourcePath\com\cet\eem\fusion\common\common\src\main\java\com\cet\eem\common\model\auth\user\UserGroupVo.java",
    "$coreSourcePath\com\cet\eem\fusion\common\common\src\main\java\com\cet\eem\common\model\auth\user\RoleVo.java"
)

foreach ($file in $wrongPathFiles) {
    if (Test-Path $file) {
        Write-Host "Removing incorrectly placed file: $file" -ForegroundColor Yellow
        Remove-Item $file -Force
        
        # Remove the entire wrong directory structure
        $wrongDir = "$coreSourcePath\com\cet\eem\fusion\common\common"
        if (Test-Path $wrongDir) {
            Remove-Item $wrongDir -Recurse -Force
        }
    }
}

# Create missing workflow classes
Write-Host "Creating missing workflow classes..." -ForegroundColor Cyan

New-Item -ItemType Directory -Path "$coreSourcePath\com\cet\electric\workflow\common\constants" -Force | Out-Null
New-Item -ItemType Directory -Path "$coreSourcePath\com\cet\electric\workflow\common\model\params" -Force | Out-Null

# WorkflowConstants
$workflowConstantsContent = @"
package com.cet.electric.workflow.common.constants;

/**
 * Workflow constants
 */
public class WorkflowConstants {
    public static final String PROCESS_STARTED = "PROCESS_STARTED";
    public static final String PROCESS_COMPLETED = "PROCESS_COMPLETED";
    public static final String TASK_CREATED = "TASK_CREATED";
    public static final String TASK_COMPLETED = "TASK_COMPLETED";
}
"@

[System.IO.File]::WriteAllText("$coreSourcePath\com\cet\electric\workflow\common\constants\WorkflowConstants.java", $workflowConstantsContent, $utf8NoBom)

# ManyUserTaskParams
$manyUserTaskParamsContent = @"
package com.cet.electric.workflow.common.model.params;

import java.util.List;
import java.util.Map;

/**
 * Many user task parameters
 */
public class ManyUserTaskParams {
    private List<String> userIds;
    private Map<String, Object> variables;
    private String taskName;
    private String description;
    
    // Getters and setters
    public List<String> getUserIds() { return userIds; }
    public void setUserIds(List<String> userIds) { this.userIds = userIds; }
    
    public Map<String, Object> getVariables() { return variables; }
    public void setVariables(Map<String, Object> variables) { this.variables = variables; }
    
    public String getTaskName() { return taskName; }
    public void setTaskName(String taskName) { this.taskName = taskName; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
}
"@

[System.IO.File]::WriteAllText("$coreSourcePath\com\cet\electric\workflow\common\model\params\ManyUserTaskParams.java", $manyUserTaskParamsContent, $utf8NoBom)

# Create missing task classes
Write-Host "Creating missing task classes..." -ForegroundColor Cyan

New-Item -ItemType Directory -Path "$coreSourcePath\com\cet\eem\bll\common\task" -Force | Out-Null

# TaskSchedule
$taskScheduleContent = @"
package com.cet.eem.bll.common.task;

/**
 * Task schedule interface
 */
public interface TaskSchedule {
    void execute();
    String getTaskName();
    String getCronExpression();
}
"@

[System.IO.File]::WriteAllText("$coreSourcePath\com\cet\eem\bll\common\task\TaskSchedule.java", $taskScheduleContent, $utf8NoBom)

# Create missing node classes
Write-Host "Creating missing node classes..." -ForegroundColor Cyan

New-Item -ItemType Directory -Path "$coreSourcePath\com\cet\eem\bll\common\model\node" -Force | Out-Null

# EemNodeFieldInfo
$eemNodeFieldInfoContent = @"
package com.cet.eem.bll.common.model.node;

/**
 * EEM node field information
 */
public class EemNodeFieldInfo {
    private String fieldName;
    private String fieldType;
    private String fieldValue;
    private String description;
    
    // Getters and setters
    public String getFieldName() { return fieldName; }
    public void setFieldName(String fieldName) { this.fieldName = fieldName; }
    
    public String getFieldType() { return fieldType; }
    public void setFieldType(String fieldType) { this.fieldType = fieldType; }
    
    public String getFieldValue() { return fieldValue; }
    public void setFieldValue(String fieldValue) { this.fieldValue = fieldValue; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
}
"@

[System.IO.File]::WriteAllText("$coreSourcePath\com\cet\eem\bll\common\model\node\EemNodeFieldInfo.java", $eemNodeFieldInfoContent, $utf8NoBom)

Write-Host "Created all missing classes successfully!" -ForegroundColor Green
Write-Host "- Constants class" -ForegroundColor Cyan
Write-Host "- Workflow classes" -ForegroundColor Cyan
Write-Host "- Task schedule classes" -ForegroundColor Cyan
Write-Host "- Node field info classes" -ForegroundColor Cyan
Write-Host "- Removed incorrectly placed files" -ForegroundColor Cyan
