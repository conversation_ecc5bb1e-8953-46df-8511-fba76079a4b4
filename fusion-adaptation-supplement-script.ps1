# Supplementary Fusion Adaptation Script for Missing Rules
Write-Host "Starting supplementary fusion adaptation for missing rules..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$adaptationLog = @()

# Define additional replacement mappings that were missed in the first script
$additionalImportReplacements = @{
    # Permission related changes
    "import com.cet.eem.auth.aspect.OperationPermission;" = "import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;"
    "import com.cet.eem.bll.common.def.OperationAuthDef;" = "import com.cet.eem.fusion.common.def.OperationAuthDef;"
    
    # Log related changes
    "import com.cet.eem.bll.common.log.annotation.OperationLog;" = "import com.cet.eem.fusion.config.sdk.service.log.OperationLog;"
    "import com.cet.eem.bll.common.log.constant.EEMOperationLogType;" = "import com.cet.eem.fusion.config.sdk.def.OperationLogType;"
    "import com.cet.eem.bll.common.log.constant.EnumOperationSubType;" = "import com.cet.eem.fusion.common.utils.EnumOperationSubType;"
    
    # Physical quantity related changes
    "import com.cet.piem.common.def.AggregationCycle;" = "import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;"
    "import com.cet.eem.bll.common.model.domain.object.physicalquantity.ProjectUnitClassify;" = "import com.cet.eem.fusion.common.def.base.ProjectUnitClassify;"
    "import com.cet.eem.bll.common.model.domain.object.physicalquantity.UserDefineUnit;" = "import com.cet.electric.baseconfig.common.entity.UserDefineUnit;"
    "import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;" = "import com.cet.electric.modelsdk.quantity.model.QuantityAggregationData;"
    "import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;" = "import com.cet.electric.baseconfig.common.entity.QuantityObject;"
    
    # DAO related changes
    "import com.cet.eem.quantity.dao.QuantityAggregationDataDao;" = "import com.cet.eem.fusion.energy.sdk.dao.QuantityAggregationDataDao;"
    "import com.cet.eem.quantity.dao.QuantityObjectDao;" = "import com.cet.eem.fusion.energy.sdk.dao.QuantityObjectDao;"
    "import com.cet.eem.bll.common.dao.node.NodeDao;" = "import com.cet.eem.fusion.config.sdk.service.EemNodeService;"
    "import com.cet.eem.bll.common.dao.poi.EemPoiRecordDao;" = "import com.cet.eem.fusion.energy.sdk.dao.EemPoiRecordDao;"
    
    # Service related changes
    "import com.cet.eem.auth.service.InnerAuthService;" = "import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;"
    "import com.cet.piem.service.feign.PiemConfigServerService;" = "import com.cet.eem.solution.common.feign.ConfigServerService;"
    
    # Model related changes
    "import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;" = "import com.cet.eem.fusion.energy.sdk.model.EemPoiRecord;"
    "import com.cet.eem.bll.common.model.peccore.PecCoreTreeSearchVo;" = "import com.cet.eem.fusion.config.sdk.entity.peccore.PecCoreTreeSearchVo;"
    "import com.cet.eem.common.model.peccore.Meter;" = "import com.cet.eem.fusion.common.model.peccore.Meter;"
    "import com.cet.eem.bll.common.model.domain.subject.generalrules.UnnaturalSetVo;" = "import com.cet.eem.fusion.config.sdk.entity.unnatural.UnnaturalSetVo;"
    "import com.cet.eem.bll.common.model.domain.subject.generalrules.FeeScheme;" = "import com.cet.eem.fusion.energy.sdk.model.generalrules.FeeScheme;"
    "import com.cet.eem.bll.common.model.domain.object.architecture.RoomVo;" = "import com.cet.eem.fusion.config.sdk.model.node.RoomVo;"
    "import com.cet.eem.bll.common.model.domain.object.architecture.BuildingVo;" = "import com.cet.eem.fusion.config.sdk.model.node.BuildingVo;"
    
    # Utility related changes
    "import com.cet.piem.common.utils.DoubleUtils;" = "import com.cet.eem.solution.common.utils.DoubleUtils;"
    "import com.cet.eem.node.TopologyUtils;" = "import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;"
    "import com.cet.eem.bll.common.util.ExcelValidationUtils;" = "import com.cet.eem.fusion.common.utils.excel.ExcelValidationUtils;"
    
    # Aggregation related changes
    "import com.cet.eem.common.constant.AggregationType;" = "import com.cet.eem.fusion.energy.sdk.def.AggregationType;"
}

# Additional code replacements
$additionalCodeReplacements = @{
    # Service method changes - more specific patterns
    "productDao.queryProducts(GlobalInfoUtils.getProjectId())" = "productDao.queryProducts(GlobalInfoUtils.getTenantId(), Product.class)"
    ".getProjectId()" = ".getTenantId()"
    
    # Constant replacements that might have been missed
    "TableColumnNameDef." = "TableColumnNameDef."
    "EEMOperationLogType." = "OperationLogType."
    "EnumOperationSubType." = "EnumOperationSubType."
}

function Update-JavaFileSupplementary {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $changed = $false
    
    # Apply additional import replacements
    foreach ($oldImport in $additionalImportReplacements.Keys) {
        $newImport = $additionalImportReplacements[$oldImport]
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), $newImport
            $changed = $true
            Write-Host "  - Updated import: $oldImport -> $newImport" -ForegroundColor Yellow
        }
    }
    
    # Apply additional code replacements
    foreach ($oldCode in $additionalCodeReplacements.Keys) {
        $newCode = $additionalCodeReplacements[$oldCode]
        if ($content -match [regex]::Escape($oldCode)) {
            $content = $content -replace [regex]::Escape($oldCode), $newCode
            $changed = $true
            Write-Host "  - Updated code: $oldCode -> $newCode" -ForegroundColor Yellow
        }
    }
    
    if ($changed) {
        Set-Content $filePath -Value $content -Encoding UTF8
        $script:adaptationLog += "Updated: $filePath"
        Write-Host "✓ Updated file: $filePath" -ForegroundColor Green
    }
}

# Process all Java files in core module
Write-Host "Processing Java files for supplementary changes..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.FullName)" -ForegroundColor Gray
    Update-JavaFileSupplementary -filePath $file.FullName
}

# Summary
Write-Host "`n=== SUPPLEMENTARY ADAPTATION SUMMARY ===" -ForegroundColor Cyan
Write-Host "Total files processed: $($javaFiles.Count)" -ForegroundColor White
Write-Host "Files updated: $($adaptationLog.Count)" -ForegroundColor Green

if ($adaptationLog.Count -gt 0) {
    Write-Host "`nUpdated files:" -ForegroundColor Yellow
    foreach ($log in $adaptationLog) {
        Write-Host "  $log" -ForegroundColor Gray
    }
}

Write-Host "`nSupplementary fusion adaptation completed!" -ForegroundColor Green
