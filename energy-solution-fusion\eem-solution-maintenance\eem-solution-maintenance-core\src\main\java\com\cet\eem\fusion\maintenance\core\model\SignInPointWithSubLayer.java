package com.cet.eem.fusion.maintenance.core.model;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInEquipment;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.SignInPoint;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import static com.cet.eem.fusion.config.sdk.def.OperationLogType.REGISTRATION_EQUIPMENT;

/**
 * @ClassName : SignInPointWithSubLayer
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 15:35
 */
@Getter
@Setter
public class SignInPointWithSubLayer extends SignInPoint {

    @JsonProperty(REGISTRATION_EQUIPMENT + "_model")
    private List<SignInEquipment> equipmentList;
}
