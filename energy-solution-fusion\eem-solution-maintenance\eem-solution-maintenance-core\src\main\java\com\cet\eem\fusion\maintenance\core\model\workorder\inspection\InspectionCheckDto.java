package com.cet.eem.fusion.maintenance.core.model.workorder.inspection;

import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.Attachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 4/13/2021
 */
@Getter
@Setter
@ApiModel(description = "工单审核")
public class InspectionCheckDto {
    @ApiModelProperty("审核意见")
    private String opinion;

    @ApiModelProperty("附件")
    private List<Attachment> attachments;

    @ApiModelProperty("审核结果")
    private Boolean checkResult;
}

