# Fusion Adaptation Task 03 - Additional Modifications
Write-Host "Running Fusion Adaptation Task 03 - Additional Modifications..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Task 03 import replacements
$task03ImportReplacements = @{
    # UnitService modifications
    "import com.cet.piem.service.UnitService;" = "import com.cet.eem.fusion.config.sdk.service.EnergyUnitService;"
    
    # EemCloudAuthService modifications
    "import com.cet.eem.service.EemCloudAuthService;" = "import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;"
    
    # Additional imports needed for new services
    "import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitSearchDTO;" = "import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitSearchDTO;"
    "import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;" = "import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;"
}

# Service field replacements
$serviceFieldReplacements = @{
    "private UnitService unitService;" = "private EnergyUnitService energyUnitService;"
    "private EemCloudAuthService cloudAuthService;" = "private UserRestApi userService;"
}

# Method call replacements
$methodCallReplacements = @{
    # UnitService method calls - need to be more specific
    "unitService\.getUnit\(" = "energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), "
    
    # EemCloudAuthService method calls
    "cloudAuthService\.queryUserBatch\(" = "userService.getUsers("
    
    # GlobalInfoUtils.getHttpResponse() removal
    "GlobalInfoUtils\.getHttpResponse\(\)" = "response"
}

# ResultWithTotal return type replacements
$returnTypeReplacements = @{
    "ResultWithTotal<" = "ApiResult<"
    "return ResultWithTotal\.ok\(" = "return Result.ok("
}

function Apply-Task03Adaptations {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $changed = $false
    
    # Apply import replacements
    foreach ($oldImport in $task03ImportReplacements.Keys) {
        $newImport = $task03ImportReplacements[$oldImport]
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), $newImport
            $changed = $true
            Write-Host "  - Updated import: $oldImport -> $newImport" -ForegroundColor Yellow
        }
    }
    
    # Apply service field replacements
    foreach ($oldField in $serviceFieldReplacements.Keys) {
        $newField = $serviceFieldReplacements[$oldField]
        if ($content -match [regex]::Escape($oldField)) {
            $content = $content -replace [regex]::Escape($oldField), $newField
            $changed = $true
            Write-Host "  - Updated service field: $oldField -> $newField" -ForegroundColor Yellow
        }
    }
    
    # Apply return type replacements
    foreach ($oldType in $returnTypeReplacements.Keys) {
        $newType = $returnTypeReplacements[$oldType]
        if ($content -match [regex]::Escape($oldType)) {
            $content = $content -replace [regex]::Escape($oldType), $newType
            $changed = $true
            Write-Host "  - Updated return type: $oldType -> $newType" -ForegroundColor Yellow
        }
    }
    
    # Apply method call replacements (more complex, need careful handling)
    foreach ($oldMethod in $methodCallReplacements.Keys) {
        $newMethod = $methodCallReplacements[$oldMethod]
        if ($content -match $oldMethod) {
            # For UnitService calls, we need more sophisticated replacement
            if ($oldMethod -eq "unitService\.getUnit\(") {
                # This needs manual review as the method signature changed significantly
                Write-Host "  - MANUAL REVIEW NEEDED: UnitService.getUnit() method call found" -ForegroundColor Red
            } else {
                $content = $content -replace $oldMethod, $newMethod
                $changed = $true
                Write-Host "  - Updated method call: $oldMethod -> $newMethod" -ForegroundColor Yellow
            }
        }
    }
    
    # Add HttpServletResponse import if GlobalInfoUtils.getHttpResponse() was found
    if ($originalContent -match "GlobalInfoUtils\.getHttpResponse\(\)") {
        if ($content -notmatch "import javax\.servlet\.http\.HttpServletResponse;") {
            # Find the import section and add the import
            $importSection = $content -match "import.*;"
            if ($importSection) {
                $content = $content -replace "(import.*?;)", "`$1`nimport javax.servlet.http.HttpServletResponse;"
                $changed = $true
                Write-Host "  - Added HttpServletResponse import" -ForegroundColor Yellow
            }
        }
    }
    
    if ($changed) {
        Set-Content $filePath -Value $content -Encoding UTF8
        Write-Host "✓ Applied Task 03 adaptations to: $filePath" -ForegroundColor Green
        return $true
    }
    
    return $false
}

# Process all Java files
Write-Host "Processing Java files for Task 03 adaptations..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$filesUpdated = 0

foreach ($file in $javaFiles) {
    if (Apply-Task03Adaptations -filePath $file.FullName) {
        $filesUpdated++
    }
}

Write-Host "`nTask 03 adaptations completed!" -ForegroundColor Green
Write-Host "Files updated: $filesUpdated" -ForegroundColor Cyan
Write-Host "Note: Some UnitService method calls may need manual review" -ForegroundColor Yellow
