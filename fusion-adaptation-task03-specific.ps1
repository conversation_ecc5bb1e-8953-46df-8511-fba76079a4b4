# Fusion Adaptation Task 03 - Specific Modifications
Write-Host "Running Fusion Adaptation Task 03 - Specific Modifications..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

function Apply-SpecificTask03Fixes {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $changed = $false
    
    # Fix ResultWithTotal imports and usage
    if ($content -match "import.*ResultWithTotal;") {
        $content = $content -replace "import.*ResultWithTotal;", ""
        $changed = $true
        Write-Host "  - Removed ResultWithTotal import" -ForegroundColor Yellow
    }
    
    # Fix ResultWithTotal return types
    if ($content -match "ResultWithTotal<") {
        $content = $content -replace "ResultWithTotal<", "ApiResult<"
        $changed = $true
        Write-Host "  - Updated ResultWithTotal return type to ApiResult" -ForegroundColor Yellow
    }
    
    # Fix ResultWithTotal.ok() calls
    if ($content -match "ResultWithTotal\.ok\(") {
        $content = $content -replace "ResultWithTotal\.ok\(", "Result.ok("
        $changed = $true
        Write-Host "  - Updated ResultWithTotal.ok() to Result.ok()" -ForegroundColor Yellow
    }
    
    # Fix EemCloudAuthService imports
    if ($content -match "import.*EemCloudAuthService;") {
        $content = $content -replace "import.*EemCloudAuthService;", "import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;"
        $changed = $true
        Write-Host "  - Updated EemCloudAuthService import to UserRestApi" -ForegroundColor Yellow
    }
    
    # Fix EemCloudAuthService field declarations
    if ($content -match "private EemCloudAuthService.*cloudAuthService;") {
        $content = $content -replace "private EemCloudAuthService.*cloudAuthService;", "private UserRestApi userService;"
        $changed = $true
        Write-Host "  - Updated EemCloudAuthService field to UserRestApi" -ForegroundColor Yellow
    }
    
    # Fix cloudAuthService.queryUserBatch calls
    if ($content -match "cloudAuthService\.queryUserBatch\(") {
        # Add necessary import if not present
        if ($content -notmatch "import.*ApiResultI18n;") {
            $content = $content -replace "(import.*?;)", "`$1`nimport com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;"
            $changed = $true
        }
        
        # Replace the method call pattern
        $content = $content -replace "Result<List<UserVo>> (\w+) = cloudAuthService\.queryUserBatch\(([^)]+)\);", "ApiResultI18n<List<UserVo>> `$1 = userService.getUsers(`$2);"
        $content = $content -replace "cloudAuthService\.queryUserBatch\(", "userService.getUsers("
        $changed = $true
        Write-Host "  - Updated cloudAuthService.queryUserBatch() to userService.getUsers()" -ForegroundColor Yellow
    }
    
    # Fix UnitService imports
    if ($content -match "import.*UnitService;") {
        $content = $content -replace "import.*UnitService;", "import com.cet.eem.fusion.config.sdk.service.EnergyUnitService;"
        $changed = $true
        Write-Host "  - Updated UnitService import to EnergyUnitService" -ForegroundColor Yellow
    }
    
    # Fix UnitService field declarations
    if ($content -match "private UnitService.*unitService;") {
        $content = $content -replace "private UnitService.*unitService;", "private EnergyUnitService energyUnitService;"
        $changed = $true
        Write-Host "  - Updated UnitService field to EnergyUnitService" -ForegroundColor Yellow
    }
    
    # Fix GlobalInfoUtils.getHttpResponse() calls
    if ($content -match "GlobalInfoUtils\.getHttpResponse\(\)") {
        # Add HttpServletResponse import if not present
        if ($content -notmatch "import.*HttpServletResponse;") {
            $content = $content -replace "(import.*?;)", "`$1`nimport javax.servlet.http.HttpServletResponse;"
            $changed = $true
        }
        
        # Replace the method call
        $content = $content -replace "GlobalInfoUtils\.getHttpResponse\(\)", "response"
        $changed = $true
        Write-Host "  - Updated GlobalInfoUtils.getHttpResponse() to response parameter" -ForegroundColor Yellow
        Write-Host "    NOTE: You may need to add HttpServletResponse response parameter to method signature" -ForegroundColor Red
    }
    
    # Add necessary imports for UserDefineUnitSearchDTO if UnitService is used
    if ($content -match "energyUnitService" -and $content -notmatch "import.*UserDefineUnitSearchDTO;") {
        $content = $content -replace "(import.*?;)", "`$1`nimport com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitSearchDTO;"
        $changed = $true
        Write-Host "  - Added UserDefineUnitSearchDTO import" -ForegroundColor Yellow
    }
    
    if ($changed) {
        Set-Content $filePath -Value $content -Encoding UTF8
        Write-Host "✓ Applied Task 03 specific fixes to: $filePath" -ForegroundColor Green
        return $true
    }
    
    return $false
}

# Process all Java files
Write-Host "Processing Java files for Task 03 specific fixes..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$filesUpdated = 0

foreach ($file in $javaFiles) {
    if (Apply-SpecificTask03Fixes -filePath $file.FullName) {
        $filesUpdated++
    }
}

Write-Host "`nTask 03 specific fixes completed!" -ForegroundColor Green
Write-Host "Files updated: $filesUpdated" -ForegroundColor Cyan

# Check for remaining issues
Write-Host "`nChecking for remaining Task 03 issues..." -ForegroundColor Cyan
$remainingIssues = 0

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    if ($content -match "ResultWithTotal") {
        Write-Host "  - ResultWithTotal still found in: $($file.Name)" -ForegroundColor Red
        $remainingIssues++
    }
    
    if ($content -match "EemCloudAuthService") {
        Write-Host "  - EemCloudAuthService still found in: $($file.Name)" -ForegroundColor Red
        $remainingIssues++
    }
    
    if ($content -match "UnitService.*unitService") {
        Write-Host "  - Old UnitService still found in: $($file.Name)" -ForegroundColor Red
        $remainingIssues++
    }
    
    if ($content -match "GlobalInfoUtils\.getHttpResponse") {
        Write-Host "  - GlobalInfoUtils.getHttpResponse still found in: $($file.Name)" -ForegroundColor Red
        $remainingIssues++
    }
}

if ($remainingIssues -eq 0) {
    Write-Host "✓ No remaining Task 03 issues found!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Found $remainingIssues remaining issues that may need manual review" -ForegroundColor Yellow
}
