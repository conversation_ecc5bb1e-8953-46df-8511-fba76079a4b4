﻿package com.cet.eem.fusion.maintenance.core.model.workorder.inspection;

import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.Attachment;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.ProcessFlowUnit;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderPlanDef;
import com.cet.eem.fusion.maintenance.core.def.WorkSheetStatusDef;
import com.cet.eem.fusion.maintenance.core.model.WorksheetAbnormalReasonDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.MaintenanceContent;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.node.config.UserTaskConfig;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.BooleanUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 4/13/2021
 */
@Getter
@Setter
public class InspectionWorkOrderDto extends EntityWithName {
    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述", name = "taskDescription", dataType = "string", example = "1")
    @JsonProperty(WorkOrderDef.TASK_DESCRIPTION)
    private String taskDescription;

    @ApiModelProperty("工单编号")
    private String code;

    @ApiModelProperty("巡检对象")
    private BaseVo node;

    @ApiModelProperty("工单来源id")
    @JsonProperty(WorkOrderDef.SOURCE_ID)
    private Integer sourceId;

    @ApiModelProperty("工单来源模型")
    @JsonProperty(WorkOrderDef.SOURCE_MODEL)
    private String sourceModel;

    @ApiModelProperty("工单来源")
    @JsonProperty(WorkOrderDef.SOURCE_TYPE)
    private Integer sourceType;

    @ApiModelProperty("工单来源名称")
    private String sourceTypeName;

    public String getCreateType() {
        if (this.creator == null) {
            return "系统创建";
        } else {
            return "手动创建";
        }
    }

    /**
     * 操作票ID
     */
    @JsonProperty(WorkOrderDef.OPERATION_ORDER_ID)
    private String operationOrderId;
    /**
     * 危险源分析
     */
    @JsonProperty(WorkOrderDef.HAZARD_ANALYSIS)
    private String hazardAnalysis;
    /**
     * 危险源附件
     */
    @JsonProperty(WorkOrderDef.HAZARD_ATTACHMENT)
    private String hazardAttachment;
    /**
     * 安全措施
     */
    @JsonProperty(WorkOrderDef.SAFETY_MEASURE)
    private String safetyMeasure;
    /**
     * 安全措施附件
     */
    @JsonProperty(WorkOrderDef.SAFETY_MEASURE_ATTACHMENT)
    private String safetyMeasureAttachment;
    /**
     * 参训人员
     */
    private String trainee;
    /**
     * 处理描述
     */
    @JsonProperty(WorkOrderDef.PROCESS_DESCRIPITON)
    private String processDescription;

    @ApiModelProperty("来源事件的时间")
    @JsonProperty(WorkOrderDef.SOURCE_TIME)
    private Long sourceTime;

    /**
     * 审核意见
     */
    private String opinion;

    @ApiModelProperty("创建人")
    private Long creator;

    @ApiModelProperty("创建时间")
    @JsonProperty(WorkOrderDef.CREATE_TIME)
    protected LocalDateTime createTime;

    @ApiModelProperty("结束时间")
    @JsonProperty(ColumnDef.END_TIME)
    private LocalDateTime endTime;

    @ApiModelProperty("创建人姓名")
    @JsonProperty(WorkOrderDef.CREATOR_NAME)
    private String creatorName;

    /**
     * 指派人员
     */
    private Long staff;

    /**
     * 指派人员
     */
    private String staffName;

    @ApiModelProperty("任务等级")
    @JsonProperty(WorkOrderDef.TASK_LEVEL)
    private Integer taskLevel;

    @ApiModelProperty("任务等级名称")
    private String taskLevelName;

    /**
     * 计划表ID
     */
    @JsonProperty(WorkOrderDef.PLANSHEET_ID)
    private Long planSheetId;

    @JsonProperty(WorkOrderPlanDef.PLAN_NAME)
    private String planName;

    /**
     * 项目ID
     */
    @NotNull
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;
    /**
     * 项目名称
     */
    @NotEmpty
    @JsonProperty(WorkOrderDef.PROJECT_NAME)
    private String projectName;

    /**
     * 工单类型
     */
    @JsonProperty(WorkOrderDef.TASK_TYPE)
    private Integer taskType;

    /**
     * 工单类型名称
     */
    private String taskTypeName;

    /**
     * 任务内容
     */
    @JsonProperty(WorkOrderDef.TASK_CONTENT)
    private String taskContent;

    @ApiModelProperty("实际开始时间")
    @JsonProperty(WorkOrderDef.EXECUTE_TIME)
    private LocalDateTime executeTime;

    @ApiModelProperty("实际完成时间")
    @JsonProperty(WorkOrderDef.FINISH_TIME)
    private LocalDateTime finishTime;

    @ApiModelProperty("实际耗时")
    @JsonProperty(WorkOrderDef.TIME_CONSUME)
    private Long timeConsume;

    @ApiModelProperty("计划开始时间")
    @JsonProperty(WorkOrderDef.EXECUTE_TIME_PLAN)
    private LocalDateTime executeTimePlan;

    @ApiModelProperty("计划结束时间")
    @JsonProperty(WorkOrderDef.FINISH_TIME_PLAN)
    private LocalDateTime finishTimePlan;

    @ApiModelProperty("计划耗时")
    @JsonProperty(WorkOrderDef.TIME_CONSUME_PLAN)
    protected Long timeConsumePlan;

    @ApiModelProperty("责任班组id")
    @JsonProperty(WorkOrderDef.TEAM_ID)
    private Long teamId;

    @ApiModelProperty("责任班组id")
    private String teamName;

    @ApiModelProperty("关联工单")
    @JsonProperty(WorkOrderDef.RELATED_CODE)
    private String relatedCode;

    @ApiModelProperty("关联工单id")
    @JsonProperty(WorkOrderDef.RELATED_CODE_ID)
    private Long relatedCodeId;

    /**
     * 流程定义ID
     */
    @JsonProperty(WorkOrderDef.PROCESS_DEFINITION_ID)
    private String processDefinitionId;
    /**
     * 流程实例ID
     */
    @JsonProperty(WorkOrderDef.PROCESS_INSTANCE_ID)
    private String processInstanceId;
    /**
     * 流程定义KEY
     */
    @JsonProperty(WorkOrderDef.PROCESS_DEFINITION_KEY)
    private String processDefinitionKey;

    @ApiModelProperty("工单状态")
    @JsonProperty(WorkOrderDef.WORKSHEET_STATUS)
    private Integer workSheetStatus;

    @ApiModelProperty("工单状态名称")
    private String workSheetStatusName;

    @ApiModelProperty("关联的设备")
    @JsonProperty(WorkOrderDef.DEVICE_PLAN_RELATIONSHIP_MODEL)
    protected List<DevicePlanRelationship> devicePlanRelationshipList;

    @ApiModelProperty("处理附件")
    @JsonProperty(WorkOrderDef.HANDLE_ATTACHMENTS)
    private String handleAttachments;

    @ApiModelProperty("处理附件列表")
    private List<Attachment> handleAttachmentList;

    public List<Attachment> getHandleAttachmentList() {
        return JsonTransferUtils.transferJsonString(handleAttachments, Attachment.class);
    }

    @ApiModelProperty("工单附件")
    private String attachment;

    @ApiModelProperty("工单附件列表")
    private List<Attachment> attachmentList;

    public List<Attachment> getAttachmentList() {
        return JsonTransferUtils.transferJsonString(attachment, Attachment.class);
    }

    /**
     * 巡检
     */
    @JsonProperty(WorkOrderDef.MAINTENANCE_CONTENT)
    protected String maintenanceContent;

    /**
     * 执行人员
     */
    @JsonProperty(WorkOrderDef.GIVEN_STAFF_NAME)
    private String givenStaffName;

    /**
     * 超时时间
     */
    @JsonProperty(WorkOrderDef.CANCEL_TIME_OUT)
    private Long cancelTimeOut;

    /**
     * 执行人员
     */
    @JsonProperty(WorkOrderDef.ASSIGN_TIME)
    private Long assignTime;

    @JsonProperty(value = WorkOrderDef.TASK_ID)
    private String taskId;

    @JsonProperty(WorkOrderDef.REJECT_ENABLED)
    private Boolean rejectEnabled;

    @ApiModelProperty("故障和处理描述")
    @JsonProperty(WorkOrderDef.FAULT_DESCRIPTION)
    private String faultDescription;

    @ApiModelProperty("巡检方案id")
    @JsonProperty(WorkOrderDef.INSPECTION_SCHEME_ID)
    private Long inspectionSchemeId;

    @ApiModelProperty("巡检方案名称")
    private String inspectionSchemeName;

    @ApiModelProperty("用户组所属的租户id")
    @JsonProperty(ColumnDef.TENANT_ID)
    private Long tenantId;

    @ApiModelProperty("业务状态")
    @JsonProperty(WorkOrderDef.BUSSINESS_STATUS)
    private Integer businessStatus;

    @JsonProperty(WorkOrderDef.WORKSHEET_ABNORMAL_REASON_MODEL)
    private List<WorksheetAbnormalReasonDto> abnormalReasonList;

    public String getInspectResult() {
        if (this.businessStatus == null) {
            if (BooleanUtils.isTrue(this.overtime)) {
                return "超时";
            }

            if (WorkSheetStatusDef.TO_BE_SENT.equals(this.workSheetStatus)) {
                return null;
            } else if (WorkSheetStatusDef.OVERTIME.equals(this.workSheetStatus)) {
                return "超时";
            } else {
                return "正常";
            }
        }

        if (this.businessStatus.equals(WorkSheetStatusDef.ABNORMAL)) {
            return "异常";
        } else {
            return "正常";
        }
    }

    @ApiModelProperty("签到点分组id")
    @JsonProperty(WorkOrderDef.SIGN_GROUP_ID)
    private Long signGroupId;

    @ApiModelProperty("处理描述")
    @JsonProperty(WorkOrderDef.HANDLE_DESCRIPTION)
    private String handleDescription;

    @ApiModelProperty("巡检参数详情")
    private List<InspectionSchemeDetail> inspectionSchemeDetails;

    public List<InspectionSchemeDetail> getInspectionSchemeDetails() {
        MaintenanceContent maintenanceContentObj = JsonTransferUtils.parseObject(this.maintenanceContent, MaintenanceContent.class);
        return maintenanceContentObj == null ? null : maintenanceContentObj.getInspectParams();
    }

    @ApiModelProperty("巡检点日志")
    private List<ProcessFlowUnit> processFlowUnits;

    @ApiModelProperty("维修方式")
    @JsonProperty(WorkOrderDef.REPAIR_TYPE)
    private Integer repairType;

    @ApiModelProperty("维修方式")
    private String repairTypeName;

    @ApiModelProperty("超时原因")
    @JsonProperty(WorkOrderDef.OVER_TIME_REASON)
    private String overTimeReason;

    @ApiModelProperty("是否超时")
    @JsonProperty(WorkOrderDef.OVER_TIME)
    private Boolean overtime;

    @ApiModelProperty("设备归类id")
    @JsonProperty(ColumnDef.DEVICE_CLASSIFICATION_ID)
    private Long deviceClassificationId;

    @ApiModelProperty("事件归类id")
    @JsonProperty(ColumnDef.EVENT_CLASSIFICATION_ID)
    private Long eventClassificationId;

    @ApiModelProperty("故障场景id")
    @JsonProperty(ColumnDef.FAULT_SCENARIOS_ID)
    private Long faultScenariosId;

    @ApiModelProperty("故障预案")
    @JsonProperty(WorkOrderDef.EVENT_PLAN_ID)
    private Long eventPlanId;

    @ApiModelProperty("签到点id")
    @JsonProperty(WorkOrderDef.SIGN_POINT_ID)
    private Long signPointId;

    @ApiModelProperty("填报方式")
    @JsonProperty(WorkOrderDef.FILL_FORM_TYPE)
    private Integer fillFormType;

    @ApiModelProperty("运值确认时间")
    @JsonProperty(WorkOrderDef.INSPECT_CONFIRM_TIME)
    private Long inspectConfirmTime;

    @ApiModelProperty("维修确认时间")
    @JsonProperty(WorkOrderDef.REPAIR_CONFIRM_TIME)
    private Long repairConfirmTime;

    @ApiModelProperty("签到点名称")
    private String signPointName;

    @ApiModelProperty("巡检班组id")
    @JsonProperty(WorkOrderDef.INSPECT_TEAM_ID)
    private Long inspectTeamId;

    @ApiModelProperty("工单当前所处的任务节点信息")
    private UserTaskConfig userTaskConfig;
    @ApiModelProperty("维修类型")
    @JsonProperty(WorkOrderDef.REPAIR_CATEGORY)
    private Integer repairCategory;
    @ApiModelProperty("工单类别-维修专业类别")
    @JsonProperty(WorkOrderDef.PROFESSIONAL_CATEGORY)
    private Integer professionalCategory;

    public InspectionWorkOrderDto() {
    }

    public InspectionWorkOrderDto(Integer workSheetStatus) {
        this.workSheetStatus = workSheetStatus;
    }
}


