# Create missing authentication and user classes
Write-Host "Creating missing authentication and user classes..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$utf8NoBom = New-Object System.Text.UTF8Encoding $false

# Create directory structure for auth classes
$fusionCommonPath = "$coreSourcePath\com\cet\eem\fusion\common"
New-Item -ItemType Directory -Path "$fusionCommonPath\model\auth\user" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\constant" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\model\ext\subject\powermaintenance" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\model\base" -Force | Out-Null
New-Item -ItemType Directory -Path "$fusionCommonPath\def\common" -Force | Out-Null

Write-Host "Creating user authentication classes..." -ForegroundColor Cyan

# UserGroupVo
$userGroupVoContent = @"
package com.cet.eem.fusion.common.model.auth.user;

import java.util.List;

/**
 * User group value object
 */
public class UserGroupVo {
    private String id;
    private String groupName;
    private String groupCode;
    private String description;
    private String parentId;
    private Integer level;
    private List<UserVo> users;
    private List<UserGroupVo> subGroups;
    
    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getGroupName() { return groupName; }
    public void setGroupName(String groupName) { this.groupName = groupName; }
    
    public String getGroupCode() { return groupCode; }
    public void setGroupCode(String groupCode) { this.groupCode = groupCode; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getParentId() { return parentId; }
    public void setParentId(String parentId) { this.parentId = parentId; }
    
    public Integer getLevel() { return level; }
    public void setLevel(Integer level) { this.level = level; }
    
    public List<UserVo> getUsers() { return users; }
    public void setUsers(List<UserVo> users) { this.users = users; }
    
    public List<UserGroupVo> getSubGroups() { return subGroups; }
    public void setSubGroups(List<UserGroupVo> subGroups) { this.subGroups = subGroups; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\auth\user\UserGroupVo.java", $userGroupVoContent, $utf8NoBom)

# RoleVo
$roleVoContent = @"
package com.cet.eem.fusion.common.model.auth.user;

import java.util.List;

/**
 * Role value object
 */
public class RoleVo {
    private String id;
    private String roleName;
    private String roleCode;
    private String description;
    private String status;
    private List<String> permissions;
    
    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getRoleName() { return roleName; }
    public void setRoleName(String roleName) { this.roleName = roleName; }
    
    public String getRoleCode() { return roleCode; }
    public void setRoleCode(String roleCode) { this.roleCode = roleCode; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public List<String> getPermissions() { return permissions; }
    public void setPermissions(List<String> permissions) { this.permissions = permissions; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\auth\user\RoleVo.java", $roleVoContent, $utf8NoBom)

# UserVo
$userVoContent = @"
package com.cet.eem.fusion.common.model.auth.user;

import java.util.List;

/**
 * User value object
 */
public class UserVo {
    private String id;
    private String username;
    private String realName;
    private String email;
    private String phone;
    private String status;
    private String userGroupId;
    private List<RoleVo> roles;
    
    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getRealName() { return realName; }
    public void setRealName(String realName) { this.realName = realName; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getUserGroupId() { return userGroupId; }
    public void setUserGroupId(String userGroupId) { this.userGroupId = userGroupId; }
    
    public List<RoleVo> getRoles() { return roles; }
    public void setRoles(List<RoleVo> roles) { this.roles = roles; }
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\model\auth\user\UserVo.java", $userVoContent, $utf8NoBom)

Write-Host "Creating constant classes..." -ForegroundColor Cyan

# ErrorCode
$errorCodeContent = @"
package com.cet.eem.fusion.common.constant;

/**
 * Error code constants
 */
public class ErrorCode {
    public static final String SUCCESS = "0000";
    public static final String SYSTEM_ERROR = "9999";
    public static final String PARAMETER_ERROR = "1001";
    public static final String DATA_NOT_FOUND = "1002";
    public static final String PERMISSION_DENIED = "1003";
    public static final String USER_NOT_FOUND = "2001";
    public static final String USER_GROUP_NOT_FOUND = "2002";
    public static final String ROLE_NOT_FOUND = "2003";
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\constant\ErrorCode.java", $errorCodeContent, $utf8NoBom)

# ExcelType
$excelTypeContent = @"
package com.cet.eem.fusion.common.constant;

/**
 * Excel type constants
 */
public class ExcelType {
    public static final String XLS = "xls";
    public static final String XLSX = "xlsx";
    public static final String CSV = "csv";
}
"@

[System.IO.File]::WriteAllText("$fusionCommonPath\constant\ExcelType.java", $excelTypeContent, $utf8NoBom)

Write-Host "Created authentication and constant classes successfully!" -ForegroundColor Green
Write-Host "- UserGroupVo, RoleVo, UserVo" -ForegroundColor Cyan
Write-Host "- ErrorCode, ExcelType constants" -ForegroundColor Cyan
