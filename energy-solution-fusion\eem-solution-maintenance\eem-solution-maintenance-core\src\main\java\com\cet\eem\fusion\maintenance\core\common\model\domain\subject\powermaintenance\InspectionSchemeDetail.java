package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 4/13/2021
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.INSPECTION_SCHEME_DETAIL)
public class InspectionSchemeDetail extends EntityWithName {

    private Boolean status;
    private Boolean deleted;
    private Double max;
    private Double min;
    private Double value;
    private Integer sort;
    @JsonProperty("inspectionparameterid")
    private Long inspectionParameterId;

    @JsonProperty("createtime")
    private Long createTime;

    private String paraName;
    /**
     * 1 状态量
     * 2 模拟量
     * 3 文本量
     */
    private Integer type;
    /**
     * 文本量存储值的字段
     */
    @JsonProperty("textvalue")
    private String textValue;

    public InspectionSchemeDetail() {
        this.modelLabel = ModelLabelDef.INSPECTION_SCHEME_DETAIL;
    }

    /**
     * 将对象合并到列表中，如果已存在则不合并
     * @param inspectionSchemeDetailList
     * @return
     */
    public void combine(List<InspectionSchemeDetail> inspectionSchemeDetailList) {
        for (InspectionSchemeDetail schemeDetail : inspectionSchemeDetailList) {
            if (this.inspectionParameterId.equals(schemeDetail.getInspectionParameterId()) &&
                    Objects.equals(this.type,schemeDetail.getType())) {
                schemeDetail.setMax(this.max);
                schemeDetail.setMin(this.min);
                schemeDetail.setId(null);
                return;
            }
        }
        inspectionSchemeDetailList.add(this);
    }
}
