package com.cet.eem.fusion.maintenance.core.service.inspection;

import com.cet.eem.fusion.maintenance.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.fusion.maintenance.core.model.scheme.*;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.electric.commons.ApiResult;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

public interface InspectorSchemeService {

    ApiResult<List<InspectionSchemeVo>> queryInspectionScheme(QueryInspectionSchemeRequest queryInspectionSchemeRequest);

    InspectionScheme addInspectionScheme(AddInspectionSchemeRequest addInspectionSchemeRequest);

    QueryInspectionDetailResult queryInspectionSchemeAndDetail(Long inspectionSchemeId);

    void editInspectionScheme(EditInspectionSchemeRequest editInspectionSchemeRequest);

    void deleteInspectionScheme(Collection<Long> inspectionSchemeIds);

    /**
     * 下载导入参数和方案的模板
     *
     * @param response
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 导入参数方案
     *
     * @param file
     */
    void importItem(MultipartFile file, Long projectId) throws IOException, InvalidFormatException, ValidationException;
}


