package com.cet.eem.fusion.maintenance.core.service.maintenance;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.MaintenanceTypeDefine;
import com.cet.eem.fusion.maintenance.core.model.maintance.item.AddMaintenanceTypeVo;
import com.cet.eem.fusion.maintenance.core.model.maintance.item.UpdateMaintenanceTypeVo;

import java.util.List;

/**
 * 维保类型业务逻辑类
 *
 * <AUTHOR>
 * @date 2021/7/13
 */
public interface MaintenanceTypeService {
    /**
     * 查询维保类型
     *
     * @param key
     * @return
     */
    List<MaintenanceTypeDefine> queryMaintenanceType(String key);

    /**
     * 新增维保类型
     *
     * @param maintenanceTypeVos
     * @return
     */
    List<MaintenanceTypeDefine> createMaintenanceType(List<AddMaintenanceTypeVo> maintenanceTypeVos);

    /**
     * 更新维保类型
     *
     * @param maintenanceTypeVos
     * @return
     */
    List<MaintenanceTypeDefine> updateMaintenanceType(List<UpdateMaintenanceTypeVo> maintenanceTypeVos);

    /**
     * 删除维保类型
     *
     * @param ids
     */
    void deleteMaintenanceType(List<Long> ids);
}

