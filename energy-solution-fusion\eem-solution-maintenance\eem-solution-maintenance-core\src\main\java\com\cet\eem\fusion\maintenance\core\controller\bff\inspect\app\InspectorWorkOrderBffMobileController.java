package com.cet.eem.fusion.maintenance.core.controller.bff.inspect.app;

import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;
import com.cet.eem.fusion.common.def.OperationAuthDef;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.model.wo.MobileWorkOrderQueryVO;
import com.cet.eem.fusion.maintenance.core.model.workorder.WoStatusCountDTO;
import com.cet.eem.fusion.maintenance.core.model.workorder.app.InspectionWorkOrderDetailDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.app.SignPointWithWorkOrder;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.service.inspection.app.InspectionWorkOrderMobileService;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/5/12
 */
public class InspectorWorkOrderBffMobileController {
    @Autowired
    private InspectionWorkOrderMobileService inspectionWorkOrderMobileService;

    @ApiOperation(value = "查询巡检工单")
    @PostMapping("/workOrder")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<InspectionWorkOrderDetailDto> queryWorkOrderList(
            @RequestBody(required = false) @ApiParam(name = "page", value = "分页信息") Page page) {
        InspectionWorkOrderDetailDto result = inspectionWorkOrderMobileService.queryWorkOrderDetail(page);
        return Result.ok(result);
    }

    @ApiOperation(value = "查询巡检工单数量统计")
    @GetMapping("/workOrder/status/count")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<List<WoStatusCountDTO>> queryWorkOrderList() {
        List<WoStatusCountDTO> result = inspectionWorkOrderMobileService.queryWoStatusCount(GlobalInfoUtils.getUserId());
        return Result.ok(result);
    }

    @ApiOperation(value = "查询巡检工单数量统计")
    @GetMapping("/workOrder/signPoint")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<List<SignPointWithWorkOrder>> querySignPoint() {
        List<SignPointWithWorkOrder> signPointWithWorkOrders = inspectionWorkOrderMobileService.querySignPoint(GlobalInfoUtils.getUserId());
        return Result.ok(signPointWithWorkOrders);
    }

    @ApiOperation(value = "分页查询工单")
    @PostMapping("/workOrder/page")
    @OperationPermission(authNames = {OperationAuthDef.INSPECTOR_WORK_ORDER_BROWSER})
    public ApiResult<List<InspectionWorkOrderDto>> queryWorkOrderList(
            @Valid @RequestBody MobileWorkOrderQueryVO query) {
        if (Objects.isNull(query.getUserId())) {
            query.setUserId(GlobalInfoUtils.getUserId());
        }
        List<InspectionWorkOrderDto> result = inspectionWorkOrderMobileService.queryWorkOrderList(query);
        return Result.ok(result);
    }
}



