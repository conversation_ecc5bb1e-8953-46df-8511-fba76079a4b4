package com.cet.eem.fusion.maintenance.core.model;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : InspectionSchemeWithSubLayer
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 17:28
 */
@Getter
@Setter
public class InspectionSchemeWithSubLayer extends InspectionScheme {

    @JsonProperty("inspectionschemedetail_model")
    private List<InspectionSchemeDetail> schemeDetails;
}
