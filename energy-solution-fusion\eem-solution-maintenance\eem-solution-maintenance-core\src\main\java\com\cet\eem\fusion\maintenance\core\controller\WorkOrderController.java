package com.cet.eem.fusion.maintenance.core.controller;

import com.cet.eem.fusion.maintenance.core.controller.bff.WorkOrderBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/5/31
 */
@Api(value = "/eem/v1/workorder", tags = "工单：所有工单")
@RequestMapping(value = "/eem/solution/maintenance/eem/v1/workorder")
@RestController
@Validated
public class WorkOrderController extends WorkOrderBffController {

}


