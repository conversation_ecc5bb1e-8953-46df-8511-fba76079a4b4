package com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance;

import com.cet.eem.fusion.common.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.common.definition.ColumnDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

/**
 * @Author: jiangzixuan
 * @Description:
 * @Data: Created in 2021-05-12
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.SPARE_PARTS_DEVICE)
public class SparePartsDevice extends EntityWithName {
    /**
     * 设备型号
     */
    private String model;
    /**
     * 设备类型
     */
    @JsonProperty("objectlabel")
    private String objectLabel;

    /**
     * 所属公司
     */
    private String company;

    /**
     * 关键技术参数
     */
    @JsonProperty(ColumnDef.CRITICAL_PARAMS)
    private String criticalParams;

    /**
     * 二级库备品备件id
     */
    @JsonProperty(ColumnDef.SECONDARY_SPARE_PARTS_ID)
    private Long secondarySparePartsId;

    public SparePartsDevice() {
        this.modelLabel = ModelLabelDef.SPARE_PARTS_DEVICE;
    }

    public SparePartsDevice(String model, String objectLabel, String name) {
        this.model = model;
        this.objectLabel = objectLabel;
        this.name = name;
        this.modelLabel = ModelLabelDef.SPARE_PARTS_DEVICE;
    }
}