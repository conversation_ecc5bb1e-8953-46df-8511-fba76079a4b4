package com.cet.eem.fusion.maintenance.core.service.bff.repair;

import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;

import com.cet.eem.fusion.maintenance.core.model.maintance.WorkOrderExportVo;
import com.cet.eem.fusion.maintenance.core.model.maintance.WorkOrderVo;
import com.cet.eem.bll.common.exception.CommonException;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.ProcessFlowUnit;
import com.cet.eem.fusion.maintenance.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderDao;
import com.cet.eem.fusion.maintenance.core.model.workorder.MaintenanceContent;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionCountSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.RepairByNodeSearchVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.RepairSearchVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.RepairSourceIndexVo;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorService;
import com.cet.eem.fusion.maintenance.core.service.repair.RepairWorkOrderService;
import com.cet.eem.fusion.maintenance.core.utils.InspectorUserCheckUtils;
import com.cet.eem.fusion.maintenance.core.utils.WorkSheetStatusUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.common.ExcelUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.maintenance.common.definition.CommonDef;
import com.cet.eem.fusion.maintenance.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.event.model.expert.ClassifiedBaseVo;
import com.cet.eem.event.model.expert.DeviceClassification;
import com.cet.eem.fusion.maintenance.core.bll.common.model.domain.subject.huaxingguangdian.EventPlan;
import com.cet.eem.bll.common.model.domain.subject.huaxingguangdian.FaultScenarios;
import com.cet.eem.event.service.ExpertService;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.maintenance.core.common.workflow.common.model.node.config.UserTaskConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import com.cet.eem.fusion.common.def.common.ContentTypeDef;

/**
 * <AUTHOR>
 * @date 2021/6/2
 */
@Service
public class RepairWorkOrderBffServiceImpl implements RepairWorkOrderBffService {
    @Autowired
    NodeDao nodeDao;

    @Autowired
    WorkOrderDao workOrderDao;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Resource
    UserRestApi userRestApi;

    @Autowired
    InspectorService inspectorService;

    @Autowired
    RepairWorkOrderService repairWorkOrderService;

    @Autowired
    ExpertService expertService;

    @Autowired
    WorkSheetStatusUtils workSheetStatusUtils;

    @Autowired
    InspectorUserCheckUtils inspectorUserCheckUtils;

    @Override
    public ApiResult<List<WorkOrderVo>> queryWorkOrderList(RepairSearchVo dto) {
        // 查询用户信息和班组信息
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        dto.setInspectUser(inspectorService.isInspectUser(user));
        // 判断当前用户是否为巡检用户，对于巡检用户只能看自己班组的工单
        dto.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(dto.getTeamId(), user));

        ApiResult<List<WorkOrderVo>> result = repairWorkOrderService.queryWorkOrderList(dto, WorkOrderVo.class);
        List<WorkOrderVo> data = result.getData();
        assemblyPartWorkOrderList(data, dto.getTenantId(), user);
        return Result.ok(data, result.getTotal());
    }

    @Override
    public ApiResult<List<WorkOrderVo>> queryWorkOrderByNode(RepairByNodeSearchVo dto) {
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        ApiResult<List<WorkOrderVo>> result = repairWorkOrderService.queryWorkOrderByNode(dto, WorkOrderVo.class);
        List<WorkOrderVo> data = result.getData();
        assemblyPartWorkOrderList(data, CommonDef.ZERO_LONG, user);
        return Result.ok(data, result.getTotal());
    }

    @Override
    public List<WorkOrderCountDto> queryWorkOrderCount(InspectionCountSearchDto dto) {
        // 查询用户信息和班组信息
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        dto.setInspectUser(inspectorService.isInspectUser(user));

        // 判断当前用户是否为巡检用户，对于巡检用户只能看自己班组的工单
        dto.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(dto.getTeamId(), user));
        return repairWorkOrderService.queryWorkOrderCount(dto);
    }

    @Override
    public void assemblyPartWorkOrderList(List<WorkOrderVo> workOrderList, Long tenantId, UserVo user) {
        if (CollectionUtils.isEmpty(workOrderList)) {
            return;
        }

        Set<BaseVo> tmpNodes = new HashSet<>();
        Set<Long> executeUserIds = new HashSet<>();
        Set<Long> deviceIds = new HashSet<>();
        Set<Long> faultIds = new HashSet<>();
        Set<Long> eventIds = new HashSet<>();
        Set<Long> eventPlanIds = new HashSet<>();
        resolveData(workOrderList, tmpNodes, executeUserIds, deviceIds, faultIds, eventIds, eventPlanIds);
        // 查询节点
        Map<BaseVo, String> nodeMap = nodeDao.queryNodeNameMap(tmpNodes);
        // 查询用户信息
        Map<Long, String> userMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        Map<Long, String> groupMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        queryUserInfo(tenantId, executeUserIds, userMap, groupMap);
        Map<Integer, String> workStatusMap = workSheetStatusUtils.getWorkSheetStatusMapByTaskType(WorkSheetTaskType.REPAIR);
        Map<Integer, String> taskLevelMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORKSHEET_TASK_LEVEL);
        Map<Integer, String> taskTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORK_SHEET_TASK_TYPE);
        Map<Integer, String> repairTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.REPAIR_TYPE);
        Map<Integer, String> sourceTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORK_ORDER_SOURCE_TYPE);
        Map<Integer, String> fillFormTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.FILL_FORM_TYPE);
        Set<Long> workOrderIds = workOrderList.stream().map(EntityWithName::getId).collect(Collectors.toSet());
        Map<String, UserTaskConfig> taskConfigMap = workOrderDao.queryTaskConfigList(workOrderIds, null);

        for (WorkOrderVo dto : workOrderList) {
            dto.setTeamName(groupMap.get(dto.getTeamId()));
            dto.setInspectTeamName(groupMap.get(dto.getInspectTeamId()));
            dto.setStaffName(userMap.get(dto.getStaff()));
            dto.setCreatorName(userMap.get(dto.getCreator()));
            dto.setWorkSheetStatusName(workStatusMap.get(dto.getWorkSheetStatus()));
            dto.setTaskTypeName(taskTypeMap.get(dto.getTaskType()));
            dto.setTaskLevelName(taskLevelMap.get(dto.getTaskLevel()));
            dto.setRepairTypeName(repairTypeMap.get(dto.getRepairType()));
            handleUserTaskConfig(user, taskConfigMap, dto);
            dto.setSourceTypeName(sourceTypeMap.get(dto.getSourceType()));
            dto.setFillFormTypeName(fillFormTypeMap.get(dto.getFillFormType()));

            // 匹配巡检对象名称
            List<DevicePlanRelationship> devicePlanRelationshipList = dto.getDevicePlanRelationshipList();
            if (CollectionUtils.isEmpty(devicePlanRelationshipList)) {
                continue;
            }
            for (DevicePlanRelationship devicePlanRelationship : devicePlanRelationshipList) {
                devicePlanRelationship.setDeviceName(nodeMap.get(new BaseVo(devicePlanRelationship.getDeviceId(), devicePlanRelationship.getDeviceLabel())));
            }
            if (Objects.nonNull(dto.getSourceIndex())) {
                RepairSourceIndexVo repairSourceIndexVo = JsonTransferUtils.parseString(dto.getSourceIndex(), RepairSourceIndexVo.class);
                dto.setRepairSourceIndexVo(repairSourceIndexVo);
            }
        }

        workOrderList.sort((v1, v2) -> CommonUtils.sort(v1.getCreateTime(), v2.getCreateTime(), false));
    }

    private void handleUserTaskConfig(UserVo user, Map<String, UserTaskConfig> taskConfigMap, WorkOrderVo dto) {
        UserTaskConfig userTaskConfig = taskConfigMap.get(dto.getCode());
        inspectorUserCheckUtils.checkRepairWorkOrderAuth(user, dto, userTaskConfig);
        dto.setUserTaskConfig(userTaskConfig);
    }

    @Override
    public void assemblyWholeWorkOrderList(List<WorkOrderVo> workOrderList, Long tenantId, UserVo user) {
        if (CollectionUtils.isEmpty(workOrderList)) {
            return;
        }

        Long projectId = GlobalInfoUtils.getTenantId();
        Set<BaseVo> tmpNodes = new HashSet<>();
        Set<Long> executeUserIds = new HashSet<>();
        Set<Long> deviceIds = new HashSet<>();
        Set<Long> faultIds = new HashSet<>();
        Set<Long> eventIds = new HashSet<>();
        Set<Long> eventPlanIds = new HashSet<>();
        resolveData(workOrderList, tmpNodes, executeUserIds, deviceIds, faultIds, eventIds, eventPlanIds);
        // 查询节点
        Map<BaseVo, String> nodeMap = nodeDao.queryNodeNameMap(tmpNodes);
        // 查询用户信息
        Map<Long, String> userMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        Map<Long, String> groupMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        queryUserInfo(tenantId, executeUserIds, userMap, groupMap);
        Set<Long> workOrderIds = workOrderList.stream().map(EntityWithName::getId).collect(Collectors.toSet());
        List<ProcessFlowUnit> processFlowUnits = workOrderDao.queryProcessFlowUnit(workOrderIds);
        Map<String, UserTaskConfig> taskConfigMap = workOrderDao.queryTaskConfigList(workOrderIds, null);
        Map<Long, List<ProcessFlowUnit>> processFlowUnitMap = processFlowUnits.stream().collect(Collectors.groupingBy(ProcessFlowUnit::getEventid));
        Map<Integer, String> workStatusMap = workSheetStatusUtils.getWorkSheetStatusMapByTaskType(WorkSheetTaskType.REPAIR);
        Map<Integer, String> fillFormTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.FILL_FORM_TYPE);
        Map<Integer, String> taskLevelMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORKSHEET_TASK_LEVEL);
        Map<Integer, String> taskTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORK_SHEET_TASK_TYPE);
        Map<Integer, String> repairTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.REPAIR_TYPE);
        Map<Integer, String> sourceTypeMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORK_ORDER_SOURCE_TYPE);

        List<DeviceClassification> deviceClassifications = expertService.queryData(ModelLabelDef.DEVICE_CLASSIFICATION, projectId, DeviceClassification.class);
        Map<Long, String> deviceClassificationMap = deviceClassifications.stream().collect(Collectors.toMap(ClassifiedBaseVo::getId, ClassifiedBaseVo::getName));
        List<DeviceClassification> eventClassifications = expertService.queryData(ModelLabelDef.EVENT_CLASSIFICATION, projectId, DeviceClassification.class);
        Map<Long, String> eventClassificationMap = eventClassifications.stream().collect(Collectors.toMap(ClassifiedBaseVo::getId, ClassifiedBaseVo::getName));
        List<FaultScenarios> faultScenarios = modelServiceUtils.query(faultIds, ModelLabelDef.FAULT_SCENARIOS, FaultScenarios.class);
        Map<Long, String> faultScenariosMap = faultScenarios.stream().collect(Collectors.toMap(FaultScenarios::getId, FaultScenarios::getName));
        List<EventPlan> eventPlans = modelServiceUtils.query(eventPlanIds, ModelLabelDef.EVENT_PLAN, EventPlan.class);
        Map<Long, EventPlan> eventPlanMap = eventPlans.stream().collect(Collectors.toMap(EventPlan::getId, it -> it));

        for (WorkOrderVo dto : workOrderList) {
            dto.setTeamName(groupMap.get(dto.getTeamId()));
            dto.setInspectTeamName(groupMap.get(dto.getInspectTeamId()));
            dto.setStaffName(userMap.get(dto.getStaff()));
            dto.setCreatorName(userMap.get(dto.getCreator()));
            dto.setWorkSheetStatusName(workStatusMap.get(dto.getWorkSheetStatus()));
            dto.setTaskLevelName(taskLevelMap.get(dto.getTaskLevel()));
            dto.setTaskTypeName(taskTypeMap.get(dto.getTaskType()));
            dto.setRepairTypeName(repairTypeMap.get(dto.getRepairType()));
            dto.setProcessFlowUnits(processFlowUnitMap.get(dto.getId()));
            dto.setDeviceClassificationName(deviceClassificationMap.get(dto.getDeviceClassificationId()));
            dto.setEventClassificationName(eventClassificationMap.get(dto.getEventClassificationId()));
            dto.setFaultScenariosName(faultScenariosMap.get(dto.getFaultScenariosId()));
            dto.setSourceTypeName(sourceTypeMap.get(dto.getSourceType()));
            handleUserTaskConfig(user, taskConfigMap, dto);
            dto.setFillFormTypeName(fillFormTypeMap.get(dto.getFillFormType()));
            MaintenanceContent maintenanceContent = dto.getMaintenanceContentObj();
            if (maintenanceContent != null && maintenanceContent.getEventPlan() != null) {
                dto.setEventPlan(eventPlanMap.get(maintenanceContent.getEventPlan().getEventPlanId()));
            }

            // 匹配巡检对象名称
            List<DevicePlanRelationship> devicePlanRelationshipList = dto.getDevicePlanRelationshipList();
            if (CollectionUtils.isEmpty(devicePlanRelationshipList)) {
                continue;
            }
            for (DevicePlanRelationship devicePlanRelationship : devicePlanRelationshipList) {
                devicePlanRelationship.setDeviceName(nodeMap.get(new BaseVo(devicePlanRelationship.getDeviceId(), devicePlanRelationship.getDeviceLabel())));
            }
            if (Objects.nonNull(dto.getSourceIndex())) {
                RepairSourceIndexVo repairSourceIndexVo = JsonTransferUtils.parseString(dto.getSourceIndex(), RepairSourceIndexVo.class);
                dto.setRepairSourceIndexVo(repairSourceIndexVo);
            }
        }
    }

    /**
     * 提取数据
     *
     * @param workOrderList
     * @param tmpNodes
     * @param executeUserIds
     * @param deviceIds
     * @param faultIds
     * @param eventIds
     * @param eventPlanIds
     */
    private void resolveData(List<WorkOrderVo> workOrderList, Set<BaseVo> tmpNodes, Set<Long> executeUserIds,
                             Set<Long> deviceIds, Set<Long> faultIds, Set<Long> eventIds, Set<Long> eventPlanIds) {
        for (WorkOrderVo workOrder : workOrderList) {
            if (workOrder.getDeviceClassificationId() != null) {
                deviceIds.add(workOrder.getDeviceClassificationId());
            }

            if (workOrder.getFaultScenariosId() != null) {
                faultIds.add(workOrder.getFaultScenariosId());
            }

            if (workOrder.getEventClassificationId() != null) {
                eventIds.add(workOrder.getEventClassificationId());
            }

            MaintenanceContent maintenanceContentObj = workOrder.getMaintenanceContentObj();
            if (maintenanceContentObj != null && maintenanceContentObj.getEventPlan() != null) {
                Long eventPlanId = workOrder.getMaintenanceContentObj().getEventPlan().getEventPlanId();
                if (eventPlanId != null) {
                    eventPlanIds.add(eventPlanId);
                }
            }

            // 巡检对象
            List<DevicePlanRelationship> ships = workOrder.getDevicePlanRelationshipList();
            if (CollectionUtils.isNotEmpty(ships)) {
                for (DevicePlanRelationship ship : ships) {
                    tmpNodes.add(new BaseVo(ship.getDeviceId(), ship.getDeviceLabel()));
                }
            }

            // 巡单人员
            if (ParamUtils.checkPrimaryKeyValid(workOrder.getStaff())) {
                executeUserIds.add(workOrder.getStaff());
            }

            // 创建工单
            if (ParamUtils.checkPrimaryKeyValid(workOrder.getCreator())) {
                executeUserIds.add(workOrder.getCreator());
            }
        }
    }

    /**
     * 查询用户信息
     *
     * @param tenantId
     * @param executeUserIds
     * @param userMap
     * @param groupMap
     */
    private void queryUserInfo(Long tenantId, Set<Long> executeUserIds, Map<Long, String> userMap, Map<Long, String> groupMap) {
        authUtils.getUserAndGroupMsg(tenantId, userMap, groupMap);

        for (Long executeUserId : executeUserIds) {
            if (userMap.containsKey(executeUserId)) {
                continue;
            }

            UserVo userVo = authUtils.queryUser(executeUserId);
            if (userVo != null) {
                userMap.put(userVo.getId(), userVo.getName());
            }
        }
    }

    @Override
    public WorkOrderExportVo queryWorkOrder(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ValidationException("工单id不合法!");
        }

        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        WorkOrderVo workOrder = workOrderDao.queryWorkOrder(code, WorkOrderVo.class);
        if (workOrder == null) {
            return null;
        }

        assemblyWholeWorkOrderList(Collections.singletonList(workOrder), user.getTenantId(), user);
        WorkOrderExportVo exportVo = new WorkOrderExportVo();
        BeanUtils.copyProperties(workOrder, exportVo);
        List<EventPlan> eventPlans = expertService.queryEventPlan(workOrder.getFaultScenariosId(), 3);
        if (CollectionUtils.isNotEmpty(eventPlans)) {
            int num = 1;
            for (EventPlan eventPlan : eventPlans) {
                eventPlan.setNumber(num++);
            }
        }
        exportVo.setEventPlans(eventPlans);
        return exportVo;
    }

    @Override
    public void exportWorkOrder(String code) {
        WorkOrderVo workOrder = queryWorkOrder(code);
        WorkOrderExportVo exportVo = new WorkOrderExportVo();
        BeanUtils.copyProperties(workOrder, exportVo);
        List<EventPlan> eventPlans = expertService.queryEventPlan(workOrder.getFaultScenariosId(), 3);
        if (CollectionUtils.isNotEmpty(eventPlans)) {
            int num = 1;
            for (EventPlan eventPlan : eventPlans) {
                eventPlan.setNumber(num++);
            }
        }
        exportVo.setEventPlans(eventPlans);
        exportExcel("维修工单导出模板.xlsx", response, (Map<String, Object>) JsonTransferUtils.parseObject(exportVo, Map.class), String.format("维修工单（%s）", workOrder.getCode()));
    }

    private void exportExcel(String templateName, HttpServletResponse response, Map<String, Object> data, String fileName) {
        try (InputStream iStream = this.getClass().getResourceAsStream("/templates/" + templateName)) {
            if (iStream == null) {
                throw new CommonException("未找到模板");
            }

            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setContentType(ContentTypeDef.APPLICATION_MSEXCEL + ";charset=utf-8");
            ExcelUtils.exportExcel(iStream, response.getOutputStream(), data);
        } catch (Exception e) {
            throw new CommonException(e.getMessage());
        }
    }
}






