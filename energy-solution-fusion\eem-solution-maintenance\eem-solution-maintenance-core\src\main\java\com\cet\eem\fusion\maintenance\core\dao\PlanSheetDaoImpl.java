﻿package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.entity.Result;
import com.cet.eem.fusion.common.modelutils.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.SubConditionBuilder;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.fusion.maintenance.core.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.maintenance.core.entity.bo.PlanSheetWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.model.plan.QueryInspectionPlanRequest;
import com.cet.eem.fusion.maintenance.core.model.plan.QueryMaintenancePlanRequest;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * @ClassName : PlanSheetDaoImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 13:46
 */
@Repository
public class PlanSheetDaoImpl extends ModelDaoImpl<PlanSheet> implements PlanSheetDao {
    public static final String PROJECT_ID = "project_id";
    public static final String DELETED = "deleted";
    public static final String WORKSHEET_TYPE = "worksheettype";
    public static final String ENABLED = "enabled";
    public static final String TEAM_ID = "teamid";
    @Autowired
    private ModelServiceUtils modelServiceUtils;

    @Override
    public ApiResult<List<PlanSheetWithSubLayer>> queryInspectionPlanSheetSubLayerWithPage(QueryInspectionPlanRequest queryInspectionPlanRequest) {
        ParentQueryConditionBuilder parentQueryConditionBuilder = ParentQueryConditionBuilder.of(ModelLabelDef.PLAN_SHEET);
        parentQueryConditionBuilder.where(PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId(), 1);
        parentQueryConditionBuilder.where(DELETED, ConditionBlock.OPERATOR_EQ, false, 2);
        parentQueryConditionBuilder.where(WORKSHEET_TYPE, ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.INSPECTION, 3);
        if (Objects.nonNull(queryInspectionPlanRequest.getEnabled())) {
            parentQueryConditionBuilder.where(ENABLED, ConditionBlock.OPERATOR_EQ, queryInspectionPlanRequest.getEnabled(), 4);
        }
        if (StringUtils.isNotEmpty(queryInspectionPlanRequest.getName())) {
            parentQueryConditionBuilder.where(ColumnDef.NAME, ConditionBlock.OPERATOR_LIKE, queryInspectionPlanRequest.getName(), 5);
        }
        if (Objects.nonNull(queryInspectionPlanRequest.getTeamId())) {
            parentQueryConditionBuilder.where(TEAM_ID, ConditionBlock.OPERATOR_EQ, queryInspectionPlanRequest.getTeamId(), 6);
        }
        if (BooleanUtils.isTrue(queryInspectionPlanRequest.isHide())) {
            parentQueryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_EQ, null, 7);
            parentQueryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_GT, System.currentTimeMillis(), 7);
        }
        parentQueryConditionBuilder.selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));
        parentQueryConditionBuilder.limit(queryInspectionPlanRequest.getPage());
        ApiResult<List<Map<String, Object>>> query = this.eemModelDataService.query(parentQueryConditionBuilder.build());
        return Result.ok(JsonTransferUtils.parseList(query.getData(), PlanSheetWithSubLayer.class), query.getTotal());
    }

    @Override
    public ApiResult<List<PlanSheetWithSubLayer>> queryMaintenancePlanSheetSubLayerWithPage(QueryMaintenancePlanRequest queryMaintenancePlanRequest) {
        ParentQueryConditionBuilder<PlanSheet> parentQueryConditionBuilder = ParentQueryConditionBuilder.of(ModelLabelDef.PLAN_SHEET);
        int group = 1;
        parentQueryConditionBuilder.where(PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId(), group++);
        parentQueryConditionBuilder.where(DELETED, ConditionBlock.OPERATOR_EQ, false, group++);
        parentQueryConditionBuilder.where(WORKSHEET_TYPE, ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.MAINTENANCE, group++);
        if (Objects.nonNull(queryMaintenancePlanRequest.getWorksheetTaskLevel())) {
            parentQueryConditionBuilder.where("worksheettasklevel", ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getWorksheetTaskLevel(), group++);
        }
        if (Objects.nonNull(queryMaintenancePlanRequest.getEnabled())) {
            parentQueryConditionBuilder.where(ENABLED, ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getEnabled(), group++);
        }
        if (StringUtils.isNotEmpty(queryMaintenancePlanRequest.getName())) {
            parentQueryConditionBuilder.where(ColumnDef.NAME, ConditionBlock.OPERATOR_LIKE, queryMaintenancePlanRequest.getName(), group++);
        }
        if (Objects.nonNull(queryMaintenancePlanRequest.getTeamId())) {
            parentQueryConditionBuilder.where(TEAM_ID, ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getTeamId(), group++);
        }
        if (BooleanUtils.isTrue(queryMaintenancePlanRequest.isHide())) {
            parentQueryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_EQ, null, group);
            parentQueryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_GT, System.currentTimeMillis(), group);
        }
        parentQueryConditionBuilder.selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));
        parentQueryConditionBuilder.limit(queryMaintenancePlanRequest.getPage());
        ApiResult<List<Map<String, Object>>> query = this.eemModelDataService.query(parentQueryConditionBuilder.build());
        return Result.ok(JsonTransferUtils.parseList(query.getData(), PlanSheetWithSubLayer.class), query.getTotal());
    }

    @Override
    public List<PlanSheet> queryUnFinishedPlan() {
        ParentQueryConditionBuilder<PlanSheet> parentQueryConditionBuilder = ParentQueryConditionBuilder.of(ModelLabelDef.PLAN_SHEET);
        parentQueryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_EQ, null, 2);
        parentQueryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_GT, System.currentTimeMillis(), 2);
        parentQueryConditionBuilder.where(ColumnDef.DELETED, ConditionBlock.OPERATOR_EQ, false, 3);
        ApiResult<List<Map<String, Object>>> query = this.eemModelDataService.query(parentQueryConditionBuilder.build());
        return JsonTransferUtils.parseList(query.getData(), PlanSheet.class);
    }

    @Override
    public void deletePlanSheets(List<Long> ids) {
        modelServiceUtils.delete(ModelLabelDef.PLAN_SHEET, ids);
    }

    @Override
    public List<PlanSheetWithSubLayer> queryMaintenancePlanSheetByStrategyType(Integer strategyType) {
        QueryCondition condition = ParentQueryConditionBuilder.of(ModelLabelDef.PLAN_SHEET)
                .eq(ColumnDef.FINISH_TIME, null, 1)
                .gt(ColumnDef.FINISH_TIME, null, 1)
                .eq(ColumnDef.EXECUTE_STRATEGY, strategyType, 2)
                .eq(ColumnDef.DELETED, false, 3)
                .eq(ColumnDef.WORK_SHEET_TYPE, WorkSheetTaskType.MAINTENANCE)
                .leftJoin(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP)
                .build();

        return modelServiceUtils.query(condition, PlanSheetWithSubLayer.class);
    }

    @Override
    public ApiResult<List<PlanSheetWithSubLayer>> queryMaintenancePlanBySchedule(QueryMaintenancePlanRequest queryMaintenancePlanRequest) {
        ParentQueryConditionBuilder parentQueryConditionBuilder = ParentQueryConditionBuilder.of(ModelLabelDef.PLAN_SHEET);
        parentQueryConditionBuilder.where(PROJECT_ID, ConditionBlock.OPERATOR_EQ, 1L, 1);
        parentQueryConditionBuilder.where(DELETED, ConditionBlock.OPERATOR_EQ, false, 2);
        parentQueryConditionBuilder.where(WORKSHEET_TYPE, ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.MAINTENANCE, 3);
        if (Objects.nonNull(queryMaintenancePlanRequest.getWorksheetTaskLevel())) {
            parentQueryConditionBuilder.where("worksheettasklevel", ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getWorksheetTaskLevel(), 4);
        }
        if (Objects.nonNull(queryMaintenancePlanRequest.getEnabled())) {
            parentQueryConditionBuilder.where(ENABLED, ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getEnabled(), 4);
        }
        if (StringUtils.isNotEmpty(queryMaintenancePlanRequest.getName())) {
            parentQueryConditionBuilder.where(ColumnDef.NAME, ConditionBlock.OPERATOR_LIKE, queryMaintenancePlanRequest.getName(), 5);
        }
        if (Objects.nonNull(queryMaintenancePlanRequest.getTeamId())) {
            parentQueryConditionBuilder.where(TEAM_ID, ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getTeamId(), 6);
        }
        if (BooleanUtils.isTrue(queryMaintenancePlanRequest.isHide())) {
            parentQueryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_EQ, null, 7);
            parentQueryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_GT, System.currentTimeMillis(), 7);
        }
        parentQueryConditionBuilder.selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));
        parentQueryConditionBuilder.limit(queryMaintenancePlanRequest.getPage());
        ApiResult<List<Map<String, Object>>> query = this.eemModelDataService.query(parentQueryConditionBuilder.build());
        return Result.ok(JsonTransferUtils.parseList(query.getData(), PlanSheetWithSubLayer.class), query.getTotal());

    }

    @Override
    public List<PlanSheetWithSubLayer> queryPlanSheet(Long projectId) {
        SubConditionBuilder subConditionBuilder = new SubConditionBuilder(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP);
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(ModelLabelDef.PLAN_SHEET).distinct()
                .leftJoin(subConditionBuilder.build());
        builder.where("project_id", ConditionBlock.OPERATOR_EQ, projectId);
        builder.where("worksheettype", ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.INSPECTION);
        return modelServiceUtils.query(builder, PlanSheetWithSubLayer.class);
    }
}



