﻿package com.cet.eem.fusion.maintenance.core.service.repair.app;

import com.cet.eem.fusion.maintenance.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderDao;
import com.cet.eem.fusion.maintenance.core.dao.repair.RepairWorkOrderDao;
import com.cet.eem.fusion.maintenance.core.def.WorkSheetStatusDef;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.service.repair.RepairWorkOrderService;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/3
 */
@Service
public class RepairWorkOrderMobileServiceImpl implements RepairWorkOrderMobileService {
    @Autowired
    WorkOrderDao workOrderDao;

    @Autowired
    RepairWorkOrderDao repairWorkOrderDao;

    @Autowired
    RepairWorkOrderService repairWorkOrderService;

    @Value("${cet.eem.work-order.repair.app.summary-query-pre-day:1}")
    private Integer preDay;

    @Override
    public <T extends InspectionWorkOrderDto> List<T> queryWorkOrder(UserVo user, Long teamId, boolean inspectUser, Class<T> clazz) {
        // 待处理、异常和退回的一直显示。超时和已完成的，默认显示一天或者一周，此处取24（或者一周，此处做成配置项）内

        // 查询超时和已完成
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime st = now.plusDays(-1L * preDay);
        // 查询提前指定时间的工单信息
        List<Integer> statuses = Collections.singletonList(WorkSheetStatusDef.ACCOMPLISHED);
        ApiResult<List<Map<String, Object>>> workOrderResult = repairWorkOrderDao.queryWorkOrderByWorkStatus(st, now, WorkSheetTaskType.REPAIR, statuses, teamId, user.getId(), inspectUser);
        List<T> workOrderList = JsonTransferUtils.transferList(workOrderResult.getData(), clazz);

        // 查询待处理、异常、退回工单
        statuses = Arrays.asList(WorkSheetStatusDef.ALREADY_SENT, WorkSheetStatusDef.TO_BE_SENT, WorkSheetStatusDef.AUDITED, WorkSheetStatusDef.TO_BE_AUDITED);
        LocalDateTime et = TimeUtil.getFirstTimeOfNextDay(now.toLocalDate());
        workOrderList.addAll(repairWorkOrderDao.queryRuntimeWorkOrderByWorkStatus(statuses, WorkSheetTaskType.REPAIR, teamId, et, user.getId(), inspectUser, clazz));

        return workOrderList;
    }
}



