package com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet;

import com.cet.eem.fusion.maintenance.core.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.model.WorksheetAbnormalReasonDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.OperationUser;
import com.cet.eem.fusion.common.model.BaseVo;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : InspectRecordSheetDto
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-10 16:14
 */
@Getter
@Setter
public class InspectRecordSheetDto {
    @JsonProperty(WorkOrderDef.WORKSHEET_ABNORMAL_REASON_MODEL)
    private List<WorksheetAbnormalReasonDto> abnormalReasonList;
    /**
     * 巡检
     */
    @JsonProperty(WorkOrderDef.MAINTENANCE_CONTENT)
    protected String maintenanceContent;

    private List<InspectionSchemeDetail> inspectParams;

    @ApiModelProperty("操作用户信息")
    private List<OperationUser> users;

    private Long teamId;

    private String teamName;

    private String reason;
    private String userList;
    @JsonProperty(WorkOrderDef.FINISH_TIME)
    private LocalDateTime finishTime;
    private BaseVo baseVo;
}

