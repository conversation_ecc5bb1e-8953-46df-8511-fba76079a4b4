package com.cet.eem.fusion.maintenance.core.common.definition;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Field definition annotation
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldDef {
    String value() default "";
    String name() default "";
    String comment() default "";
    boolean required() default false;
    int length() default 0;
}